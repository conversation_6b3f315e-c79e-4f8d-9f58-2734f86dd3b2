function arama_goster() {
$("#div_arama").slideToggle("slow");
}

 
function ekle(){ 
$('#ekleniyor').html('<center><img src="resimler/loadingAnimation.gif"></center>');
$.ajax({
	type: "POST",
	url: "ekle2.php",
	data: $('form').serialize(),
	success: function(msg){
	if (msg=="ok")
		{
		$('#ekleniyor').html("<font color='yellow'>Kayıt Eklendi!</font>");
		setTimeout("window.location = 'index.php?islem=giris';",1000);
		}
	else
		{$('#ekleniyor').html( msg );}
   }
});
}


function kopyala(){ 
$('#kopyalaniyor').html('<center><img src="resimler/loadingAnimation.gif"></center>');
$.ajax({
	type: "POST",
	url: "kopyala2.php",
	data: $('form').serialize(),
	success: function(msg){
	if (msg=="ok")
		{
		$('#kopyalaniyor').html("<font color='yellow'>Kopyadan Kayıt Eklendi!</font>");
		setTimeout("window.location = 'index.php?islem=giris';",1000);
		}
	else
		{$('#kopyalaniyor').html( msg );}
   }
});
}


function birim_ekle(){ 
$('#ekleniyor').html('<center><img src="resimler/loadingAnimation.gif"></center>');
$.ajax({
	type: "POST",
	url: "birim_ekle2.php",
	data: $('form').serialize(),
	success: function(msg){
	if (msg=="ok")
		{
		$('#ekleniyor').html("<font color='yellow'>Kayıt Eklendi!</font>");
		setTimeout("window.location = 'index.php?islem=giris';",1000);
		}
	else
		{$('#ekleniyor').html( msg );}
   }
});
}


function duzenle(no){ 
$('#duzenleniyor').html('<center><img src="resimler/loadingAnimation.gif"></center>');
$.ajax({
	type: "POST",
	url: "duzenle2.php",
	data: $('form').serialize(),
	success: function(msg){
	if (msg=="ok")
		{
		$('#duzenleniyor').html("<font color='yellow'><b>Kayıt Düzenlendi!</b></font>");
		setTimeout("window.location = 'index.php?islem=giris';",1000);
		}
	else
		{$('#duzenleniyor').html( msg );}
   }
});
}


function birim_duzenle(no){ 
$('#duzenleniyor').html('<center><img src="resimler/loadingAnimation.gif"></center>');
$.ajax({
	type: "POST",
	url: "birim_duzenle2.php",
	data: $('form').serialize(),
	success: function(msg){
	if (msg=="ok")
		{
		$('#duzenleniyor').html("<font color='yellow'><b>Kayıt Düzenlendi!</b></font>");
		setTimeout("window.location = 'index.php?islem=giris';",1000);
		}
	else
		{$('#duzenleniyor').html( msg );}
   }
});
}


function sil_1(no){ 

var sc = $("input[@type=hidden]").serialize();

$('#siliniyor').html('<center><img src="resimler/loadingAnimation.gif"></center>');
$.ajax({
	type: "POST",
	url: "sil_2.php",
	data: sc,
	success: function(msg){
	if (msg=="ok")
		{
		$('#siliniyor').html("<b><font color='orange'>Bu kayıt silindi!</font></b>");
		setTimeout("window.location = 'index.php?islem=giris';",500);
		}
	else
		{$('#siliniyor').html( msg );}
   }
});
}


function sil( no )
{
	if( confirm('Bu Kaydı silmek istediğinizden emin misiniz?') ) {
          $.ajax({
            type: "POST",
            url: "sil.php",
            data: encodeURIComponent(no),
            success: function(msg)
			{
            	if( msg === 'ok' )
				{
					$('tr#'+no).hide();
				}
				else 
				{
					alert("Silme işleminde problem çıktı!"); 
				}
			}
		});
	}
}