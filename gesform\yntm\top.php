<?php

$veri_lower	= $_POST['veri'];
$veri_lower = tr_strtolower_en($veri_lower);

$kosul=" WHERE (vekil_adi LIKE '%".$veri_lower."%' OR vekil_eposta LIKE '%".$veri_lower."%' OR vekil_tel_no LIKE '%".$veri_lower."%' OR sahip_adi LIKE '%".$veri_lower."%' OR sahip_eposta LIKE '%".$veri_lower."%' OR sahip_tel_no LIKE '%".$veri_lower."%' OR proje_info_proje_adi LIKE '%".$veri_lower."%' OR keywords LIKE '%".$veri_lower."%')";
$sql = "select * from ".TABLO." ".$kosul."";
$sql_user = "select * from ".TABLO_USER." WHERE kullanici_adi='".$_SESSION['frm_user']."'";
$result_user = mysqli_query($con, $sql_user);
$row_user = mysqli_fetch_array($result_user);

 

if ($_GET['tp'] == 'evet')
{
	$class_menu = "class=\"menuler_red\"";
}else{
	$class_menu = "class=\"menuler\"";
}

$tum_sorgu = mysqli_query($con, "select count(id) from ".TABLO."");
$say_tum_toplam = mysqli_fetch_array($tum_sorgu);
$say_ara = mysqli_num_rows(mysqli_query($con, $sql));

$xls_link = "xls.php?veri=".$veri_lower;

$url = 'https://'.$_SERVER['SERVER_NAME'].$_SERVER['REQUEST_URI'];
?>
 
<br><br><br><br>


<!-- ÜST BUTON ve ARAMA FORMU -->

<div class="div_arama">
<TABLE border="0px" width="100%" align="center">
<TR>
	<TD rowspan="2">
	<nobr>
		<a href="index.php" class="tooltips">
		<img src="resimler/osb_logo_Ges.png"/>
		</a>
		&nbsp;&nbsp;&nbsp;&nbsp;
	</nobr>
	</TD>



	<TD width="20%" align="right">
	<nobr>
		<a href="index.php" class="tooltips">
		<img border="0" id='home' src="resimler/home.png" height="35"
		onmouseover = 'document.getElementById("home").src = "resimler/home.png"'
		onmouseout =  'document.getElementById("home").src = "resimler/home.png"'>
		<span style="width:120px; margin-left:-60px;">Ana Sayfaya Git</span>
		</a>

		<a href="<?=$xls_link?>" class="tooltips">
		<img border="0" id='excel' src="resimler/excelb.png" height="35"
		onmouseover = 'document.getElementById("excel").src = "resimler/excela.png"'
		onmouseout =  'document.getElementById("excel").src = "resimler/excelb.png"'>
		<span style="min-width:100px;; margin-left:-60px;">Excel'e Aktar1</span>
		</a>

		<a href="#" class="tooltips" onclick="location.href='index.php?logout=true'">
		<img border="0" id="pass" src="resimler/exit.png" height="35">
		<span style="width:120px; margin-left:-60px;">ÇIKIŞ</span>
		</a>
	</nobr>
	</TD>
	
	<TD width="20%" align="left">
	<nobr>
		<form class="form-wrapper cf" action="<?=$url?>" name="arayacakform" method="post" >							
		<?php if ($_POST['veri'] == ''){ ?>
		<input type="text" name="veri" id="veri" onclick="javascript:this.value=''" value="Aranacak Metin..." required>
		<?php }else{ ?>
		<input type="text" id="veri" name="veri" onclick="javascript:this.value=''" value="<?=$_POST['veri']?>" required>
		<?php } ?> 
		<button type="submit">ARA</button>
		</form>
	</nobr>
	</TD>

</TR>


<TR>	
	<TD colspan="2" align="right">	  
	<div id="div_top">
		<TABLE class="table_info" border="0">
		<TR>


			<TD class="tooltips_td">
				<a href="edit_profile.php?id=<?=$row_user['id']?>&height=205&width=415" class="thickbox tooltips" Title="<table><tr height='11px'><td style='padding:0px;'><img src='resimler/duzenle.png' height='22px'/></td><td>Profilini Düzenle</td></tr></table>">
					&nbsp;&nbsp;&nbsp;&nbsp;<?=$row_user['isim']." ".$row_user['soyisim']." - ".$ip?>&nbsp;&nbsp;&nbsp;&nbsp;
				</a>
			</TD>
			
			<TD class="tooltips_td">
				<span style="width:300px; margin-left:-150px;">Tüm kullanıcıların açtığı toplam kayıt sayısıdır.</span>
				<font color="white" style="font-weight:normal; font-size:10px">G.TOPLAM :</font> <font color="yellow" style="font-weight:normal; font-size:13px"><?=$say_tum_toplam[0]?></font>
			</TD>
	
			<TD align="right" width="200" class="tooltips_td">
			
<?php if ($veri_lower != ''){ ?>
				
						<?=$say_ara?><font color="white" style="font-weight:normal; font-size:10px">&nbsp;adet eşleşme..</font>

<?php }else{ ?>	
		
						&nbsp;<font color="white" style="font-weight:normal; font-size:10px">Tüm liste gösteriliyor..</font>

<?php } ?>
			</TD>
		</TR>
		</TABLE> 
	</div>
	</TD>
</TR>
</TABLE>
</div>

