<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="931" height="476" viewBox="0 0 931 476">
    <defs>
        <linearGradient id="z32ybi21mc" x1="50%" x2="50%" y1="0%" y2="100%">
            <stop offset="0%" stop-opacity="0"/>
            <stop offset="100%" stop-color="#173A47"/>
        </linearGradient>
        <filter id="qxtemauy2a" width="114.4%" height="130.8%" x="-7.2%" y="-13.1%" filterUnits="objectBoundingBox">
            <feMorphology in="SourceAlpha" operator="dilate" radius="4.5" result="shadowSpreadOuter1"/>
            <feOffset dy="9" in="shadowSpreadOuter1" result="shadowOffsetOuter1"/>
            <feGaussianBlur in="shadowOffsetOuter1" result="shadowBlurOuter1" stdDeviation="14.5"/>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"/>
            <feColorMatrix in="shadowBlurOuter1" values="0 0 0 0 0.203549592 0 0 0 0 0.203549592 0 0 0 0 0.203549592 0 0 0 0.1 0"/>
        </filter>
        <rect id="eniafdyvqb" width="855" height="400" x="0" y="0" rx="6"/>
    </defs>
    <g fill="none" fill-rule="evenodd" opacity=".704">
        <g>
            <g>
                <g transform="translate(-65 -378) translate(83 195) translate(20 212)">
                    <use fill="#000" filter="url(#qxtemauy2a)" xlink:href="#eniafdyvqb"/>
                    <use fill="url(#z32ybi21mc)" fill-opacity=".6" xlink:href="#eniafdyvqb"/>
                </g>
            </g>
        </g>
    </g>
</svg>
