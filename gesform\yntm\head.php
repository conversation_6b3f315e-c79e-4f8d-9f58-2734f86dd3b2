<!doctype html>
<html lang="tr">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<title>GES-LES PANEL</title>
<link rel="icon" href="resimler/favicon.png" />
<!-- META -->
<?php
$url = 'http://'.$_SERVER['SERVER_NAME'].$_SERVER['REQUEST_URI'];
?>
	
<!-- CSS -->
<link rel="stylesheet" href="css/style.css" type="text/css" />
<link rel="stylesheet" href="css/button.css" type="text/css" />
<link rel="stylesheet" href="css/thickbox.css" type="text/css" media="screen" />
<link rel="stylesheet" href="css/baslik.css" type="text/css" media="screen" />



<!-- JS -->
<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.9.1/jquery.min.js"></script>
<script type="text/javascript" src="js/jquery.js"></script>
<script type="text/javascript" src="js/thickbox.js"></script>
<script type="text/javascript" src="js/ges.js"></script>

<script>
var Saniye = 00; // set the Saniyeonds
var Dakika = 02; // set the Dakikautes

function countDown() {
Saniye--;
if (Saniye == -01) {
Saniye = 59;
Dakika = Dakika - 1;
} else {
Dakika = Dakika;
}
if (Saniye<=9) { Saniye = "0" + Saniye; }
time = (Dakika<=9 ? "0" + Dakika : Dakika) + " Dakika " + Saniye + " Saniye ";
if (document.getElementById) { theTime.innerHTML = time; }
SD=window.setTimeout("countDown();", 1000);
if (Dakika == '00' && Saniye == '00') { Saniye = "00"; window.clearTimeout(SD); }
}

function addLoadEvent(func) {
var oldonload = window.onload;
if (typeof window.onload != 'function') {
window.onload = func;
} else {
window.onload = function() {
if (oldonload) {
oldonload();
}
func();
}
}
}

addLoadEvent(function() {
countDown();
});
</script>

<style>
.timeClass {
font-family:arial,verdana,helvetica,sans-serif;
font-weight:bold;
font-size:10pt;
color:orange
}
</style>

</head>
<body>
<div class="center"></div>
<center>
