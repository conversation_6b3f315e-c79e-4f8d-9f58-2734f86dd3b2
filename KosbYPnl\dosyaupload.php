<?php
require 'class.upload.php';

$dir_pics = (isset($_GET['pics']) ? $_GET['pics'] : $dir_dest);

$log = '';

use Verot\Upload\Upload;

if (isset($_FILES['dosya'])) {

    $files = array();
    foreach ($_FILES['dosya'] as $k => $l) {
        foreach ($l as $i => $v) {
            if (!array_key_exists($i, $files)) {
                $files[$i] = array();
            }
            $files[$i][$k] = $v;
        }
    }

    session_start(); // session_start() döngü dışında

    foreach ($files as $file) {
        $handle = new Upload($file);
        if ($handle->uploaded) {
            $handle->file_max_size = $dosyaboyutu * 1000000;
            $handle->allowed = array('application/*', 'image/*');
            $handle->process('../' . $yol);
    
            if ($handle->processed) {
                // Orijinal dosya adı ve uzantıyı al
                $original_name = pathinfo($handle->file_dst_name, PATHINFO_FILENAME);
                $extension = pathinfo($handle->file_dst_name, PATHINFO_EXTENSION);
    
                // SEO-friendly dosya adı oluştur
                $isim = $original_name . '.' . $extension;
                $seoluisim = SEOdosya($isim); // SEO-friendly dosya adı
                $full_path = "../$yol/$seoluisim"; // SEO dosya adıyla tam yol
    
                // Dosya var mı kontrol et, varsa '_1', '_2' ekle
                $counter = 1;
                while (file_exists($full_path)) {
                    $isim = $original_name . '_' . $counter . '.' . $extension;
                    $seoluisim = SEOdosya($isim);
                    $full_path = "../$yol/$seoluisim";
                    $counter++;
                }
    
                // Dosyayı unique SEO-friendly isim ile işle
                $boyut = $handle->file_src_size;

                // Yeni dosya için sıra numarası hesapla
                $sirasor = $db->prepare("SELECT COALESCE(MAX(menu_sira), 0) + 1 as yeni_sira FROM dokuman WHERE menu_id = ? AND sayfatip = ?");
                $sirasor->execute(array($menuid, $sayfatip));
                $yeni_sira = $sirasor->fetch(PDO::FETCH_ASSOC)['yeni_sira'];

                $dosyaekle = $db->prepare("INSERT INTO dokuman (menu_id, sayfatip, baslik, link, boyut, yaziicidosya, menu_sira) VALUES (?, ?, ?, ?, ?, ?, ?)");
                $dosyaekle->execute(array($menuid, $sayfatip, $seoluisim, $yol . $seoluisim, $boyut, $yaziicidosya, $yeni_sira));
    
                // Dosyayı SEO-friendly isimle yeniden adlandır
                rename("../$yol/{$handle->file_dst_name}", $full_path);
    
                $_SESSION['dokumanyuklendi'] = 1;
            } else {
                $_SESSION['desteklenmiyor'] = 1;
            }
        }
    
        $log .= $handle->log . '<br />';
    }
    
    header("Location:" . $yonlenurl);
    
}
