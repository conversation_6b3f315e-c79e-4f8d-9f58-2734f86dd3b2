<?php
header("Content-type: application/x-msdownload"); 
$bugun = date('d.m.Y');
include ("fonksiyon.php");

$name = 'SMS_Onay';
$file = $name."_".$bugun.".xls";



header("Content-Disposition: attachment; filename=\"$file\""); 
header("Pragma: no-cache"); 
header("Expires: 0"); 
readfile($file);


session_start();
$baslik = 'Kayseri OSB SMS Onay Listesi';
if ($_SESSION['frm_user'] != '')
{
	
	
	$kosul=" WHERE (isim_soyisim LIKE '%".$_GET['veri']."%' OR firma LIKE '%".$_GET['veri']."%' OR cep_tel LIKE '%".$_GET['veri']."%' OR sms_kod LIKE '%".$_GET['veri']."%' OR email LIKE '%".$_GET['veri']."%' OR email_link LIKE '%".$_GET['veri']."%' OR ip LIKE '%".$_GET['veri']."%')";
	$sql_say = "SELECT * FROM ".TABLO." ".$kosul."";
	$sql = "SELECT * FROM ".TABLO." ".$kosul." ORDER BY id DESC";
	$se_say = mysqli_num_rows(mysqli_query($con, $sql_say));
	$result = mysqli_query($con, $sql);

?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="content-language" content="tr" />
<meta name="language" content="tr-TR" />
</head>

<BODY>
<center>
<TABLE border="1" width="900px">
<TR>
	<TD colspan="10" valign="middle" align="center" bgcolor="#84C2F7">
	<h2><?=$baslik?></h2>
	</TD>
</TR>

<TR>
	<TD style="color:white; background-color:#262222;"> Sayı			</TD>
	<TD style="color:white; background-color:#262222;"> ID				</TD>
	<TD style="color:white; background-color:#262222;"> TARİH			</TD>
	<TD style="color:white; background-color:#262222;"> ADI - SOYADI	</TD>
	<TD style="color:white; background-color:#262222;"> FİRMA			</TD>
	<TD style="color:white; background-color:#262222;"> CEP-TEL			</TD>
	<TD style="color:white; background-color:#262222;"> SMS KOD			</TD>
	<TD style="color:white; background-color:#262222;"> EMAIL			</TD>
	<TD style="color:white; background-color:#262222;"> EMAIL LINK		</TD>
	<TD style="color:white; background-color:#262222;"> IP ADRESİ		</TD>
</TR>
<?php 
$say = 0;
while ($row = mysqli_fetch_array($result))
	{
		$say = $say + 1;
		$a++;		 
		if ($a % 2 == 0) { $still="bgcolor=\"#acdae6\"";} else { $still="bgcolor=\"#eef5f6\"";}
?>

<TR>
	<TD <?=$still?>>
		<?=$say?>
	</TD>
	<TD <?=$still?>>
		<?=$row['id']?>
	</TD>
	<TD <?=$still?>>
		<?=$row['tarih']?>
	</TD>
	<TD <?=$still?>>
		<b><?=$row['isim_soyisim']?></b>
	</TD>
	<TD <?=$still?>>		
		<?=$row['firma']?>
	</TD>
	<TD <?=$still?>>		
		<?=$row['cep_tel']?>
	</TD>			
	<TD <?=$still?>>
		<?=$row['sms_kod']?>
	</TD>	
	<TD <?=$still?>>
		<?=$row['email']?>
	</TD>
	<TD <?=$still?>>
		<?=$row['email_link']?>
	</TD>
	<TD <?=$still?>>
		<?=$row['ip']?>
	</TD>
</TR>

<?php
	}
?>

</TABLE>
</center>
</BODY>
</HTML>
<?php
	}else{
		echo "Yanlış iş yapma !!!";
	}

?>