<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>

<!--META-->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>GES-LES BAŞVURU</title>
<link rel="icon" href="resimler/favicon.png" />
<!--STYLESHEETS-->
<link href="css/login-style.css" rel="stylesheet" type="text/css" />

<!--SCRIPTS-->
<script type="text/javascript" src="http://ajax.googleapis.com/ajax/libs/jquery/1.2.6/jquery.min.js"></script>
<!--Slider-in icons-->
<script type="text/javascript">
$(document).ready(function() {
	$(".username").focus(function() {
		$(".user-icon").css("left","-48px");
	});
	$(".username").blur(function() {
		$(".user-icon").css("left","0px");
	});
	
	$(".password").focus(function() {
		$(".pass-icon").css("left","-48px");
	});
	$(".password").blur(function() {
		$(".pass-icon").css("left","0px");
	});
});
</script>

</head>
<body>
<!--WRAPPER-->
<div class="center"></div>
<div id="wrapper">

	<!--SLIDE-IN ICONS-->
    <div class="user-icon"></div>
    <div class="pass-icon"></div>
    <!--END SLIDE-IN ICONS-->

<?php
$kullanici = $_COOKIE["frm_user"];
if($kullanici != '')
{
	header("location:index.php");
}else{
?>

<!--LOGIN FORM-->
<form name="login" class="login-form" action="index.php" method="post">

	<!--HEADER-->
    <div class="header" align="center">
    <img src="resimler/osb_logo_155.png"/><br><br>
    GES-LES BAŞVURU
	<FONT Color="red" size="3">
	
<?php
	if($_SESSION['frm_user'] != '')
	{	
		if ($_SESSION['frm_user']) 
		{
			echo "<font style=\"color:red;\"><br>&nbsp; BAŞARILI</font>".$_SESSION['frm_user'];
		}else{
			print '<br><b>"'.$_SESSION['frm_user'].'"</b> Olarak Yetkiniz Yok';
			session_start();
			session_unset();
			session_destroy();
		}
	}else{
		if($_POST['frm_user'] == '' OR $_POST['frm_user'] == 'Kullanıcı Adınız')
		{		
			print "<br>&nbsp;";
		}else{
			if ($_POST['frm_user'])
			{
				print "<br>Hatalı Şifre Girdiniz.".$_SESSION['frm_user'];
			}else{
				print "<br>Kullanıcı Adı ya da Şifreniz Hatalı.";
			}
			
		}
	}
?>
	</FONT>
	</div>
    <!--END HEADER-->
	
	<!--CONTENT-->
    <div class="content">
	<!--USERNAME--><input type="text" id="user" class="input username" value="" NAME="frm_user" onfocus="this.value=''" placeholder="Kullanıcı Adınız"/><!--END USERNAME-->
    <!--PASSWORD--><input type="password" id="password" class="input password" value="" NAME="frm_passwd" onfocus="this.value=''" placeholder="&#8226;&#8226;&#8226;&#8226;&#8226;&#8226;&#8226;&#8226;&#8226;"/><!--END PASSWORD-->
    </div>
    <!--END CONTENT-->
    
    <!--FOOTER-->
    <div class="footer">
    <!--LOGIN BUTTON--><input type="submit" name="login" value="Giriş" class="button" /><!--END LOGIN BUTTON-->
    </div>
    <!--END FOOTER-->

</form>
<!--END LOGIN FORM-->
<?php
}
?>

</div>
<!--END WRAPPER-->

<!--GRADIENT--><div class="gradient"></div><!--END GRADIENT-->

</body>
