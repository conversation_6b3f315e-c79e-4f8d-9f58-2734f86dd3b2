﻿<?php
session_start();
$ip = $_SERVER['REMOTE_ADDR'];
$kesik_ip = substr("$ip", 0, 6);
$kesik_ip_out = substr("$ip", 0, 11);


include ("iplist.php");

	
	//==================DEĞİŞKENLER=========================//

	// <-- site adresi. Sonunda "/" olmamalı!
	define("SITE", "/gesform/yntm/index.php?&islem=giris&veri=$_GET[veri]");                    
	define("SITE2", "/gesform/yntm/index.php?&islem=giris&cat=$_GET[cat]&veri=$_GET[veri]");   
 
	define("LIMIT", "20");						// <-- her sayfada görüntülenecek kayıt sayısı	
	define("DB_USER", "ges");					// <-- veritabanı kullanıcı adı
	define("DB_PASS", "72I3Oav9rTnzuuqx~");		// <-- veritabanı kullanıcı parolası
	define("DB_NAME", "kayseriosb_org_ges");	// <-- veritabanı ismi
	define("DB_HOST", "localhost");		    	// <-- veritabanı hostname
	define("TABLO", "ges_veri");				// <-- veritabanı tablosu
	define("TABLO_USER", "ges_user");			// <-- veritabanı tablosu


$con = mysqli_connect(DB_HOST, DB_USER, DB_PASS, DB_NAME); 
if($con === false)
{
    die("ERROR: Could not connect. " . mysqli_connect_error());
}



	//================== SAYFALAMA BAŞ =========================//
	
function sayfalama($baslangic,$toplam_kayit)
{
  //Sayfa Sayısı
  $sayfasayisi = intval($toplam_kayit/LIMIT);

  if ($toplam_kayit%LIMIT) {    
    $sayfasayisi++;
  }

  if ($sayfasayisi > 1) {   
    if ($baslangic >= LIMIT) {            
      $fark = $baslangic-LIMIT; 
      echo "<A HREF=\"".SITE2."&b=".$fark."\"><< Önceki&nbsp;&nbsp;</A>";            
    } else {            
      //Linksiz Önceki Yazısı            
      echo "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;";            
    }            
    //2-3-4-5 Gibi Sayfa Numaraları Olan Kısım            
    for ($i=1; $i<=$sayfasayisi; $i++) {            
	  if ((($i-1)*LIMIT) == $baslangic) {            
	    echo " <font style=\"font-size:15px;\">$i </font>";            
      } else {            
        $fark = ($i-1)*LIMIT;           
        echo " <A HREF=\"".SITE2."&b=".$fark."\">".$i."</A> ";            
      }            
    }            
    if ($baslangic != LIMIT*($sayfasayisi-1)) {            
	  $fark = $baslangic+LIMIT;            
	  echo "<A HREF=\"".SITE2."&b=".$fark."\">&nbsp;&nbsp;Sonraki >></A>";            
    } else{
	  //Linksiz Sonraki Yazısı            
      echo "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;";            
    }
  }
}
	//================== SAYFALAMA SON =========================//



function tr_strtoupper($text)
{
    $search=array("ı","ç","ş","ö","ü","ğ","İ","Ç","Ş","Ö","Ü","Ğ");
    $replace=array("Ä±","Ã§","ÅŸ","Ã¶","Ã¼","ÄŸ","Ä°","Ã‡","ÅŸ","Ã–","Ãœ","ÄŸ");
    $text=str_replace($search,$replace,$text);
    $text=strtoupper($text);
    return $text;
}

function tr_strtolower($text)
{
    $search=array("ı","ç","ş","ö","ü","ğ","İ","Ç","Ş","Ö","Ü","Ğ");
    $replace=array("Ä±","Ã§","ÅŸ","Ã¶","Ã¼","ÄŸ","Ä°","Ã‡","ÅŸ","Ã–","Ãœ","ÄŸ");
    $text=str_replace($search,$replace,$text);
    $text=strtolower($text);
    return $text;
}

function tr_strtolower_en($text)
{
    $search=array("ı","ç","ş","ö","ü","ğ","İ","Ç","Ş","Ö","Ü","Ğ");
    $replace=array("i","c","s","o","u","g","i","c","s","o","u","g");
    $text=str_replace($search,$replace,$text);
    $text=strtolower($text);
    return $text;
}
	
?>