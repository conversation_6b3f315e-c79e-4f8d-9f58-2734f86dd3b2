<!-- <PERSON>ÇERİK KISMI -->
<?php 
if (!isset($_GET['b'])) { $b=0; } else { $b = $_GET['b']; } 

$veri_lower	= $_POST['veri'];
$veri_lower = tr_strtolower_en($veri_lower);

$url = 'http://'.$_SERVER['SERVER_NAME'].$_SERVER['REQUEST_URI'];


	$kosul=" WHERE (vekil_adi LIKE '%".$veri_lower."%' OR vekil_eposta LIKE '%".$veri_lower."%' OR vekil_tel_no LIKE '%".$veri_lower."%' OR sahip_adi LIKE '%".$veri_lower."%' OR sahip_eposta LIKE '%".$veri_lower."%' OR sahip_tel_no LIKE '%".$veri_lower."%' OR proje_info_proje_adi LIKE '%".$veri_lower."%' OR keywords LIKE '%".$veri_lower."%')";
	
	$sql_say = "SELECT * FROM ".TABLO." ".$kosul."";
	$sql = "SELECT * FROM ".TABLO." ".$kosul." ORDER BY id DESC LIMIT ".$b.",".LIMIT."";
	$se_say = mysqli_num_rows(mysqli_query($con, $sql_say));
	$result = mysqli_query($con, $sql);
	
if(!$result){
	die(mysqli_error($con));
}
 
?>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<div class="div_icerik">

<!-- SAYFALAMA Üst -->
<nobr>
<div class="div_sayfala">
<?php sayfalama($b,$se_say); ?>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</div>
</nobr>
<!-- SAYFALAMA Üst SON -->


<TABLE class="table_border" style="margin-left:0px; width:1325px; border-color:#000; border-collapse:collapse;">
<TR style=" background-color:#262222" class="ic_baslik">
	<TD><nobr> ID			</nobr></TD>
	<TD><nobr> TARİH		</nobr></TD>
	<TD><nobr> SAAT			</nobr></TD>
	<TD><nobr> PROJE ADI	</nobr></TD>
	<TD><nobr> BELGELER		</nobr></TD>
	<TD><nobr> VEKİL ADI	</nobr></TD>
	<TD><nobr> VEKİL EMAIL	</nobr></TD>
	<TD><nobr> VEKİL TEL.	</nobr></TD>
	<TD><nobr> BAŞV. TÜRÜ	</nobr></TD>
	<TD><nobr> ASİL ADI		</nobr></TD>
	<TD><nobr> ASİL EMAIL	</nobr></TD>
	<TD><nobr> ASİL TEL.	</nobr></TD>
	<TD><nobr> DÜZENLE		</nobr></TD>
</TR>

<?php 
while ($row = mysqli_fetch_array($result))
	{
		$a++;
		if ($a % 2 == 0) { $still="class=\"satir_1_asil\"";} else { $still="class=\"satir_2_asil\"";}
		$date = date_create($row['tarih']);
		
		$vekil_adi = mb_substr($row['vekil_adi'],0,20);
		$vekil_email = mb_substr($row['vekil_eposta'],0,25);
		$asil_adi = mb_substr($row['sahip_adi'],0,20);
		$asil_email = mb_substr($row['sahip_eposta'],0,20);
		$proje_adi = mb_substr($row['proje_info_proje_adi'],0,30);
		
?>

<TR <?=$still?> id="<?=$row['id']?>">
	<TD class="siracss">
		<nobr><b><?=$row['id']?></b></nobr>
	</TD>
	<TD class="siracss">
		<nobr><?=date_format($date,"d.m.Y")?></nobr>
	</TD>
	<TD class="siracss">
		<nobr><?=date_format($date,"H:i:s")?></nobr>
	</TD>
	<TD class="linkcss">
		<nobr>
		<a href="detay.php?id=<?=$row['id']?>&height=610&width=640" class="thickbox tooltips" Title="<table><tr height='11px'><td style='padding:0px;'><img src='resimler/detay.png' height='22px'/></td><td>BAŞVURU DETAYLARI</td></tr></table>"><img src="resimler/detay.png" height="15px"/> <?=$proje_adi?>..</a>
		</nobr>
		
	</TD>
	<TD class="linkcss">
		<nobr>
		<a href="belgeler.php?id=<?=$row['id']?>&height=610&width=640" class="thickbox tooltips" Title="<table><tr height='11px'><td style='padding:0px;'><img src='resimler/belgeler.png' height='22px'/></td><td>BELGELER</td></tr></table>"><img src="resimler/belgeler.png" height="15px"/> BELGELER</a>
		</nobr>
	</TD>
	<TD class="isimcss">
		<nobr><?=$vekil_adi?>..</nobr>
	</TD>
	
	<TD class="isimcss">
	<nobr>
		<nobr><?=$vekil_email?>..</nobr>
	</nobr>
	</TD>
	
	<TD class="isimcss">
	<nobr>
		<nobr><?=$row['vekil_tel_no']?></nobr>
	</nobr>
	</TD>

	<TD class="isimcss">
		<nobr><?=$row['sahip_basvuru_turu']?></nobr>
	</TD>
	
	<TD class="isimcss">
		<nobr><?=$asil_adi?>...</nobr>
	</TD>
	
	<TD class="isimcss">
		<nobr><?=$asil_email?>..</nobr>
	</TD>
	
	<TD class="isimcss">
		<nobr><?=$row['sahip_tel_no']?></nobr>
	</TD>
	



	<TD class="usercss">
	<nobr>
<?php if ($_SESSION['user_level'] == '1'){ ?>
		<a href="duzenle.php?id=<?=$row['id']?>&height=610&width=640" class="thickbox tooltips" Title="<table><tr height='11px'><td style='padding:0px;'><img src='resimler/duzenle.png' height='22px'/></td><td>Başvuruyu Düzenle</td></tr></table>">
			<img src="resimler/duzenle.png" title="Düzenle">
			<span style="width:100px; margin-left:-50px;">DÜZENLE</span>
		</a>

		<a href="sil_1.php?id=<?=$row['id']?>&height=170&width=680" class="thickbox tooltips" Title="<table><tr height='11px'><td style='padding:0px;'><img src='resimler/sil.png' height='22px'/></td><td>!!! DİKKAT !!! Silme Ekranındasın !.</td></tr></table>">
			<img src="resimler/sil.png" title="Sil">
			<span style="width:100px; margin-left:-50px;">SİL</span>
		</a>
<?php }else{ ?>
	---
<?php } ?>
	</nobr>
	</TD>

</TR>

<?php } ?>

</TABLE>


<!-- SAYFALAMA -->
<div class="div_sayfala">
<?php sayfalama($b,$se_say); ?>
</div>
<!-- SAYFALAMA SON -->

</div>
<!-- İÇERİK KISMI SON -->

</center>
</body>
