<?php
session_start();
include ("fonksiyon.php");

		$keywords_lower = $_POST['vekil_adi'].", ".$_POST['vekil_eposta'].", ".$_POST['vekil_tel_no'].", ".$_POST['vekil_note'].", ".$_POST['sahip_basvuru_turu'].", ".$_POST['sahip_adi'].", ".$_POST['sahip_kisi_turu'].", ".$_POST['sahip_vergi_kimlik_no'].", ".$_POST['sahip_eposta'].", ".$_POST['sahip_kep_adresi'].", ".$_POST['sahip_tel_no'].", ".$_POST['sahip_note'].", ".$_POST['proje_info_guc_arttirim'].", ".$_POST['proje_info_proje_adi'].", ".$_POST['proje_info_uret_tuket_tesis'].", ".$_POST['proje_info_uygulama_yeri'].", ".$_POST['proje_info_ilce_il'].", ".$_POST['proje_info_kurulacak_mevki'].", ".$_POST['proje_info_kurulacak_ada'].", ".$_POST['proje_info_kurulacak_parsel'].", ".$_POST['proje_info_gecici_parsel'].", ".$_POST['proje_info_kaynak_turu'].", ".$_POST['proje_info_baglanti_sekli'].", ".$_POST['proje_info_abonelik_secimi'].", ".$_POST['proje_info_yonetmelik'].", ".$_POST['proje_info_note'];
		$keywords = tr_strtolower_en($keywords_lower);


		$sql_upd = mysqli_query($con, "UPDATE ".TABLO." SET vekil_adi='$_POST[vekil_adi]', vekil_eposta='$_POST[vekil_eposta]', vekil_tel_no='$_POST[vekil_tel_no]', vekil_note='$_POST[vekil_note]', sahip_basvuru_turu='$_POST[sahip_basvuru_turu]', sahip_adi='$_POST[sahip_adi]', sahip_kisi_turu='$_POST[sahip_kisi_turu]', sahip_vergi_kimlik_no='$_POST[sahip_vergi_kimlik_no]', sahip_eposta='$_POST[sahip_eposta]', sahip_kep_adresi='$_POST[sahip_kep_adresi]', sahip_tel_no='$_POST[sahip_tel_no]', sahip_note='$_POST[sahip_note]', proje_info_guc_arttirim='$_POST[proje_info_guc_arttirim]', proje_info_proje_adi='$_POST[proje_info_proje_adi]', proje_info_uret_tuket_tesis='$_POST[proje_info_uret_tuket_tesis]', proje_info_uygulama_yeri='$_POST[proje_info_uygulama_yeri]', proje_info_ilce_il='$_POST[proje_info_ilce_il]', proje_info_kurulacak_mevki='$_POST[proje_info_kurulacak_mevki]', proje_info_kurulacak_ada='$_POST[proje_info_kurulacak_ada]', proje_info_kurulacak_parsel='$_POST[proje_info_kurulacak_parsel]', proje_info_gecici_parsel='$_POST[proje_info_gecici_parsel]', proje_info_tesis_dc_guc='$_POST[proje_info_tesis_dc_guc]', proje_info_tesis_ac_guc='$_POST[proje_info_tesis_ac_guc]', proje_info_kaynak_turu='$_POST[proje_info_kaynak_turu]', proje_info_baglanti_sekli='$_POST[proje_info_baglanti_sekli]', proje_info_santral_enlemx='$_POST[proje_info_santral_enlemx]', proje_info_santral_boylamy='$_POST[proje_info_santral_boylamy]', proje_info_abonelik_secimi='$_POST[proje_info_abonelik_secimi]', proje_info_yonetmelik='$_POST[proje_info_yonetmelik]', proje_info_note='$_POST[proje_info_note]', keywords='$keywords' WHERE id='$_POST[id]'");

		print "ok";
?>
