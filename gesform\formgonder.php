<?php
include ("dbcon.php");
include ("head.php");
if($_POST)
{
	
$vekil_adi									= htmlspecialchars($_POST['TemsilBasvuruSahibi']);									
$vekil_eposta								= htmlspecialchars($_POST['TemsilMailAdresi']);								
$vekil_tel_no								= htmlspecialchars($_POST['TemsilTelNo']);								
$vekil_note									= htmlspecialchars($_POST['TemsilYazismaAdresi']);									
$sahip_basvuru_turu							= htmlspecialchars($_POST['SelectedBasvuruTuru']);							
$sahip_adi									= htmlspecialchars($_POST['BasvuruSahibi']);									
$sahip_kisi_turu							= htmlspecialchars($_POST['SelectedBasvuruSahibiKisiTuru']);							
$sahip_vergi_kimlik_no						= htmlspecialchars($_POST['VergiKimlikNumarasi']);						
$sahip_eposta								= htmlspecialchars($_POST['MailAdresi']);								
$sahip_kep_adresi							= htmlspecialchars($_POST['KepAdresi']);							
$sahip_tel_no								= htmlspecialchars($_POST['TelNo']);								
$sahip_note									= htmlspecialchars($_POST['YazismaAdresi']);									
$proje_info_guc_arttirim					= htmlspecialchars($_POST['GucArttirim']);					
$proje_info_proje_adi						= htmlspecialchars($_POST['ProjeAdi']);						
$proje_info_uret_tuket_tesis				= htmlspecialchars($_POST['SelectedUretimTuketimYeri']);				
$proje_info_uygulama_yeri					= htmlspecialchars($_POST['SelectedUygulamaYeri']);					
$proje_info_ilce_il							= htmlspecialchars($_POST['SelectedIl']);							
$proje_info_kurulacak_mevki					= htmlspecialchars($_POST['MahalleMevkiKoy']);					
$proje_info_kurulacak_ada					= htmlspecialchars($_POST['Ada']);					
$proje_info_kurulacak_parsel				= htmlspecialchars($_POST['Parsel']);				
$proje_info_gecici_parsel					= htmlspecialchars($_POST['GeciciParsel']);					
$proje_info_tesis_dc_guc					= htmlspecialchars($_POST['UretimTesisiDCKuruluGuc']);					
$proje_info_tesis_ac_guc					= htmlspecialchars($_POST['UretimTesisiACKuruluGuc']);					
$proje_info_kaynak_turu						= htmlspecialchars($_POST['SelectedKaynakTuru']);						
$proje_info_baglanti_sekli					= htmlspecialchars($_POST['SelectedOlcuGerilimSeviyesi']);					
$proje_info_santral_enlemx					= htmlspecialchars($_POST['UretimSantralEnlem']);					
$proje_info_santral_boylamy					= htmlspecialchars($_POST['UretimSantralBoylam']);					
$proje_info_abonelik_secimi					= htmlspecialchars($_POST['SelectedAbonelik']);					
$proje_info_yonetmelik						= htmlspecialchars($_POST['SelectedIlgiliYonetmelikMaddesi']);						
$proje_info_note							= htmlspecialchars($_POST['Aciklama']);

	
$belge_array_1 = array($_FILES['BasvuruDilekce'],$_FILES['BasvuruFormuEk1'],$_FILES['YetkiBelgeleri'],$_FILES['OrtaklikYapisiBelgeleri'],$_FILES['KiraSozlesmesi'],$_FILES['AboneNumarasiBelgesi'],$_FILES['YapiRuhsati'],$_FILES['BasvuruBedeliDekontu'],$_FILES['TekHatSemasi'],$_FILES['EigmTeknikDegerlendirmeFormuPdf'],$_FILES['EigmTeknikDegerlendirmeFormuXlsx'],$_FILES['KoordinatliAplikasyonKrokisi'],$_FILES['FaaliyetYasaginaIliskinBeyanEk4'],$_FILES['TicaretSicilGazetesi'],$_FILES['SanayiSicilBelgesi']);

$belge_array_2 = array($_FILES['BasvuruDilekce2'],$_FILES['BasvuruFormuEk12'],$_FILES['YetkiBelgeleri2'],$_FILES['OrtaklikYapisiBelgeleri2'],$_FILES['KiraSozlesmesi2'],$_FILES['AboneNumarasiBelgesi2'],$_FILES['YapiRuhsati2'],$_FILES['BasvuruBedeliDekontu2'],$_FILES['TekHatSemasi2'],$_FILES['EigmTeknikDegerlendirmeFormuPdf2'],$_FILES['EigmTeknikDegerlendirmeFormuXlsx2'],$_FILES['KoordinatliAplikasyonKrokisi2'],$_FILES['FaaliyetYasaginaIliskinBeyanEk42'],$_FILES['TicaretSicilGazetesi2'],$_FILES['SanayiSicilBelgesi2']);

$eski = array('Ç','ç','Ğ','ğ','ı','İ','Ö','ö','Ş','ş','Ü','ü',' ','(',')','+','ì‡','ìˆ','â°','ìˆ','ì§','ì†','â·','ì§','‡');
$yeni = array('c','c','g','g','i','i','o','o','s','s','u','u','_','-','-','-','','','','','','','','','');


if ($sahip_basvuru_turu == "0001")
{
$b = 0;
foreach ($belge_array_1 as $belge_bos) {
	$b++;
	if($belge_bos['name'] == "") {
		if ($b == 1){$belge_bos_uyari = '<b>Başvuru Dilekçesi</b> alanına dosya eklemediniz !!.';}
		elseif ($b == 2){$belge_bos_uyari = '<b>Başvuru Ek1</b> alanına dosya eklemediniz !!.';}
		elseif ($b == 3){$belge_bos_uyari = '<b>Vekil Yetki Belgeleri</b> alanına dosya eklemediniz !!.';}
		elseif ($b == 4){$belge_bos_uyari = '<b>Ortaklık Belgeleri</b> alanına dosya eklemediniz !!.';}
		elseif ($b == 5){$belge_bos_uyari = '<b>Tapu / Tahsis / Kira Sözleşmesi</b> alanına dosya eklemediniz !!.';}
		elseif ($b == 6){$belge_bos_uyari = '<b>Abone No</b> alanına dosya eklemediniz !!.';}
		elseif ($b == 7){$belge_bos_uyari = '<b>Yapı Ruhsat İzin Belgesi</b> alanına dosya eklemediniz !!.';}
		elseif ($b == 8){$belge_bos_uyari = '<b>Başvuru Dekontu</b> alanına dosya eklemediniz !!.';}
		elseif ($b == 9){$belge_bos_uyari = '<b>TekHat Şeması</b> alanına dosya eklemediniz !!.';}
		elseif ($b == 10){$belge_bos_uyari = '<b>EİGM Değerlendirme PDF</b> alanına dosya eklemediniz !!.';}
		elseif ($b == 11){$belge_bos_uyari = '<b>EİGM Değerlendirme XLSX</b> alanına dosya eklemediniz !!.';}
		elseif ($b == 12){$belge_bos_uyari = '<b>Aplikasyon Krokisi</b> alanına dosya eklemediniz !!.';}
		elseif ($b == 13){$belge_bos_uyari = '<b>Faaliyet Yasağına İlişkin EK4</b> alanına dosya eklemediniz !!.';}
		elseif ($b == 14){$belge_bos_uyari = '<b>Ticaret Sicil Gazetesi</b> alanına dosya eklemediniz !!.';}
		elseif ($b == 15){goto hedef;}
		echo '<center><br><br><br><br><br><br><font color="darkred">'.$belge_bos_uyari.'<br><button onClick="geri()">Geri Dön</button></font></center>';
		exit;
	}
}
}
elseif ($sahip_basvuru_turu == "0002")
{
$b = 0;
foreach ($belge_array_2 as $belge_bos) {
	$b++;
	if($belge_bos['name'] == "") {
		if ($b == 1){$belge_bos_uyari = '<b>Başvuru Dilekçesi</b> alanına dosya eklemediniz !!.';}
		elseif ($b == 2){$belge_bos_uyari = '<b>Başvuru Ek1</b> alanına dosya eklemediniz !!.';}
		elseif ($b == 3){$belge_bos_uyari = '<b>Vekil Yetki Belgeleri</b> alanına dosya eklemediniz !!.';}
		elseif ($b == 4){$belge_bos_uyari = '<b>Ortaklık Belgeleri</b> alanına dosya eklemediniz !!.';}
		elseif ($b == 5){$belge_bos_uyari = '<b>Tapu / Tahsis / Kira Sözleşmesi</b> alanına dosya eklemediniz !!.';}
		elseif ($b == 6){$belge_bos_uyari = '<b>Abone No</b> alanına dosya eklemediniz !!.';}
		elseif ($b == 7){$belge_bos_uyari = '<b>Yapı Ruhsat İzin Belgesi</b> alanına dosya eklemediniz !!.';}
		elseif ($b == 8){$belge_bos_uyari = '<b>Başvuru Dekontu</b> alanına dosya eklemediniz !!.';}
		elseif ($b == 9){$belge_bos_uyari = '<b>TekHat Şeması</b> alanına dosya eklemediniz !!.';}
		elseif ($b == 10){$belge_bos_uyari = '<b>EİGM Değerlendirme PDF</b> alanına dosya eklemediniz !!.';}
		elseif ($b == 11){$belge_bos_uyari = '<b>EİGM Değerlendirme XLSX</b> alanına dosya eklemediniz !!.';}
		elseif ($b == 12){$belge_bos_uyari = '<b>Aplikasyon Krokisi</b> alanına dosya eklemediniz !!.';}
		elseif ($b == 13){$belge_bos_uyari = '<b>Faaliyet Yasağına İlişkin EK4</b> alanına dosya eklemediniz !!.';}
		elseif ($b == 14){$belge_bos_uyari = '<b>Ticaret Sicil Gazetesi</b> alanına dosya eklemediniz !!.';}
		elseif ($b == 15){goto hedef2;}
		echo '<center><br><br><br><br><br><br><font color="darkred">'.$belge_bos_uyari.'<br><button onClick="geri()">Geri Dön</button></font></center>';
		exit;
	}
}
}

if ($sahip_basvuru_turu == "0001")
{
hedef:
$i = 0;
foreach ($belge_array_1 as $belgeler) {
	$i++;
	$path = "dosyalar/";
	if($belgeler != '')
	{
		$filtre = str_replace($eski,$yeni,basename($belgeler['name']));
		$filtre = strtolower($filtre);
		$temp = explode(".", $filtre);

		$filtre_projeadi = str_replace($eski,$yeni,$_POST['ProjeAdi']);
		$filtre_projeadi = strtolower($filtre_projeadi);
		$filtre_projeadi = strtoupper($filtre_projeadi);

		$newfilename = '';
		$newfilename = $filtre_projeadi."-".$i."-".$temp[0]."-".$belgeler["size"].'.'.end($temp);
		$path = $path . $newfilename;
		
		if($belgeler['name'] != "") {
			if ($i == 1 AND $belgeler['name'] != ""){$belge_lisanssiz_basvuru_dilekce = $newfilename;}
			elseif ($i == 2 AND $belgeler['name'] != ""){$belge_lisanssiz_basvuru_ek1 = $newfilename;}
			elseif ($i == 3 AND $belgeler['name'] != ""){$belge_lisanssiz_vekil_yetki_belge = $newfilename;}
			elseif ($i == 4 AND $belgeler['name'] != ""){$belge_lisanssiz_ortaklik_belge = $newfilename;}
			elseif ($i == 5 AND $belgeler['name'] != ""){$belge_lisanssiz_tapu_tahsis = $newfilename;}
			elseif ($i == 6 AND $belgeler['name'] != ""){$belge_lisanssiz_abone_no = $newfilename;}
			elseif ($i == 7 AND $belgeler['name'] != ""){$belge_lisanssiz_yapi_ruhsat_izin = $newfilename;}
			elseif ($i == 8 AND $belgeler['name'] != ""){$belge_lisanssiz_basvuru_dekont = $newfilename;}
			elseif ($i == 9 AND $belgeler['name'] != ""){$belge_lisanssiz_tekhat_semasi = $newfilename;}
			elseif ($i == 10 AND $belgeler['name'] != ""){$belge_lisanssiz_eigm_degerlendirme_pdf = $newfilename;}
			elseif ($i == 11 AND $belgeler['name'] != ""){$belge_lisanssiz_eigm_degerlendirme_xlsx = $newfilename;}
			elseif ($i == 12 AND $belgeler['name'] != ""){$belge_lisanssiz_aplikasyon_kroki = $newfilename;}
			elseif ($i == 13 AND $belgeler['name'] != ""){$belge_lisanssiz_faaliyet_yasagi_ek4 = $newfilename;}
			elseif ($i == 14 AND $belgeler['name'] != ""){$belge_lisanssiz_ticaret_sicil = $newfilename;}
			elseif ($i == 15 AND $belgeler['name'] != ""){$belge_lisanssiz_sanayi_sicil = $newfilename;}
	
			if(move_uploaded_file($belgeler['tmp_name'], $path)) {
				echo "<center><b>".$i."</b> - <font color='white'>".basename($belgeler['name'])." Dosyası -> <b>".$newfilename."</b> olarak kaydedildi!.</font><br></center>";
			}
		}
	}
}
}elseif ($sahip_basvuru_turu == "0002")
{
hedef2:
$i = 0;
foreach ($belge_array_2 as $belgeler) {
	$i++;
	$path = "dosyalar/";
	if($belgeler != '')
	{
		$filtre = str_replace($eski,$yeni,basename($belgeler['name']));
		$filtre = strtolower($filtre);
		$temp = explode(".", $filtre);
		$newfilename = '';
		$newfilename = "2-".$_POST['ProjeAdi'] . "-" . $temp[0] . "-" . $belgeler["size"] . '.' . end($temp);
		$path = $path . $newfilename;
		
		if($belgeler['name'] != "") {
			if ($i == 1 AND $belgeler['name'] != ""){$belge_25kw_basvuru_dilekce = $newfilename;}
			elseif ($i == 2 AND $belgeler['name'] != ""){$belge_25kw_basvuru_ek1 = $newfilename;}
			elseif ($i == 3 AND $belgeler['name'] != ""){$belge_25kw_vekil_yetki_belge = $newfilename;}
			elseif ($i == 4 AND $belgeler['name'] != ""){$belge_25kw_ortaklik_belge = $newfilename;}
			elseif ($i == 5 AND $belgeler['name'] != ""){$belge_25kw_tapu_tahsis = $newfilename;}
			elseif ($i == 6 AND $belgeler['name'] != ""){$belge_25kw_abone_no = $newfilename;}
			elseif ($i == 7 AND $belgeler['name'] != ""){$belge_25kw_yapi_ruhsat_izin = $newfilename;}
			elseif ($i == 8 AND $belgeler['name'] != ""){$belge_25kw_basvuru_dekont = $newfilename;}
			elseif ($i == 9 AND $belgeler['name'] != ""){$belge_25kw_tekhat_semasi = $newfilename;}
			elseif ($i == 10 AND $belgeler['name'] != ""){$belge_25kw_eigm_degerlendirme_pdf = $newfilename;}
			elseif ($i == 11 AND $belgeler['name'] != ""){$belge_25kw_eigm_degerlendirme_xlsx = $newfilename;}
			elseif ($i == 12 AND $belgeler['name'] != ""){$belge_25kw_aplikasyon_kroki = $newfilename;}
			elseif ($i == 13 AND $belgeler['name'] != ""){$belge_25kw_faaliyet_yasagi_ek4 = $newfilename;}
			elseif ($i == 14 AND $belgeler['name'] != ""){$belge_25kw_ticaret_sicil = $newfilename;}
			elseif ($i == 15 AND $belgeler['name'] != ""){$belge_25kw_sanayi_sicil = $newfilename;}
	
			if(move_uploaded_file($belgeler['tmp_name'], $path)) {
				echo "<center><b>".$i."</b> - <font color='white'>".basename($belgeler['name'])." Dosyası -> <b>".$newfilename."</b> olarak kaydedildi!.</font><br></center>";
			}
		}
	}
}
}


$keywords_lower = $vekil_adi.", ".$vekil_eposta.", ".$vekil_tel_no.", ".$vekil_note.", ".$sahip_basvuru_turu.", ".$sahip_adi.", ".$sahip_kisi_turu.", ".$sahip_vergi_kimlik_no.", ".$sahip_eposta.", ".$sahip_kep_adresi.", ".$sahip_tel_no.", ".$sahip_note.", ".$proje_info_guc_arttirim.", ".$proje_info_proje_adi.", ".$proje_info_uret_tuket_tesis.", ".$proje_info_uygulama_yeri.", ".$proje_info_ilce_il.", ".$proje_info_kurulacak_mevki.", ".$proje_info_kurulacak_ada.", ".$proje_info_kurulacak_parsel.", ".$proje_info_gecici_parsel.", ".$proje_info_kaynak_turu.", ".$proje_info_baglanti_sekli.", ".$proje_info_abonelik_secimi.", ".$proje_info_yonetmelik.", ".$proje_info_note;

$keywords = tr_strtolower_en($keywords_lower);


$tarih = date("Y-m-d H:i:s");
$ip_adres = $_SERVER['REMOTE_ADDR'];
$query  = "INSERT INTO ".TABLO." (tarih,ip_adres,vekil_adi,vekil_eposta,vekil_tel_no,vekil_note,sahip_basvuru_turu,sahip_adi,sahip_kisi_turu,sahip_vergi_kimlik_no,sahip_eposta,sahip_kep_adresi,sahip_tel_no,sahip_note,proje_info_guc_arttirim,proje_info_proje_adi,proje_info_uret_tuket_tesis,proje_info_uygulama_yeri,proje_info_ilce_il,proje_info_kurulacak_mevki,proje_info_kurulacak_ada,proje_info_kurulacak_parsel,proje_info_gecici_parsel,proje_info_tesis_dc_guc,proje_info_tesis_ac_guc,proje_info_kaynak_turu,proje_info_baglanti_sekli,proje_info_santral_enlemx,proje_info_santral_boylamy,proje_info_abonelik_secimi,proje_info_yonetmelik,proje_info_note,belge_lisanssiz_basvuru_dilekce,belge_lisanssiz_basvuru_ek1,belge_lisanssiz_vekil_yetki_belge,belge_lisanssiz_ortaklik_belge,belge_lisanssiz_tapu_tahsis,belge_lisanssiz_abone_no,belge_lisanssiz_yapi_ruhsat_izin,belge_lisanssiz_basvuru_dekont,belge_lisanssiz_tekhat_semasi,belge_lisanssiz_eigm_degerlendirme_pdf,belge_lisanssiz_eigm_degerlendirme_xlsx,belge_lisanssiz_aplikasyon_kroki,belge_lisanssiz_faaliyet_yasagi_ek4,belge_lisanssiz_ticaret_sicil,belge_lisanssiz_sanayi_sicil,belge_25kw_basvuru_dilekce,belge_25kw_basvuru_ek1,belge_25kw_vekil_yetki_belge,belge_25kw_ortaklik_belge,belge_25kw_tapu_tahsis,belge_25kw_abone_no,belge_25kw_yapi_ruhsat_izin,belge_25kw_basvuru_dekont,belge_25kw_tekhat_semasi,belge_25kw_eigm_degerlendirme_pdf,belge_25kw_eigm_degerlendirme_xlsx,belge_25kw_aplikasyon_kroki,belge_25kw_faaliyet_yasagi_ek4,belge_25kw_ticaret_sicil,belge_25kw_sanayi_sicil,keywords) VALUES ('$tarih','$ip_adres','$vekil_adi','$vekil_eposta','$vekil_tel_no','$vekil_note','$sahip_basvuru_turu','$sahip_adi','$sahip_kisi_turu','$sahip_vergi_kimlik_no','$sahip_eposta','$sahip_kep_adresi','$sahip_tel_no','$sahip_note','$proje_info_guc_arttirim','$proje_info_proje_adi','$proje_info_uret_tuket_tesis','$proje_info_uygulama_yeri','$proje_info_ilce_il','$proje_info_kurulacak_mevki','$proje_info_kurulacak_ada','$proje_info_kurulacak_parsel','$proje_info_gecici_parsel','$proje_info_tesis_dc_guc','$proje_info_tesis_ac_guc','$proje_info_kaynak_turu','$proje_info_baglanti_sekli','$proje_info_santral_enlemx','$proje_info_santral_boylamy','$proje_info_abonelik_secimi','$proje_info_yonetmelik','$proje_info_note','$belge_lisanssiz_basvuru_dilekce','$belge_lisanssiz_basvuru_ek1','$belge_lisanssiz_vekil_yetki_belge','$belge_lisanssiz_ortaklik_belge','$belge_lisanssiz_tapu_tahsis','$belge_lisanssiz_abone_no','$belge_lisanssiz_yapi_ruhsat_izin','$belge_lisanssiz_basvuru_dekont','$belge_lisanssiz_tekhat_semasi','$belge_lisanssiz_eigm_degerlendirme_pdf','$belge_lisanssiz_eigm_degerlendirme_xlsx','$belge_lisanssiz_aplikasyon_kroki','$belge_lisanssiz_faaliyet_yasagi_ek4','$belge_lisanssiz_ticaret_sicil','$belge_lisanssiz_sanayi_sicil','$belge_25kw_basvuru_dilekce','$belge_25kw_basvuru_ek1','$belge_25kw_vekil_yetki_belge','$belge_25kw_ortaklik_belge','$belge_25kw_tapu_tahsis','$belge_25kw_abone_no','$belge_25kw_yapi_ruhsat_izin','$belge_25kw_basvuru_dekont','$belge_25kw_tekhat_semasi','$belge_25kw_eigm_degerlendirme_pdf','$belge_25kw_eigm_degerlendirme_xlsx','$belge_25kw_aplikasyon_kroki','$belge_25kw_faaliyet_yasagi_ek4','$belge_25kw_ticaret_sicil','$belge_25kw_sanayi_sicil','$keywords')";
$result = mysqli_query($con,$query) or die (mysqli_error($con));
echo '<center><br><font color=\'white\' size=\'35\'>!!! KAYDINIZ <b>BAŞARILI</b> BİR ŞEKİLDE ALINMIŞTIR !!!</font></center>';



//Telekom Api - Vekil için
if ($vekil_tel_no != ''){
	require_once('Sms/SmsApi.php');
	$smsApi = new SmsApi("app.teknomart.com.tr", "kayseriosb","cpmzdieR");

	$vekil_tel_no = str_replace('-','',$vekil_tel_no);
	
	//echo "\n***** SendSingleSms *****\n";
	$request = new SendSingleSms();
	$request->title = "KAYSERI OSB";
	$request->content = "'".$proje_info_proje_adi."' isimli LES-GES projeniz başarılı bir şekilde kaydedilmiştir. 1";
	$request->number = "90".$vekil_tel_no."";
	$request->encoding = 1;
	$request->sender = "KAYSERI OSB";
	$response = $smsApi->sendSingleSms($request);

	if($response->err == null){
		//echo "MessageId: ".$response->pkgID."\n";
	}else{
		echo "Status: ".$response->err->status."\n";
		echo "Code: ".$response->err->code."\n";
		echo "Message: ".$response->err->message."\n";
	}
}
//
sleep(1);
//Telekom Api - Asil için
if ($sahip_tel_no != ''){
	require_once('Sms/SmsApi.php');
	$smsApi = new SmsApi("app.teknomart.com.tr", "kayseriosb","cpmzdieR");

	$sahip_tel_no = str_replace('-','',$sahip_tel_no);
	
	//echo "\n***** SendSingleSms *****\n";
	$request = new SendSingleSms();
	$request->title = "KAYSERI OSB";
	$request->content = "'".$proje_info_proje_adi."' isimli GES-LES projeniz başarılı bir şekilde kaydedilmiştir. 2";
	$request->number = "90".$sahip_tel_no."";
	$request->encoding = 1;
	$request->sender = "KAYSERI OSB";
	$response = $smsApi->sendSingleSms($request);

	if($response->err == null){
		//echo "MessageId: ".$response->pkgID."\n";
	}else{
		echo "Status: ".$response->err->status."\n";
		echo "Code: ".$response->err->code."\n";
		echo "Message: ".$response->err->message."\n";
	}
}
//



echo '<meta http-equiv="refresh" content="2; url=success.php">';
}else{
	echo 'HATALI bir deneme yapıyorsunuz!!!.<br>Sizi doğru adrese yönlendirelim, baştan başlayın. 1';
	header('Refresh: 2; URL=index.php');
}
?>
</body>
</html>