<?php
include "../db/baglanti.php";
extract($_GET);
$sayfatip = 9;
$tablo = "kisiler";


if ($_POST['islem'] == 1) {

    $id = $_POST['id'];
    $icresimcek = $db->prepare("SELECT * FROM $tablo WHERE id=:id"); // Menüye ait iç resimleri çağırıp sileceğiz.
    $icresimcek->execute(array("id" => $id));
    foreach ($icresimcek as $icresim) {
        $resimlink = $icresim['resim'];
        unlink('../' . $resimlink);
    }

    $menusil = $db->prepare("DELETE FROM $tablo WHERE id=:id");
    $menusil->execute(array("id" => $id));
} else {
    //MENÜ SİLME İŞLEMİ YOKSA SAYFAYI YÜKLÜYORUZ -- EĞER SİLME İŞLEMİ VARSA AŞAĞISI YÜKLENMİYOR... YANİ ASIL SAYFA KODLARI AŞAĞIDAN BAŞLIYOR.

    require 'header.php';
    require 'solmenu.php';
    include "../db/baglan.php";

    $yonlenurl = $_POST['url'];
    /*********************************************************** Yeni Menü Ekleme Alanı ***********************************************************************/

    if (isset($_POST['ekle'])) {
        $yenimenu = "1";
        include 'icresimyukle.php';

        foreach ($_POST as $column => $value) {
            if ($column != "url" & $column != "ekle" & $column != "id") {
                // Sütun varlığını kontrol et
                $SutunVarmi = $db->prepare("DESCRIBE $tablo");
                $SutunVarmi->execute();
                $Sutunlar = $SutunVarmi->fetchAll(PDO::FETCH_COLUMN);
                if (!in_array($column, $Sutunlar)) {
                    // Sütun yoksa oluştur
                    $db->exec("ALTER TABLE $tablo ADD COLUMN $column TEXT");
                }


                if (strpos($column, "icerik_") !== false) {
                    $value = YazidakiResimleriIndir($value);
                    $value = str_replace('&nbsp;', '', $value);
                    $value = str_replace("'", '"', $value);
                } else {
                    $value = str_replace('"', "'", $value);
                    $value = addslashes($value);
                }

                $keys[] = "{$column}";
                $values[] = "'{$value}'";
            }
        }


        try {
            $kayit_ekle = $db->query("INSERT INTO $tablo (" . implode(",", $keys) . ",resim) 
                VALUES (" . implode(",", $values) . ",'$resim')");

            if ($kayit_ekle) {
                Basarili("Kişi başarıyla eklendi!");
            } else {
                Hata("Kişi eklenemedi!");
            }
        } catch (Exception $e) {
            Hata("Bir hata oluştu " . $e->getMessage());
        }
    }
    /*********************************************************** Menü Ekleme Alanı Sonu ***********************************************************************/


    /*********************************************************** Menü Düzenleme Alanı ***********************************************************************/
    if (isset($_POST['duzenle'])) {
        $yenimenu = "0";
        $id = $_POST['id'];


        $fields = array(
            'resim' => 'resimlink',
        );

        foreach ($fields as $postKey => $linkVar) {
            if ($_POST[$postKey . 'sil'] == "on") {
                $columnName = str_replace('sil', '', $postKey); // 'sil' son eki kaldırarak sütun adını elde ediyoruz
                $logosil = $db->prepare("UPDATE $tablo SET $columnName = '' WHERE id = :id");
                $logosil->execute(array("id" => $id));
                unlink("../" . $_POST[$linkVar]);
            }
        }


        include 'icresimyukle.php';


        foreach ($_POST as $column => $value) {

            if ($column != "url" & $column != "duzenle" & $column != "resim1link" & $column != "resim2link" & $column != "resim1sil" & $column != "resim2sil" & $column != "dosyalink" & $column != "dosyasil") {

                $SutunVarmi = $db->prepare("DESCRIBE $tablo");
                $SutunVarmi->execute();
                $Sutunlar = $SutunVarmi->fetchAll(PDO::FETCH_COLUMN);
                if (!in_array($column, $Sutunlar)) {
                    // Sütun yoksa oluştur
                    $db->exec("ALTER TABLE $tablo ADD COLUMN $column TEXT");
                }


                if (strpos($column, "icerik_") !== false) {
                    $value = YazidakiResimleriIndir($value);
                    $value = str_replace('&nbsp;', '', $value);
                    $value = str_replace("'", '"', $value);
                } else {
                    $value = str_replace('"', "'", $value);
                    $value = addslashes($value);
                }

                $keys[] = "{$column}";
                $values[] = "'{$value}'";
            }
        }



        try {
            $arr = array_map(function ($a, $b) {
                return $a . '=' . $b;
            }, $keys, $values);
            $duzenle = $db->query("UPDATE $tablo SET " . implode(",", $arr) . ",resim='$resim' WHERE id=" . $id);

            if ($duzenle) {
                Basarili("Düzenleme başarılı!");
            } else {
                Hata("Kişi düzenlenirken hata oluştu!");
            }
        } catch (Exception $e) {
            Hata("Bir hata oluştu " . $e->getMessage());
        }
    }

    /*********************************************************** Menü Düzenleme Alanı Sonu ***********************************************************************/


    $yetki = $db->prepare("SELECT * FROM yonetim WHERE kullanici_adi=:id");
    $yetki->execute(array("id" => $_SESSION['kullanici_adi']));
    $yetkicek = $yetki->fetch(PDO::FETCH_ASSOC);

    if ($yetkicek['ustmenu'] == 1) {

        $url = 'https://' . $_SERVER['SERVER_NAME'] . $_SERVER['REQUEST_URI'];

        $uid = $_GET['uid'];
        $id = $_GET['id'];
        $st = $_GET['st'];


        if ($_GET["id"]) {
            $MenuSor = $db->prepare("SELECT * FROM $tablo WHERE id=$id");
            $MenuSor->execute();
            $MenuVeri = $MenuSor->fetch(PDO::FETCH_ASSOC);
        }




?>


        <div class="main-content app-content mt-0">
            <div class="side-app">



                <div id="gizle" class="main-container container-fluid <?= isset($_GET['id']) ? " gizle" : "goster"; ?>">
                    <div class="page-header">

                        <div>
                            <?php if (empty($_GET['id'])) { ?>
                                <button type="button" id="gizle" onclick="menuler()" class="btn btn-green"><i class="fe fe-plus me-2"></i>Yeni Kişi Ekle</button>
                            <?php } else { ?>
                                <a href="kisiekle.php?uid=<?php echo $_GET['uid']; ?>&st=<?php echo $st ?>"><button type="button" class="btn btn-green"><i class="fe fe-plus me-2"></i>Yeni Kişi Ekle</button></a>
                                <button type="button" id="gizle" onclick="menuler()" class="btn btn-gray"><i class="fe fe-edit me-2"></i>Kişiyi Düzenlemeye Devam Et</button>
                            <?php } ?>
                        </div>

                        <div>
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="javascript:void(0)">Anasayfa</a></li>
                                <li class="breadcrumb-item active" aria-current="page">Kişiler</li>
                            </ol>
                        </div>
                    </div>


                    <div class="row ">
                        <div class="col-md-12 ">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Kişiler</h3>
                                </div>
                                <div class="card-body">

                                    <div class="table-responsive">
                                        <table class="table text-nowrap text-md-nowrap mb-0">
                                            <thead>
                                                <tr>
                                                    <th>Resim</th>
                                                    <th>İsim Soyisim</th>
                                                    <th>Görev</th>
                                                    <th class="text-center">Düzenle</th>
                                                    <th class="text-center">Sil</th>

                                                </tr>
                                            </thead>
                                            <tbody>

                                                <?php
                                                function kategoriListe($id, $tab)
                                                {
                                                    global $db, $sayfatip, $tablo;

                                                    $acilirid = $_GET['uid'];
                                                    $sayfatip = $_GET['st'];

                                                    $kod = "SELECT *, 
                                                    (SELECT COUNT(A.id) FROM $tablo AS A WHERE A.ustmenu=K.id and A.menuid=$acilirid) as altKategoriSayisi 
                                                    FROM $tablo AS K
                                                    WHERE K.ustmenu=:id and K.menuid=:acilirid and K.sayfatipi=:sayfatip 
                                                    ORDER BY menu_sira";

                                                    $stmt = $db->prepare($kod);
                                                    $stmt->execute(array('id' => $id, 'sayfatip' => $sayfatip, 'acilirid' => $acilirid));


                                                    while ($veri = $stmt->fetch(PDO::FETCH_ASSOC)) { ?>

                                                        <?php if ($veri['ustmenu'] == '0') { ?>
                                                            <tr id='Rowai<?= $veri["id"] ?>' class='usttablohover'>
                                                            <?php } else { ?>
                                                            <tr id='Rowai<?= $veri["id"] ?>' class='tablohover'>
                                                            <?php } ?>

                                                            <?php if ($veri['resim'] == '') { ?>
                                                                <td><img class='w-7 h-7' src='../images/images/resimyok.png'></td>
                                                            <?php } else { ?>
                                                                <td><img class='w-7 h-7' src='../<?= $veri["resim"] ?>'></td>
                                                            <?php } ?>

                                                            <?php if ($veri['ustmenu'] == '0') { ?>
                                                                <td>
                                                                <?php } else { ?>
                                                                <td class='' style='padding-left:<?= $tab ?>px !important'><i class='fe fe-corner-down-right'></i>
                                                                <?php } ?>
                                                                <?= kes($veri["baslik_tr"], 85) ?>
                                                                </td>

                                                                <td>
                                                                    <?= kes($veri["baslik1_tr"], 85) ?>
                                                                </td>

                                                                <td class='text-center'><a href='kisiekle.php?uid=<?= $acilirid ?>&id=<?= $veri["id"] ?>&st=<?= $veri["sayfatipi"] ?>'><i class='fe fe-edit fs-25 text-green'></i></a></td>
                                                                <td class='text-center'><a style='cursor: pointer' onclick='javascript:KisilerSilConfirm(<?= $veri["id"] ?>)' name='sil'><i class='fe fe-trash-2 fs-25 text-danger'></i></a></td>
                                                            </tr>

                                                    <?php if ($veri["altKategoriSayisi"] > 0) {
                                                            kategoriListe($veri["id"], $tab + 30);
                                                        }
                                                    }
                                                }

                                                kategoriListe(0, 10); ?>


                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>




                <div id="goster" class="main-container container-fluid <?= isset($_GET['id']) ? " goster" : "gizle"; ?>">
                    <div class="page-header">
                        <div>
                            <?php if (isset($_GET['id'])) { ?>
                                <a href="kisiekle.php?uid=<?php echo $_GET['uid']; ?>&st=<?php echo $st ?>"><button type="button" class="btn btn-green"><i class="fe fe-plus me-2"></i>Yeni Kişi Ekle</button></a>
                            <?php } ?>
                            <button type="button" id="goster" onclick="menuekle()" class="btn btn-btn btn-gray"><i class="fe fe-list me-2"></i>Kişileri Listele</button>
                        </div>
                        <div>
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="javascript:void(0)">Anasayfa</a></li>
                                <li class="breadcrumb-item active" aria-current="page">Kişi Ekle</li>
                            </ol>
                        </div>
                    </div>



                    <div class="row ">
                        <div class="col-md-12 ">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title"> <?= isset($_GET['id']) ? "Kişi Düzenle" : "Kişi Ekle" ?> </h3>
                                </div>
                                <div class="card-body">

                                    <div class="card-pay">
                                        <div class="tab-content">
                                            <div class="tab-pane show active" id="menuayar">
                                                <form action="" method="POST" enctype="multipart/form-data" id="demo-form2" data-parsley-validate class="form-horizontal form-label-left">

                                                    <input type="hidden" name="id" value="<?php echo $_GET['id']; ?>">
                                                    <input type="hidden" name="menuid" value="<?php echo $_GET['uid']; ?>">
                                                    <input type="hidden" name="sayfatipi" value="<?php echo $_GET['st']; ?>">
                                                    <input type="hidden" name="st" value="<?php echo $_GET['st']; ?>">
                                                    <input type="hidden" name="url" value="<?php echo $url; ?>">

                                                    <div class="row">
                                                        <div class="panel panel-primary">

                                                            <div class="row">
                                                                <!-- Menu Sıra -->
                                                                <div class="form-group col-md-4 col-sm-4 col-xs-12">
                                                                    <label class="control-label" for="first-name">Menu Sıra<span class="required">*</span>
                                                                    </label>
                                                                    <div>
                                                                        <?php if ($_GET["id"]) {
                                                                            $MenuSira = $MenuVeri["menu_sira"];
                                                                        } else {
                                                                            $MenuSira = $db->query("SELECT count(*) FROM $tablo")->fetchColumn() + 1;
                                                                        } ?>
                                                                        <input type="number" class="form-control col-md-12 col-xs-12" value="<?php echo $MenuSira ?>" name="menu_sira">
                                                                    </div>
                                                                </div>

                                                                <div class="form-group  col-md-4 col-sm-4 col-xs-12">
                                                                    <label class="control-label" for="first-name">Dahili<span class="required">*</span>
                                                                    </label>
                                                                    <div>
                                                                        <input type="text" class="form-control col-md-12 col-xs-12" value="<?php echo $MenuVeri['dahili'] ?>" name="dahili">
                                                                    </div>
                                                                </div>

                                                                <div class="form-group  col-md-4 col-sm-4 col-xs-12">
                                                                    <label class="control-label" for="first-name">E-Posta<span class="required">*</span>
                                                                    </label>
                                                                    <div>
                                                                        <input type="text" class="form-control col-md-12 col-xs-12" value="<?php echo $MenuVeri['mail'] ?>" name="mail">
                                                                    </div>
                                                                </div>


                                                                <div class="tab-menu-heading">
                                                                    <div class="tabs-menu1">
                                                                        <ul class="nav panel-tabs">

                                                                            <?php
                                                                            $dil = $db->prepare("SELECT * FROM dil WHERE aktif=1 ORDER BY id");
                                                                            $dil->execute();
                                                                            foreach ($dil as $dilcek) { ?>
                                                                                <li>
                                                                                    <a href="#<?php echo $dilcek['kisaltma']; ?>" class="<?= ($dilcek['id'] == 1) ? 'active' : '' ?>" data-bs-toggle="tab">
                                                                                        <i class="flag flag-<?php echo $dilcek['kisaltma']; ?>"></i>
                                                                                        <?php echo $dilcek['dil']; ?>
                                                                                    </a>
                                                                                </li>
                                                                            <?php } ?>
                                                                        </ul>
                                                                    </div>
                                                                </div>


                                                                <div class="panel-body tabs-menu-body">
                                                                    <div class="tab-content">
                                                                        <?php
                                                                        $dil = $db->prepare("SELECT * FROM dil WHERE aktif=1");
                                                                        $dil->execute();
                                                                        foreach ($dil as $dilcek) {
                                                                            $dil = $dilcek["kisaltma"]; ?>

                                                                            <div class="tab-pane <?= ($dilcek['id'] == 1) ? 'active' : '' ?>" id="<?php echo $dilcek['kisaltma']; ?>">

                                                                                <?php if ($dilcek['kisaltma'] != "tr") { ?>
                                                                                    <div class="btn btn-success ms-3 mt-4 mb-5 float-none"  onclick="cevir('<?php echo $dilcek['kisaltma']; ?>')">
                                                                                        <i class="fe fe-check me-2" style="color:#fff !important"></i>
                                                                                        <?php echo $dilcek['dil']; ?> diline otomatik çevir
                                                                                    </div>
                                                                                <?php } ?>


                                                                                <div class="form-group">
                                                                                    <label class="control-label col-12" for="first-name">İsim Soyisim</label>
                                                                                    <div class="col-12">
                                                                                        <input type="text" class="baslik form-control" dil="<?php echo $dilcek["dil"] ?>" name="baslik_<?php echo $dilcek['kisaltma']; ?>" value="<?php echo $MenuVeri["baslik_$dil"] ?>">
                                                                                    </div>
                                                                                </div>

                                                                                <div class="form-group">
                                                                                    <label class="control-label col-12" for="first-name">Görevi</label>
                                                                                    <div class="col-12">
                                                                                        <input type="text" class="form-control" name="baslik1_<?php echo $dilcek['kisaltma']; ?>" value="<?php echo $MenuVeri["baslik1_$dil"] ?>">
                                                                                    </div>
                                                                                </div>


                                                                                <div class="form-group">
                                                                                    <label class="control-label col-12" for="first-name">Açıklama </label>
                                                                                    <div class="col-12">
                                                                                        <textarea class="icerikalan<?php echo $dilcek['kisaltma']; ?>" id="icerik_<?php echo $dilcek['kisaltma']; ?>" name="icerik_<?php echo $dilcek['kisaltma']; ?>"><?php echo $MenuVeri["icerik_$dil"] ?></textarea>
                                                                                    </div>
                                                                                </div>


                                                                            </div>

                                                                        <?php } ?>


                                                                        <div class="col-12">
                                                                            <div class="row">
                                                                                <div class="col-6">
                                                                                    <div class="form-group">
                                                                                        <label class="control-label col-12" for="logo">Resim Seç</label>
                                                                                        <div class="col-12">
                                                                                            <input type="file" class="form-control col-12" name="resim" id="resim">
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="col-6">
                                                                                    <div style="margin-left:10px">
                                                                                        <?php
                                                                                        if (strlen($MenuVeri["resim"]) > 0) { ?>
                                                                                            <div id="thumbwrap">
                                                                                                <a class="thumb">
                                                                                                    <img style="width: 80px; height: 80px;" src="../<?php echo $MenuVeri["resim"] ?>">
                                                                                                    <span><img src="../<?php echo $MenuVeri["resim"] ?>"></span>
                                                                                                </a>
                                                                                            </div>
                                                                                            <label class="custom-control custom-checkbox-md mt-2 colorinput">
                                                                                                <input type="checkbox" class="custom-control-input" name="resimsil">
                                                                                                <input type="hidden" name="resimlink" value="<?php echo $MenuVeri["resim"] ?>">
                                                                                                <span class="custom-control-label">Resmi Sil</span>
                                                                                            </label>
                                                                                        <?php } else { ?>
                                                                                            <div class="mt-5">
                                                                                                <img style="width: 40px; height: 40px;" src="../images/logo-yok.webp">
                                                                                                Yüklü Resim Yok
                                                                                            </div>
                                                                                        <?php }
                                                                                        ?>
                                                                                    </div>
                                                                                </div>
                                                                            </div>

                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <hr>
                                                        <div class="col-12">
                                                            <button name="<?= isset($_GET['id']) ? " duzenle" : "ekle"; ?>" type="submit" class="btn btn-dark btn-lg"><i class="fa fa-floppy-o me-2" style="color:#fff !important"></i> Kaydet</button>
                                                        </div>
                                                    </div>
                                                </form>

                                            </div>
                                        </div>
                                    </div>
                                </div>



                            </div>
                        </div>
                    </div>
                </div>



        <?php
    } else {
        require 'yetkisizislem.php';
    }
    require 'footer.php';
} ?>