<script>
function myFunction() {
  var x = document.getElementById("bos_gecme").required;
  document.getElementById("demo").innerHTML = x;
}
</script>


<script>
    // extend jquery range validator to work for required checkboxes
    var defaultRangeValidator = $.validator.methods.range;
    $.validator.methods.range = function (value, element, param) {
        if (element.type === 'checkbox') {
            return element.checked;
        } else {
            return defaultRangeValidator.call(this, value, element, param);
        }
    }
</script>
<script>
    $(document).ready(function () {
        $.validator.unobtrusive.parse("#lisanssizUretimBasvuruForm");
        $("#UretimTesisiACKuruluGuc").inputmask({ regex: "^[0-9]\\d{0,9}(\\.\\d{1,3})?%?$" });
        $("#UretimTesisiDCKuruluGuc").inputmask({ regex: "^[0-9]\\d{0,9}(\\.\\d{1,3})?%?$" });
        $('#TelNo').inputmask('999-999-99-99');
        $('#TemsilTelNo').inputmask('999-999-99-99');
        $('#UretimSantralEnlem').inputmask('99.99999999');
        $('#UretimSantralBoylam').inputmask('99.99999999');

        CaptchaShowHide("lisanssizUretimBasvuruForm");

        $("#referans-tesisat-img").hide();
    });
    function TesisatEkle() {
        if ($("input:text.tesisatNumaralari").length == 11) {

        } else {
            $("#cloneTesisatDiv").contents().clone().appendTo("#tesisatListeDiv");
        }
    }
    var index = 1;

    function TesisatEkleDiger() {
        $("#cloneDigerAbonelikDiv").contents().clone().appendTo("#digerAbonelikDiv");
        $("#message").show();
    }

    function EnlemBoylamEkle() {
        if ($("input:text.enlemDegerleri").length == 5) {

        } else {
            $("#cloneEnlemBoylamDiv").contents().clone().appendTo("#enlemBoylamListeDiv");
            $('.enlemDegerleri').inputmask('99.99999999');
            $('.boylamDegerleri').inputmask('99.99999999');
        }
    }

    function EnlemBoylamKaldir(elem) {
        $(elem).parent().parent().parent().remove();
    }

    function TesisatKaldir(elem) {
        $(elem).parent().parent().remove();
    }

    function TesisatKaldirDiger(elem) {
        $(elem).parent().parent().parent().remove();
    }




    $("input[type='file']").change(function (e) {
        var fileId = $(this).attr("name");
        if ($(this).val() == "") {
            AddValidationError(fileId, "Lütfen dosya yükleyiniz.");
            return;
        }

        // Allowing file type
        var allowedExtensions = /(\.jpeg|\.tif|\.png|\.pdf)$/i;
        var allowedExtensionExcel = /(\.xlsx)$/i;

        if (fileId == "EigmTeknikDegerlendirmeFormuXlsx" || fileId == "EigmTeknikDegerlendirmeFormuXlsx2" ) {
            if (!allowedExtensionExcel.exec($(this).val()) || ($(this)[0].files[0].size / 1024 / 1024) > 3.0) {
                AddValidationError(fileId, "Lütfen dokümanı uygun formatta ve 3MB'ı geçmeyecek şekilde yükleyiniz (xlsx).");
                e.preventDefault();
                $("." + fileId + "Kaldir").click();
            } else {
                RemoveValidationError(fileId);
                fileInputImageShow(this);
            }
        }
        else {

            if (!allowedExtensions.exec($(this).val()) || ($(this)[0].files[0].size / 1024 / 1024) > 3.0) {
                AddValidationError(fileId, "Lütfen dokümanı uygun formatta ve 3MB'ı geçmeyecek şekilde yükleyiniz (jpeg, tif, pdf ya da png).");
                e.preventDefault();
                $("." + fileId + "Kaldir").click();
            } else {
                RemoveValidationError(fileId);
                fileInputImageShow(this);
            }
        }
    });



    // Dosya Görünüm Ayarları
    $("#SelectedBasvuruTuru").change(function () {
        var value = $("#SelectedBasvuruTuru").val();
        if (value == '0002') {
            $("#KwAltiGesTesisleriEvraklari").show();
            $("#LisanssizUretimTesisleriEvraklari").hide();
        } else {
            $("#KwAltiGesTesisleriEvraklari").hide();
            $("#LisanssizUretimTesisleriEvraklari").show();
        }
    });

    $("#SelectedBasvuruSahibiKisiTuru").change(function () {
        var value = $("#SelectedBasvuruSahibiKisiTuru").val();
        if (value != 'K') {
            $("#OrtaklikYapisiBelgeleri").show();
        } else {
            $("#OrtaklikYapisiBelgeleri").hide();
        }
    });
    $("#SelectedUygulamaYeri").change(function () {
        var value = $("#SelectedUygulamaYeri").val();

        //Arazi ve Arazi Çatı Cephe
        if (value == '03' || value == '05') {
            $("#TarimDisiAraziYazisi").show();
        } else {
            $("#TarimDisiAraziYazisi").hide();
        }

        if (value == '03' && parseInt($("#UretimTesisiACKuruluGuc").val()) >= 1000) {
            $("#CedRaporu").show();
        } else {
            $("#CedRaporu").hide();
        }
    });

    $("#UretimTesisiACKuruluGuc").change(function () {
        if ($("#SelectedUygulamaYeri").val() == '03' && parseInt($("#UretimTesisiACKuruluGuc").val()) >= 1000) {
            $("#CedRaporu").show();
        } else {
            $("#CedRaporu").hide();
        }
    });



    $("#SelectedKaynakTuru").change(function () {
        var value = $("#SelectedKaynakTuru").val();


        //Arazi ve Arazi Çatı Cephe
        if (value == 'GÜNEŞ' || value == 'RÜZGAR') {
            $("#EigmTeknikDegerlendirmeFormuPdf").show();
            $("#EigmTeknikDegerlendirmeFormuXlsx").show();
            $("#KoordinatliAplikasyonKrokisi").show();
            $("#FaaliyetYasaginaIliskinBeyanEk4").show();
        } else {
            $("#EigmTeknikDegerlendirmeFormuPdf").hide();
            $("#EigmTeknikDegerlendirmeFormuXlsx").hide();
            $("#KoordinatliAplikasyonKrokisi").hide();
            $("#FaaliyetYasaginaIliskinBeyanEk4").hide();
        }

        if (value == 'KOJENERASY' || value == 'MIKROJENER') {
            $("#KojenerasyonVerimlilikBelgesi").show();
        } else {
            $("#KojenerasyonVerimlilikBelgesi").hide();
        }

        if (value == 'JEOTERMAL' || value == 'HIDROLIK') {
            $("#YenilenebilirEnerjiHakBelgesi").show();
        } else {
            $("#YenilenebilirEnerjiHakBelgesi").hide();
        }
    });


    // Dosya Görünüm Ayarları
    $("#SelectedUretimTuketimYeri").change(function () {
        var value = $("#SelectedUretimTuketimYeri").val();
        if (value == 'AYNI') {
            $("#referans-tesisat-img").show();
        } else {
            $("#referans-tesisat-img").hide();
        }
    });

    $("#VergiKimlikNumarasi").keypress(function (e) {
        var charCode = (e.which) ? e.which : e.keyCode;
        if (charCode > 31 && (charCode < 48 || charCode > 57)) {
            return false;
        }
    });

    $("#SelectedIl").change(function () {

        $.ajax({
            async: true,
            url: '/adres/ilceler',
            type: 'GET',
            data: { "ilKodu": $("#SelectedIl").val().split('_')[0] },
            beforeSend: function () {

            },
            success: function (response) {

                if (response.state == 0) {
                    ShowMessage(response.message);
                }
                else if (response.state == 1) {
                    $("#SelectedIlce").html('');
                    var content = "";
                    content += "<option  value='' selected disabled data-subtext='İlçe'> İlçe </option>";
                    $.each(response.result.ilceListe, function (id, option) {
                        content += "<option data-subtext='İlçe' value='" + option.ilceKodu + "_" + option.ilceAdi + "'>" + option.ilceAdi + "</option>";
                    });
                    $("#SelectedIlce").html(content).selectpicker('refresh');
                }
            },
            error: function (response) {
            },
            complete: function (response) {
            }
        });


        $.ajax({
            async: true,
            url: '/LisanssizUretimBasvuru/GetIrtibatMerkezListe',
            type: 'GET',
            data: { "ilAdi": $("#SelectedIl").val().split('_')[1] },
            beforeSend: function () {

            },
            success: function (response) {

                $("#SelectedIrtibatMerkezi").html('');
                var content = "";
                $.each(response, function (id, option) {
                    content += "<option data-subtext='TM' value='" + option.value + "'>" + option.text + "</option>";
                });
                $("#SelectedIrtibatMerkezi").html(content).selectpicker('refresh');
            },
            error: function (response) {
            },
            complete: function (response) {
            }
        });

    });

    function formSuccess(response) {
        if (response.state == 0) {
            ShowMessage(response.message);
        }
        else if (response.state == 1) {
            window.location = response.url;
        }
        else if (response.state == 2) {
            $.each(response.data, function (index, element) {
                AddValidationError(element.fieldName, element.message);
            });
        }
        else if (response.state == 3) {
            $("#captchaErrorMesajDiv").show().html(response.message);
        }
    }

    function formComplete(response) {
        removeloadingGif();
        SetCaptchaValue("lisanssizUretimBasvuruForm");
        CaptchaShowHide("lisanssizUretimBasvuruForm");
        grecaptcha.reset();
    }

    function formOnBegin() {

        var conditionalFields = checkOtherFields();
        var validResult = $("#lisanssizUretimBasvuruForm").valid();

        loadingGif();
        if (!validResult) {
            removeloadingGif();
            return false;
        }

        if (!conditionalFields) {
            removeloadingGif();
            return false;
        }

        if (CaptchaValueCheck()) {
            $("#captchaErrorMesajDiv").hide().html("");
            return validResult;
        } else {
            removeloadingGif();
            $("#captchaErrorMesajDiv").show().html("Captcha Doğrulaması Başarısız...");
            return false;
        }
    }

    function checkOtherFields() {

        var result = true;

        debugger;

        var value = $("#SelectedAbonelik").val();
        if (value == '1') {

        }
        return result;
    }

</script>


<footer class="footer footer-desk">
    <div class="container">
        <div class="footer-center">
            <div class="social">
                <p>Bizi Takip Edin</p>
                <a href="https://www.facebook.com/kayseriosb1" target="_blank">
                    <img src="img/facebook_f.svg" alt="facebook" />
                </a>
                <a href="https://twitter.com/KayseriOSB" target="_blank">
                    <img src="img/twitter_f.svg" alt="twitter" />
                </a>
                <a href="https://instagram.com/KayseriOSB" target="_blank">
                    <img src="img/instagram_f.svg" alt="instagram" />
                </a>
		<br>
            </div>
        </div>
    </div>
</footer>

<footer class="footer footer-mobile">
    <div class="container">
        <div class="social">
            <p>Bizi Takip Edin</p>
            <a href="https://www.facebook.com/kayseriosb1" target="_blank">
                <img src="img/facebook_f.svg" alt="facebook" />
            </a>
            <a href="https://twitter.com/KayseriOSB" target="_blank">
                <img src="img/twitter_f.svg" alt="twitter" />
            </a>
            <a href="https://instagram.com/KayseriOSB" target="_blank">
                <img src="img/instagram_f.svg" alt="instagram" />
            </a>
			<br>
        </div>
        <hr />
    </div>
</footer>


<script src="css/bootstrap.bundle.min.js"></script>
<script src="css/bootstrap-select.min.js"></script>
<script src="css/js.cookie.min.js"></script>


<div class="loading-bg">
    <div class="center">
        <h3>Yükleniyor</h3>
        <div class="spinner-border" role="status"></div>
        <div class="loading-img"><img src="img/loading-img.svg" alt="yükleniyor" /> </div>
    </div>
</div>


<script src="css/main.js"></script>


    <script>
        function Loading() {
            $('.loading-bg').show();
            $('body').addClass('body-loading');
            return false;
        }
        function RemoveLoading() {
            $('.loading-bg').hide();
            $('body').removeClass('body-loading');
            return false;
        }
    </script>
	

</body>
</html>