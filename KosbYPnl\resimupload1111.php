<?php

namespace Verot\Upload;

require 'class.upload.php';

$dir_pics = (isset($_GET['pics']) ? $_GET['pics'] : $dir_dest);

$log = '';

if (isset($_FILES['resim'])) {


    $files = array();
    foreach ($_FILES['resim'] as $k => $l) {
        foreach ($l as $i => $v) {
            if (!array_key_exists($i, $files))
                $files[$i] = array();
            $files[$i][$k] = $v;
            $resimtmpname = $files[$i]['tmp_name'];
        }
    }

    foreach ($files as $file) {

        $handle = new Upload($file);
        if ($handle->uploaded) {

            $size = getimagesize($resimtmpname);
            $width = $size[0];
            $height = $size[1];

            $new_width = $width * $kucultmeorani / 100;
            $new_height = $height * $kucultmeorani / 100;

            $handle->image_convert = 'webp';
            $handle->file_max_size = $resimboyut * 1000000;
            $handle->webp_quality = $kalite;
            $handle->allowed = array('image/*');

            if ($resimkucultme == 1) {
                if ($dikeylimit <= $height & $yataylimit <= $width) {
                    $handle->image_resize = true;
                    $handle->image_x = $new_width;
                    $handle->image_y = $new_height;
                }
            }



            $handle->process('../' . $yol);

            if ($handle->processed) {
                $isim = $handle->file_dst_name;
                $sql = "INSERT INTO resimler (menu_id, sayfatip, $dilSutunlar, link, yaziiciresim) VALUES (?, ?, $dilPlaceholders, ?, ?)";
                $veriler = array_merge([$id, $sayfatipi], array_fill(0, count($diller), SEOdosya($isim)), [$yol . SEOdosya($isim), $yaziiciresim]);
                $resimekle = $db->prepare($sql);
                $resimekle->execute($veriler);
                rename("../$yol/$isim", "../$yol/" . SEOdosya($isim) . "");
                session_start();
                $_SESSION['resimyuklendi'] = 1;
                header("Location:" . $yonlenurl . "");
            } else {
                session_start();
                $_SESSION['resimhata'] = 1;
                header("Location:" . $yonlenurl . "");
            }

        }

        $log .= $handle->log . '<br />';
    }
}
