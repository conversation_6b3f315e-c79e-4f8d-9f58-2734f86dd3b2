

.chatbox-popup .chat {
    margin-top: auto;
    margin-bottom: auto;
}

.chatbox-popup .card {
    height: 500px;
    border-radius: 15px !important;
    background-color: rgba(0,0,0,0.4) !important;
    word-break: break-word;
}

.chatbox-popup .contacts_body {
    padding: 0.75rem 0 !important;
    overflow-y: auto;
    white-space: nowrap;
}

.chatbox-popup .msg_card_body {
    overflow-y: auto;
}

.chatbox-popup .card-header {
    border-radius: 15px 15px 0 0 !important;
    border-bottom: 0 !important;
}

.chatbox-popup .card-footer {
    border-radius: 0 0 15px 15px !important;
    border-top: 0 !important;
}

.chatbox-popup .container {
    align-content: center;
}

.chatbox-popup .search {
    border-radius: 15px 0 0 15px !important;
    background-color: rgba(0,0,0,0.3) !important;
    border: 0 !important;
    color: white !important;
}

    .chatbox-popup .search:focus {
        box-shadow: none !important;
        outline: 0px !important;
    }

.chatbox-popup .type_msg {
    background-color: rgba(0,0,0,0.3) !important;
    border: 0 !important;
    color: white !important;
    height: 60px !important;
    overflow-y: auto;
}

    .chatbox-popup .type_msg:focus {
        box-shadow: none !important;
        outline: 0px !important;
    }

.chatbox-popup .attach_btn {
    border-radius: 15px 0 0 15px !important;
    background-color: rgba(0,0,0,0.3) !important;
    border: 0 !important;
    color: white !important;
    cursor: pointer;
}

.chatbox-popup .send_btn {
    border-radius: 0 15px 15px 0 !important;
    background-color: rgba(0,0,0,0.3) !important;
    border: 0 !important;
    color: white !important;
    cursor: pointer;
}

.chatbox-popup .search_btn {
    border-radius: 0 15px 15px 0 !important;
    background-color: rgba(0,0,0,0.3) !important;
    border: 0 !important;
    color: white !important;
    cursor: pointer;
}

.chatbox-popup .contacts {
    list-style: none;
    padding: 0;
}

    .chatbox-popup .contacts li {
        width: 100% !important;
        padding: 5px 10px;
        margin-bottom: 15px !important;
    }

.chatbox-popup .active {
    background-color: rgba(0,0,0,0.3);
}

.chatbox-popup .user_img {
    height: 70px;
    width: 70px;
    border: 1.5px solid #f5f6fa;
}

.chatbox-popup .user_img_msg {
    height: 40px;
    width: 40px;
    border: 1.5px solid #f5f6fa;
}

.chatbox-popup .img_cont {
    position: relative;
    height: 70px;
    width: 70px;
}

.chatbox-popup .img_cont_msg {
    height: 40px;
    width: 40px;
}

.chatbox-popup .video_cam {
    margin-left: 50px;
    margin-top: 5px;
}

    .chatbox-popup .video_cam span {
        color: white;
        font-size: 20px;
        cursor: pointer;
        margin-right: 20px;
    }

.chatbox-popup .msg_time {
    position: absolute;
    left: -0px;
    bottom: -20px;
    color: rgb(0 0 0 / 50%);
    font-size: 10px;
    margin-top: 15px;
    width: 30px;
}

.chatbox-popup .msg_time_send {
    position: absolute;
    right: -0px;
    bottom: -20px;
    color: rgb(0 0 0 / 50%);
    font-size: 10px;
    width: 32px;
}

.chatbox-popup .msg_head {
    position: relative;
}

.chatbox-popup #action_menu_btn {
    position: absolute;
    right: 10px;
    top: 10px;
    color: white;
    cursor: pointer;
    font-size: 20px;
}

.chatbox-popup .action_menu {
    z-index: 1;
    position: absolute;
    padding: 15px 0;
    background-color: rgba(0,0,0,0.5);
    color: white;
    border-radius: 15px;
    top: 30px;
    right: 15px;
    display: none;
}

    .chatbox-popup .action_menu ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

        .chatbox-popup .action_menu ul li {
            width: 100%;
            padding: 10px 15px;
            margin-bottom: 5px;
        }

            .chatbox-popup .action_menu ul li i {
                padding-right: 10px;
            }

            .chatbox-popup .action_menu ul li:hover {
                cursor: pointer;
                background-color: rgba(0,0,0,0.2);
            }

@media(max-width: 576px) {
    .chatbox-popup .contacts_card {
        margin-bottom: 15px !important;
    }
}


.chatbox-popup #chatLog {
    height: 90%;
    overflow-y: auto;
}

.chatbox-popup .msg_cotainer {
    margin-right: 10px;
    margin-bottom: auto;
    border-radius: 25px;
    margin-left: 10px;
    margin-top: auto;
    position: relative;
    padding: .5rem;
    border: 4px solid transparent;
    background: linear-gradient(to right, white, white), linear-gradient(15deg, #edaf42, #faeb4f);
    background-clip: padding-box, border-box;
    background-origin: padding-box, border-box;
    max-width: 75%;
}

.chatbox-popup .msg_cotainer_pre {
    padding: 0px !important;
    overflow: hidden;
    white-space: pre-wrap;
    font-family: -apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol";
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    text-align: left;
    margin: auto;
}

.chatbox-popup .msg_cotainer_send {
    margin-bottom: auto;
    margin-right: 10px;
    border-radius: 25px;
    position: relative;
    padding: .5rem;
    border: 4px solid transparent;
    background: linear-gradient(to right, white, white), linear-gradient(15deg, #e6e6e6, #f1f1f1);
    background-clip: padding-box, border-box;
    background-origin: padding-box, border-box;
    white-space: pre-wrap;
    word-break: break-word;
    max-width: 75%;
}

.fa-chevron-left:before {
    content: "\f053";
}

.fa {
    display: inline-block;
    /* font: normal normal normal 14px/1 FontAwesome; */
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
}

.mb-4 {
    margin-bottom: 1.5rem !important;
}

.justify-content-end {
    -ms-flex-pack: end !important;
    justify-content: flex-end !important;
}

.d-flex {
    display: flex !important;
}

.justify-content-start {
    justify-content: flex-start !important;
}

@font-face {
    font-family: 'Lato';
    font-style: normal;
    font-weight: 400;
    src: local('Lato Regular'), local('Lato-Regular'), url(https://fonts.gstatic.com/s/lato/v16/S6uyw4BMUTPHjxAwXjeu.woff2) format('woff2');
    unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
    font-display: swap;
}


button {
    color: inherit;
    background-color: transparent;
    border: 0;
    outline: 0 !important;
    cursor: pointer;
}

    button.chatbox-close {
        position: fixed;
        bottom: 0;
        right: 0;
        width: 52px;
        height: 52px;
        color: #fff;
        background-color: #9884b9c2;
        background-position: center center;
        background-repeat: no-repeat;
        box-shadow: 12px 15px 20px 0 rgba(46, 61, 73, 0.15);
        border: 0;
        border-radius: 50%;
        cursor: pointer;
        display: none;
        margin: 16px calc(2 * 16px + 52px) 16px 16px;
    }

#userBox {
    border: 0px solid #ababab00 !important;
    border-radius: 30px;
    box-sizing: border-box;
    width: 100%;
    margin: 0;
    height: 35px;
    /*height: calc(16px + 16px / 2); */
    padding: 0 calc(16px / 2);
    font-family: inherit;
    font-size: 16px;
    /* line-height: calc(16px + 16px / 2);*/
    color: #888;
    background-color: none;
    outline: 0 !important;
    resize: none;
    /*overflow: hidden; */
}

    #userBox::-webkit-#userBox-placeholder {
        color: #888;
    }

    #userBox::-moz-placeholder {
        color: #888;
    }

    #userBox:-ms-#userBox-placeholder {
        color: #888;
    }

.chatbox-popup {
    position: absolute;
    box-shadow: 5px 5px 25px 0 rgba(46, 61, 73, 0.2);
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    flex-direction: column;
    bottom: 30px;
    right: 60px;
    width: 377px;
    height: auto;
    background-color: #fff;
    border-radius: 16px;
    z-index: 100 !important
}

    .chatbox-popup .chatbox-popup__header {
        box-sizing: border-box;
        display: flex;
        width: 100%;
        height: 71px;
        color: #fff;
        background: linear-gradient(90deg, rgba(240,189,69,1) 10%, rgba(246,250,79,1) 66%);
        -webkit-box-align: center;
        align-items: center;
        justify-content: space-around;
        border-top-right-radius: 12px;
        border-top-left-radius: 12px;
    }

        .chatbox-popup .chatbox-popup__header .chatbox-popup__avatar {
            margin-top: -32px;
            background-color: #0360a5;
            border: 5px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
        }

    .chatbox-popup .chatbox-popup__main {
        box-sizing: border-box;
        width: 100%;
        padding: calc(2 * 16px) 16px;
        color: #000;
    }

    .chatbox-popup .chatbox-popup__footer {
        background-color: #f1f1f1;
        box-sizing: border-box;
        display: flex;
        width: 100%;
        padding: 16px;
        border-top: 1px solid #e6e6e6;
        -webkit-box-align: center;
        align-items: center;
        justify-content: space-around;
        border-bottom-right-radius: 12px;
        border-bottom-left-radius: 12px;
        margin: 0;
    }

    .chatbox-popup .chatbox-panel {
        display: flex;
        position: absolute;
        box-shadow: 5px 5px 25px 0 rgba(46, 61, 73, 0.2);
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        flex-direction: column;
        /* display: none; */
        top: 0;
        right: 0;
        bottom: 0;
        width: 377px;
        background-color: #fff;
    }

        .chatbox-popup .chatbox-panel .chatbox-panel__header {
            box-sizing: border-box;
            display: -webkit-box;
            display: flex;
            width: 100%;
            padding: 16px;
            color: #fff;
            background-color: #0360a5;
            -webkit-box-align: center;
            align-items: center;
            justify-content: space-around;
            -webkit-box-flex: 0;
            flex: 0 0 auto;
        }

        .chatbox-popup .chatbox-panel .chatbox-panel__main {
            box-sizing: border-box;
            width: 100%;
            padding: calc(2 * 16px) 16px;
            line-height: calc(16px + 16px / 2);
            color: #888;
            text-align: center;
            -webkit-box-flex: 1;
            flex: 1 1 auto;
        }

        .chatbox-popup .chatbox-panel .chatbox-panel__footer {
            box-sizing: border-box;
            display: -webkit-box;
            display: flex;
            width: 100%;
            padding: 16px;
            border-top: 1px solid #ddd;
            -webkit-box-align: center;
            align-items: center;
            justify-content: space-around;
            -webkit-box-flex: 0;
            flex: 0 0 auto;
        }

    .chatbox-popup .user {
        word-wrap: break-word;
        float: right;
        width: 100%;
        margin-top: 15px;
        border-radius: 25px;
        background-color: #f0ffdc;
        max-width: 85%;
        text-align: left;
        padding: 5px;
    }

    .chatbox-popup .bot {
        word-wrap: break-word;
        text-align: left;
        float: left;
        width: 100%;
        margin-top: 15px;
        border-radius: 25px;
        background-color: #f3dcff;
        max-width: 85%;
        padding: 5px;
    }

.fa-user-circle:before {
    content: "\f2bd";
    visibility: hidden;
}

#userBox.placeholder {
    text-align: center;
    color: red;
    size: 100px;
}

.chatbox-popup .msg_cotainer .msg_cotainer_pre {
    background-color: #ffffff00 !important;
    border: 0px !important;
}

.chatbox-popup .ms-webpartPage-root {
    border-spacing: 0px !important;
}

.chatbox-popup .ms-webpartzone-cell {
    margin: 0px !important;
}

* {
    box-sizing: border-box;
}


.chatbox-popup .rating {
    display: flex;
    width: 100%;
    justify-content: center;
    overflow: hidden;
    flex-direction: row-reverse;
    height: 100px;
    position: relative;
}

.chatbox-popup .feedback_x {
    margin-bottom: 24px;
}

.chatbox-popup .rating-0 {
    filter: grayscale(100%);
}

.chatbox-popup .rating > input {
    display: none;
}

.chatbox-popup .rating > label {
    cursor: pointer;
    width: 35px;
    height: 35px;
    margin-top: auto;
    background-image: url("https://www.baskentedas.com.tr/site-dosya/dosya-on-izleme/9257");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 67%;
    transition: .3s;
}

.chatbox-popup .rating > input:checked ~ label,
.chatbox-popup .rating > input:checked ~ label ~ label {
    background-image: url("https://www.baskentedas.com.tr/site-dosya/dosya-on-izleme/9258");
}


.chatbox-popup .rating > input:not(:checked) ~ label:hover,
.chatbox-popup .rating > input:not(:checked) ~ label:hover ~ label {
    background-image: url("https://www.baskentedas.com.tr/site-dosya/dosya-on-izleme/9259");
}

.chatbox-popup .emoji-wrapper {
    width: 100%;
    text-align: center;
    height: 100px;
    overflow: hidden;
    position: absolute;
    top: 0;
    left: 0;
}

    .chatbox-popup .emoji-wrapper:before,
    .chatbox-popup .emoji-wrapper:after {
        content: "";
        height: 15px;
        width: 100%;
        position: absolute;
        left: 0;
        z-index: 1;
    }

.chatbox-popup .emoji {
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: .3s;
}

    .chatbox-popup .emoji > svg {
        margin: 25px 0;
        width: 50px;
        height: 50px;
        flex-shrink: 0;
    }

#rating-1:checked ~ .emoji-wrapper > .emoji {
    transform: translateY(-100px);
}

#rating-2:checked ~ .emoji-wrapper > .emoji {
    transform: translateY(-200px);
}

#rating-3:checked ~ .emoji-wrapper > .emoji {
    transform: translateY(-300px);
}

#rating-4:checked ~ .emoji-wrapper > .emoji {
    transform: translateY(-400px);
}

#rating-5:checked ~ .emoji-wrapper > .emoji {
    transform: translateY(-500px);
}

.chatbox-popup .feedback {
    max-width: 360px;
    background-color: #fff;
    width: 100%;
    padding: 15px;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    align-items: center;
    box-shadow: 0 4px 30px rgba(0,0,0,.05);
}

#rating-1:hover ~ .emoji-wrapper > .emoji {
    transform: translateY(-100px);
}

#rating-2:hover ~ .emoji-wrapper > .emoji {
    transform: translateY(-200px);
}

#rating-3:hover ~ .emoji-wrapper > .emoji {
    transform: translateY(-300px);
}

#rating-4:hover ~ .emoji-wrapper > .emoji {
    transform: translateY(-400px);
}

#rating-5:hover ~ .emoji-wrapper > .emoji {
    transform: translateY(-500px);
}

.survey-btn {
    border: 2px solid #cbcaca;
    background-color: #fafafa;
    padding: 8px;
    margin-top: 30px;
    cursor: pointer;
    border-bottom-width: 6px;
    border-radius: 3px;
    border-right-width: 3px;
    border-left-width: 3px;
}

    .survey-btn:hover {
        background-color: rgb(216, 177, 30, 0.8);
        transition: .3s;
    }


.rd1 {
    margin-right: 5px;
}

.buttons {
    display: flex;
}

.radio-custom {
    opacity: 0;
    position: absolute;
}

.radio-custom, .radio-custom-label {
    display: inline-block;
    vertical-align: middle;
    margin: 5px;
    cursor: pointer;
}

.radio-custom-label {
    position: relative;
}

.radio-custom + .radio-custom-label:before {
    content: '';
    border: 2px solid #ddd;
    display: inline-block;
    vertical-align: middle;
    width: 20px;
    height: 20px;
    padding: 2px;
    margin-right: 10px;
    text-align: center;
}

.radio-custom + .radio-custom-label:before {
    border-radius: 50%;
}

.radio-custom:checked + .radio-custom-label:before {
    content: "\f00c";
    font-family: "Font Awesome 5 Free";
    color: #D8B11E;
    font-weight: 600;
}

.zackai-survey {
    list-style-type: none;
    padding: 0;
    float: left;
    width: 100%;
    justify-content: center;
    display: flex;
    height: 90px;
    margin-top: 30px;
}


    .zackai-survey li {
        float: left;
        width: 100px;
        zoom: 0.86;
        justify-content: center;
        display: flex;
    }

        .zackai-survey li:hover {
            zoom: 0.95;
        }



        .zackai-survey li a {
            box-shadow: 3px 4px 4px 3px rgb(52 52 52 / 11%);
            background: #fff;
            border-radius: 50%;
            padding: 8px;
            cursor: pointer;
            width: 56px;
            height: 56px;
            float: left;
        }


            .zackai-survey li a.active {
                background: #ff9500;
            }