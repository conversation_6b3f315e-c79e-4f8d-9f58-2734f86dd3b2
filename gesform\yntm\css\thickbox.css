/* ----------------------------------------------------------------------------------------------------------------*/
/* ---------->>> global settings needed for thickbox <<<-----------------------------------------------------------*/
/* ----------------------------------------------------------------------------------------------------------------*/
*{padding: 0; margin: 0;}

/* ----------------------------------------------------------------------------------------------------------------*/
/* ---------->>> thickbox specific link and font settings <<<------------------------------------------------------*/
/* ----------------------------------------------------------------------------------------------------------------*/
#TB_window {
	position: fixed;
	font: 12px Arial, Helvetica, sans-serif;
	color: darkgray;
	text-decoration: none;
	font-weight: bold;
}

#TB_secondLine {
	font: 10px Arial, Helvetica, sans-serif;
	color:#666666;
}




/* ----------------------------------------------------------------------------------------------------------------*/
/* ---------->>> thickbox settings <<<-----------------------------------------------------------------------------*/
/* ----------------------------------------------------------------------------------------------------------------*/
#TB_overlay {
	position: fixed;
	z-index:100;
	top: 0px;
	left: 0px;
	height:100%;
	width:100%;
}

.TB_overlayMacFFBGHack {background: url(macFFBgHack.png) repeat;}
.TB_overlayBG {
	background-color:#000;
	filter:alpha(opacity=75);
	-moz-opacity: 0.75;
	opacity: 0.75;
}

* html #TB_overlay { /* ie6 hack */
     position: absolute;
     height: expression(document.body.scrollHeight > document.body.offsetHeight ? document.body.scrollHeight : document.body.offsetHeight + 'px');
}

#TB_window {
	position: fixed;
	background-color:#454545;
	z-index: 102;
	color: #c7e6f0;
	display:none;
	border: 4px solid #fff;
	border-radius: 10px;
	text-align:center;
	top:50%;
	left:50%;
}

* html #TB_window { /* ie6 hack */
position: absolute;
margin-top: expression(0 - parseInt(this.offsetHeight / 2) + (TBWindowMargin = document.documentElement && document.documentElement.scrollTop || document.body.scrollTop) + 'px');

}

#TB_window img#TB_Image {
	display:block;
	margin: 5px 0 0 0px;
	border-right: 1px solid #ccc;
	border-bottom: 1px solid #ccc;
	border-top: 1px solid #666;
	border-left: 1px solid #666;
}

#TB_caption{
	height:25px;
	padding:7px 30px 10px 25px;
	float:left;
}

#TB_closeWindow{
	height:12px;
	padding:11px 25px 0px 0;
	float:right;
}

#TB_closeAjaxWindow{
	padding:9px 10px 0px 0;
	margin-bottom:1px;
	text-align:right;
	float:right;
}
#TB_closeAjaxWindow a:link {color: orange; text-decoration:none;}
#TB_closeAjaxWindow a:visited {color: orange; text-decoration:none;}
#TB_closeAjaxWindow a:hover {color: red; font-weight: bold;}
#TB_closeAjaxWindow a:active {color: orange; text-decoration:none;}
#TB_closeAjaxWindow a:focus{color: orange; text-decoration:none;}

#TB_ajaxWindowTitle{
	float:left;
	padding:3px 0 0px 10px;
	margin-bottom:1px;
	font-size: 15px;
	font-weight: bold;
}



#TB_title{
	background-color:#454545;
	height:12px;
	border-radius: 5px;
}


#TB_ajaxContent{
	background: url('../resimler/zemin5.jpg') repeat fixed;
	background-color:#000;
	clear:both;
	padding:2px 15px 15px 15px;
	overflow:auto;
	text-align:justify;
	line-height:1.4em;
	color: #000;
}

#TB_ajaxContent.TB_modal{
	padding:1px;
}

#TB_ajaxContent p{
	padding:1px 1px 1px 13px;
}

#TB_load{
	position: fixed;
	display:none;
	height:13px;
	width:208px;
	z-index:103;
	top: 50%;
	left: 50%;
	margin: -6px 0 0 -104px; /* -height/2 0 0 -width/2 */
}

* html #TB_load { /* ie6 hack */
position: absolute;
margin-top: expression(0 - parseInt(this.offsetHeight / 2) + (TBWindowMargin = document.documentElement && document.documentElement.scrollTop || document.body.scrollTop) + 'px');
}

#TB_HideSelect{
	z-index:99;
	position:fixed;
	top: 0;
	left: 0;
	background-color:#fff;
	border:none;
	filter:alpha(opacity=0);
	-moz-opacity: 0;
	opacity: 0;
	height:100%;
	width:100%;
}

* html #TB_HideSelect { /* ie6 hack */
     position: absolute;
     height: expression(document.body.scrollHeight > document.body.offsetHeight ? document.body.scrollHeight : document.body.offsetHeight + 'px');
}

#TB_iframeContent{
	clear:both;
	border:none;
	margin-bottom:-1px;
	margin-top:1px;
	_margin-bottom:1px;
}
