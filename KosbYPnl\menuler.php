<?php
include "../db/baglanti.php";
include "logfonksiyon.php";

if (!isset($_SESSION['kullanici_adi'])) {
    header("Location:giris.php");
    exit;
}

$kullanici_adi = $_SESSION['kullanici_adi'];
$kullaciSor = $db->prepare("SELECT * FROM yonetim WHERE kullanici_adi = ?");
$kullaciSor->execute(array($kullanici_adi));
$kullaniciCek = $kullaciSor->fetch(PDO::FETCH_ASSOC);

if (!$kullaniciCek) {
    header("Location:giris.php");
    exit;
}

extract($_GET);
$tablo = "sayfalar";

if ($_POST['islem'] == "anasayfadagoster") {
    $id = (int) $_POST['id'];
    $durum = (int) $_POST['durum'];
    $sutun = $_POST['sutun'];
    $sorgu = $db->prepare("UPDATE $tablo SET $sutun=$durum WHERE id=$id");
    $sorgu->execute();
}

if ($_GET['islem'] == 1) {

    $dosyaAlanlari = ['resim', 'resim1', 'resim2', 'resim3', 'video', 'dosya', 'dosya2']; // dinamik olarak alınacak dosya alanlarını belirtin
    try {
        $KayitVarmi = $db->query("SELECT count(*) FROM $tablo WHERE ustmenu=$id")->fetchColumn();
        if ($KayitVarmi == 0) {
            // İç resimleri ve dosyaları sil
            $icresimcek = $db->prepare("SELECT " . implode(',', $dosyaAlanlari) . " FROM $tablo WHERE id=:id");
            $icresimcek->execute(array("id" => $id));
            foreach ($icresimcek as $icresim) {
                foreach ($icresim as $dosya) {
                    unlink('../' . $dosya);
                }
            }

            // Tablodan sil
            $menusil = $db->prepare("DELETE FROM $tablo WHERE id=:id");
            $menusil->execute(array("id" => $id));

            // Resimleri sil
            $resimcek = $db->prepare("SELECT link FROM resimler WHERE menu_id=:id");
            $resimcek->execute(array("id" => $id));
            foreach ($resimcek as $resim) {
                unlink('../' . $resim['link']);
            }
            $resimsil = $db->prepare("DELETE FROM resimler WHERE menu_id=:id");
            $resimsil->execute(array("id" => $id));

            // Dokümanları sil
            $dokumancek = $db->prepare("SELECT link FROM dokuman WHERE menu_id=:id");
            $dokumancek->execute(array("id" => $id));
            foreach ($dokumancek as $dokuman) {
                unlink('../' . $dokuman['link']);
            }
            $dokumansil = $db->prepare("DELETE FROM dokuman WHERE menu_id=:id");
            $dokumansil->execute(array("id" => $id));

            echo "OK";
        } else {
            echo "VAR";
        }
    } catch (PDOException $e) {
        Hata("Hata: " . $e->getMessage());
    }
} else {
    //MENÜ SİLME İŞLEMİ YOKSA SAYFAYI YÜKLÜYORUZ -- EĞER SİLME İŞLEMİ VARSA AŞAĞISI YÜKLENMİYOR... YANİ ASIL SAYFA KODLARI AŞAĞIDAN BAŞLIYOR.

    require 'header.php';
    require 'solmenu.php';
    include "../db/baglan.php";

    $sayfatipi = $_POST['sayfatip'];
    $yonlenurl = $_POST['url'];

    $dilSorgusu = $db->query("SELECT kisaltma FROM dil");
    $diller = $dilSorgusu->fetchAll(PDO::FETCH_COLUMN);

    /*********************************************************** EKLEme Alanı ***********************************************************************/


    if (isset($_POST['ekle'])) {
        if ($_POST['ekleyen'] != $_SESSION['kullanici_id']) {
            Hata("Başkasının adına menü ekleyemez ve düzenleyemezsiniz.");
            exit;
        }

        $yenimenu = 1;

        include 'icresimyukle.php';

        $fields = array('manset', 'ebelediye', 'anasayfa', 'etkinlik');
        foreach ($fields as $field) {
            ${$field} = ($_POST[$field] == "on") ? 1 : 0;
        }

        foreach ($_POST as $column => $value) {
            if (
                $column != "url" & $column != "ekle" & $column != "id" &
                $column != "resimlink" & $column != "resimsil" &
                $column != "resim1link" & $column != "resim1sil" &
                $column != "resim2link" & $column != "resim2sil" &
                $column != "resim3link" & $column != "resim3sil" &
                $column != "videolink" & $column != "videosil" &
                $column != "dosyalink" & $column != "dosyasil" &
                $column != "dosya2link" & $column != "dosya2sil" &
                $column != "manset" & $column != "oncelik" &
                $column != "ebelediye" & $column != "anasayfa"
                & $column != "etkinlik"
            ) {

                if (strpos($column, "icerik_") !== false) {
                    $value = Base64Cevir($value, '../images/yazi/');
                    $value = YazidakiResimleriIndir($value);
                    $value = str_replace('&nbsp;', '', $value);
                }

                if (strpos($column, "baslik_") !== false) {
                    $value = str_replace('"', "'", $value);
                }

                $value = addslashes($value);

                // Sütun varlığını kontrol et
                $SutunVarmi = $db->prepare("DESCRIBE $tablo");
                $SutunVarmi->execute();
                $Sutunlar = $SutunVarmi->fetchAll(PDO::FETCH_COLUMN);
                if (!in_array($column, $Sutunlar)) {
                    // Sütun yoksa oluştur
                    $db->exec("ALTER TABLE $tablo ADD COLUMN $column TEXT");
                }

                $keys[] = "{$column}";
                $values[] = "'{$value}'";
            }
        }


        $seoSutunlar = [];
        $seoDegerleri = [];
        foreach ($diller as $dil) {
            $seoSutunlar[] = "seo_$dil";
            $seoDegerleri[] = "'" . SEOLink($_POST["baslik_$dil"]) . "'";
        }

        $sutunlar = implode(",", $keys) . "," . implode(",", $seoSutunlar);
        $degerler = implode(",", $values) . "," . implode(",", $seoDegerleri);

        try {
            $sql = "INSERT INTO $tablo ($sutunlar, resim, resim1, resim2, resim3, video, dosya, dosya2, manset, anasayfa, etkinlik, ebelediye) 
        VALUES ($degerler, '$resim', '$resim1', '$resim2', '$resim3', '$video', '$dosya', '$dosya2', '$manset', '$anasayfa', '$etkinlik', '$ebelediye')";

            $kayit_ekle = $db->exec($sql);

            Basarili("Menü başarıyla eklendi.");
        } catch (PDOException $e) {
            // Veritabanı hatası oluştuğunda burası çalışır
            Hata("Veritabanı hatası: <br>" . $e->getMessage());
        } catch (Exception $e) {
            // Diğer hatalar için burası çalışır
            Hata("Beklenmeyen bir hata oluştu: <br>" . $e->getMessage());
        }
    }



    /*********************************************************** Menü Ekleme Alanı Sonu ***********************************************************************/


    /*********************************************************** Menü Düzenleme Alanı ***********************************************************************/
    if (isset($_POST['duzenle'])) {
        $id = $_POST['id'];
        $yenimenu = 0;

        if ($_POST['sonduzenleyen'] != $_SESSION['kullanici_id']) {
            echo $yetkihata;
            exit;
        }

        $fields = array('manset', 'oncelik', 'ebelediye', 'anasayfa', 'etkinlik');
        foreach ($fields as $field) {
            ${$field} = ($_POST[$field] == "on") ? 1 : 0;
        }

        $fields = array(
            'resim' => 'resimlink',
            'resim1' => 'resim1link',
            'resim2' => 'resim2link',
            'resim3' => 'resim3link',
            'video' => 'videolink',
            'dosya' => 'dosyalink',
            'dosya2' => 'dosya2link'
        );

        foreach ($fields as $postKey => $linkVar) {
            if ($_POST[$postKey . 'sil'] == "on") {
                $columnName = str_replace('sil', '', $postKey); // 'sil' son eki kaldırarak sütun adını elde ediyoruz
                $logosil = $db->prepare("UPDATE $tablo SET $columnName = '' WHERE id = :id");
                $logosil->execute(array("id" => $id));
                unlink("../" . $_POST[$linkVar]);
            }
        }


        include 'icresimyukle.php';


        $setIfadesi = "";

        foreach ($diller as $dil) {
            $setIfadesi .= "seo_$dil = '" . SEOLink($_POST["baslik_$dil"]) . "', ";
        }


        foreach ($_POST as $column => $value) {
            if (
                $column != "url" &&
                $column != "duzenle" &&
                !in_array($column, [
                    "resimlink",
                    "resimsil",
                    "resim1link",
                    "resim1sil",
                    "resim2link",
                    "resim2sil",
                    "resim3link",
                    "resim3sil",
                    "videolink",
                    "videosil",
                    "dosyalink",
                    "dosyasil",
                    "dosya2link",
                    "dosya2sil",
                    "manset",
                    "anasayfa",
                    "etkinlik",
                    "ebelediye"
                ])
            ) {
                if (strpos($column, "icerik") !== false) {
                    $value = Base64Cevir($value, '../images/yazi/');
                    $value = YazidakiResimleriIndir($value);
                    $value = str_replace('&nbsp;', '', $value);
                }

                if (strpos($column, "baslik_") !== false) {
                    $value = str_replace('"', "'", $value);
                }

                $SutunVarmi = $db->prepare("DESCRIBE $tablo");
                $SutunVarmi->execute();
                $Sutunlar = $SutunVarmi->fetchAll(PDO::FETCH_COLUMN);
                if (!in_array($column, $Sutunlar)) {
                    // Sütun yoksa oluştur
                    $db->exec("ALTER TABLE $tablo ADD COLUMN $column TEXT");
                }

                $value = addslashes($value);
                $setIfadesi .= "$column = '$value', ";
            }
        }


        try {
            $setIfadesi = rtrim($setIfadesi, ', '); // Son virgülü kaldır

            // Diğer verileri manuel olarak ekliyoruz
            $duzenleSorgusu = "UPDATE $tablo SET 
            resim='$resim', 
            resim1='$resim1', 
            resim2='$resim2', 
            resim3='$resim3', 
            video='$video', 
            dosya='$dosya', 
            dosya2='$dosya2', 
            manset='$manset',
            anasayfa='$anasayfa', 
            ebelediye='$ebelediye',
            etkinlik='$etkinlik', 
            $setIfadesi 
            WHERE id = $id";

            $duzenle = $db->query($duzenleSorgusu);

            Basarili("Menü başarıyla düzenlendi.");
        } catch (PDOException $e) {
            // Veritabanı hatası oluştuğunda burası çalışır
            Hata("Veritabanı hatası: " . $e->getMessage());
        } catch (Exception $e) {
            // Diğer hatalar için burası çalışır
            Hata("Beklenmeyen bir hata oluştu: " . $e->getMessage());
        }
    }

    /*********************************************************** Menü Düzenleme Alanı Sonu ***********************************************************************/


    $sayfatip = $_GET['st'];
    $durum = $db->prepare("SELECT * FROM sayfatipleri WHERE id=:id");
    $durum->execute(array("id" => $sayfatip));
    $durumcek = $durum->fetch(PDO::FETCH_ASSOC);
    $sayfaadi = $durumcek['yetkikontrol'];
    $sekmeadi = $durumcek['sayfa'];
    $yetki = $db->prepare("SELECT * FROM yonetim WHERE kullanici_adi=:id");
    $yetki->execute(array("id" => $_SESSION['kullanici_adi']));
    $yetkicek = $yetki->fetch(PDO::FETCH_ASSOC);

    if ($yetkicek[$sayfaadi] == "1") {
        $url = 'https://' . $_SERVER['SERVER_NAME'] . $_SERVER['REQUEST_URI'];

        if (isset($_GET['id'])) {
            $menusor = $db->prepare("SELECT * FROM $tablo WHERE id=:id");
            $menusor->execute(array("id" => addslashes(strip_tags($_GET['id']))));
            $MenuVeri = $menusor->fetch(PDO::FETCH_ASSOC);

            $id = $MenuVeri['id'];
        }


?>



        <div class="main-content app-content mt-0">
            <div class="side-app">



                <div id="gizle" class="main-container container-fluid <?php echo isset($_GET['id']) ? " gizle" : "goster"; ?>">
                    <div class="page-header">
                        <div>
                            <?php if (empty($_GET['id'])) { ?>
                                <button type="button" id="gizle" onclick="menuler()" class="btn btn-green"><i class="fe fe-plus me-2"></i>EKLE</button>
                            <?php } else { ?>
                                <a href="menuler.php?st=<?php echo $_GET['st']; ?>"><button type="button" class="btn btn-success"><i class="fe fe-plus me-2"></i>EKLE</button></a>
                                <button type="button" id="gizle" onclick="menuler()" class="btn btn-gray"><i class="fe fe-edit me-2"></i>Menüyü Düzenlemeye Devam Et</button>
                            <?php } ?>
                        </div>
                        <div>
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="javascript:void(0)">Anasayfa</a></li>
                                <li class="breadcrumb-item active" aria-current="page"><?php echo $sekmeadi ?></li>
                            </ol>
                        </div>
                    </div>


                    <div class="row ">
                        <div class="col-md-12 ">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title"><?php echo $sekmeadi ?></h3>
                                </div>
                                <div class="card-body">

                                    <div class="table-responsive">
                                        <table class="table table-bordered text-nowrap table-hover border-bottom" id="responsive-datatable">
                                            <thead>
                                                <tr>
                                                    <th class="wd-15p border-bottom-0">Resim</th>
                                                    <th class="wd-15p border-bottom-0">Menü Adı</th>
                                                    <th class="wd-15p border-bottom-0 text-center">Durum</th>
                                                    <?php if (AktifKontrol("aciliricerik")) { ?>
                                                        <th class="text-center">Açılır İçerik</th>
                                                    <?php } ?>
                                                    <?php if (AktifKontrol("kisiekle")) { ?>
                                                        <th class="text-center">Kişi Ekle</th>
                                                    <?php } ?>
                                                    <?php if (AktifKontrol("urunler")) { ?>
                                                        <th class="text-center">Ürünler</th>
                                                    <?php } ?>
                                                    <?php if (AktifKontrol("projeler")) { ?>
                                                        <th class="text-center">Projeler</th>
                                                    <?php } ?>
                                                    <?php if (AktifKontrol("dokuman")) { ?>
                                                        <th class="text-center">Döküman</th>
                                                    <?php } ?>
                                                    <?php if (AktifKontrol("resimler")) { ?>
                                                        <th class="text-center">Resimler</th>
                                                    <?php } ?>
                                                    <?php if (AktifKontrol("videolar")) { ?>
                                                        <th class="text-center">Videolar</th>
                                                    <?php } ?>
                                                    <?php if (AktifKontrol("anasayfa")) { ?>
                                                        <th class="text-center">Anasayfa</th>
                                                    <?php } ?>
                                                    <th class="wd-15p border-bottom-0 text-center">Düzenle</th>
                                                    <th class="wd-15p border-bottom-0 text-center">Sil</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php
                                                function kategoriListe($id, $tab)
                                                {
                                                    global $db, $sayfatip, $tablo;

                                                    $kod = "SELECT *, 
                                        (SELECT COUNT(A.id) FROM $tablo AS A WHERE A.ustmenu=K.id and sayfatip=:sayfatip) as altKategoriSayisi 
                                        FROM $tablo AS K
                                        WHERE K.ustmenu=:id and sayfatip=:sayfatip 
                                        ORDER BY tarih desc, menu_sira";

                                                    $stmt = $db->prepare($kod);
                                                    $stmt->execute(array('id' => $id, 'sayfatip' => $sayfatip));

                                                    while ($veri = $stmt->fetch(PDO::FETCH_ASSOC)) { ?>

                                                        <?php if ($veri['ustmenu'] == '0') { ?>
                                                            <tr id='RowMenu<?= $veri["id"] ?>' class='usttablohover'>
                                                            <?php } else { ?>
                                                            <tr id='RowMenu<?= $veri["id"] ?>' class='tablohover'>
                                                            <?php } ?>

                                                            <?php if ($veri['resim'] == '') { ?>
                                                                <td><img class='w-7 h-7' src='../images/images/resimyok.png'></td>
                                                            <?php } else { ?>
                                                                <td><img class='w-7 h-7' src='../<?= $veri["resim"] ?>'></td>
                                                            <?php } ?>

                                                            <?php if ($veri['ustmenu'] == '0') { ?>
                                                                <td>
                                                                <?php } else { ?>
                                                                <td class='' style='padding-left:<?= $tab ?>px !important'><i class='fe fe-corner-down-right'></i>
                                                                <?php } ?>
                                                                <?= kes($veri["baslik_tr"], 85) ?>
                                                                </td>

                                                                <td>
                                                                    <?php if ($veri["menu_durum"]) {
                                                                        echo "AKTİF";
                                                                    } else {
                                                                        echo "PASİF";
                                                                    } ?>
                                                                </td>

                                                                <?php if (AktifKontrol("aciliricerik")) { ?>
                                                                    <td class="text-center"><a href="aicerik.php?uid=<?= $veri['id'] ?>&st=<?= $sayfatip ?>"> <button class="btn btn-default" data-bs-effect="effect-scale" data-bs-toggle="modal">Açılır İçerik</button></a></td>
                                                                <?php } ?>

                                                                <?php if (AktifKontrol("kisiekle")) { ?>
                                                                    <td class="text-center"><a href="kisiekle.php?uid=<?= $veri['id'] ?>&st=<?= $sayfatip ?>"> <button class="btn btn-default" data-bs-effect="effect-scale" data-bs-toggle="modal">Kişi Ekle</button></a></td>
                                                                <?php } ?>

                                                                <?php if (AktifKontrol("urunler")) { ?>
                                                                    <td class="text-center"><a href="urunler.php?uid=<?= $veri['id'] ?>"> <button class="btn btn-default" data-bs-effect="effect-scale" data-bs-toggle="modal">Ürünler</button></a></td>
                                                                <?php } ?>

                                                                <?php if (AktifKontrol("projeler")) { ?>
                                                                    <td class="text-center"><a href="projeler.php?uid=<?= $veri['id'] ?>"> <button class="btn btn-default" data-bs-effect="effect-scale" data-bs-toggle="modal">Projeler</button></a></td>
                                                                <?php } ?>

                                                                <?php if (AktifKontrol("dokuman")) { ?>
                                                                    <td class="text-center"><button class="btn btn-outline-default" data-bs-effect="effect-scale" data-bs-toggle="modal" onclick="javascript:ModalID_dosya(<?= $veri['id'] ?>)" href="#dosyaekle"><i class="fa fa-folder me-2"></i> Dosya Ekle</button></td>
                                                                <?php } ?>

                                                                <?php if (AktifKontrol("resimler")) { ?>
                                                                    <td class="text-center"><button class="btn btn-outline-default" data-bs-effect="effect-scale" data-bs-toggle="modal" onclick="javascript:ModalID(<?= $veri['id'] ?>)" href="#resimekle"><i class="fa fa-file-photo-o me-2"></i> Resim Ekle</button></td>
                                                                <?php } ?>

                                                                <?php if (AktifKontrol("videolar")) { ?>
                                                                    <td class="text-center"><button class="btn btn-outline-default" data-bs-effect="effect-scale" data-bs-toggle="modal" onclick="javascript:ModalID_video(<?= $veri['id'] ?>)" href="#videoekle"><i class="fa fa-file-video-o me-2"></i> Video Ekle</button></td>
                                                                <?php } ?>

                                                                <?php if (AktifKontrol("anasayfa")) { ?>
                                                                    <td>
                                                                        <div class="material-switch">
                                                                            <input type="checkbox" id="<?= $veri['id'] ?>" class="anasayfagoster" sutun="anasayfa" <?= $veri['anasayfa'] == 1 ? 'checked' : '' ?> />
                                                                            <label for="<?= $veri['id'] ?>" class="label-danger"></label>
                                                                        </div>
                                                                    </td>
                                                                <?php } ?>

                                                                <td class='text-center'><a href='menuler.php?st=<?= $sayfatip ?>&id=<?= $veri["id"] ?>'><i class='fe fe-edit fs-25 text-green'></i></a></td>
                                                                <td class='text-center'><a style='cursor: pointer' onclick='javascript:MenuSilConfirm(<?= $veri["id"] ?>)' name='sil'><i class='fe fe-trash-2 fs-25 text-danger'></i></a></td>
                                                            </tr>

                                                    <?php if ($veri["altKategoriSayisi"] > 0) {
                                                            kategoriListe($veri["id"], $tab + 30);
                                                        }
                                                    }
                                                }

                                                kategoriListe(0, 10); ?>

                                            </tbody>
                                        </table>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>




                <div id="goster" class="main-container container-fluid <?php echo isset($_GET['id']) ? " goster" : "gizle"; ?>">
                    <div class="page-header">
                        <div>
                            <?php if (isset($_GET['id'])) { ?>
                                <a href="menuler.php?st=<?php echo $_GET['st']; ?>"><button type="button" class="btn btn-green"><i class="fe fe-plus me-2"></i>EKLE</button></a>
                            <?php } ?>
                            <button type="button" id="goster" onclick="menuekle()" class="btn btn-btn btn-gray"><i class="fe fe-list me-2"></i><?php echo $sekmeadi ?> Listele</button>
                        </div>


                        <div>
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="javascript:void(0)">Anasayfa</a></li>
                                <li class="breadcrumb-item active" aria-current="page"><?php echo $sekmeadi . (empty($_GET['id']) ? " Yeni Ekle" : " Düzenle") ?></li>
                            </ol>
                        </div>
                    </div>



                    <div class="row ">
                        <div class="col-md-12 ">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title"><?php echo isset($_GET['id']) ? "$sekmeadi Düzenle" : "$sekmeadi Yeni Ekle"; ?></h3>
                                </div>
                                <div class="card-body">

                                    <div class="card-pay">
                                        <ul class="tabs-menu nav">
                                            <?php if (isset($_GET['id'])) { ?>
                                                <li class=""><a href="#menuayar" class="active" data-bs-toggle="tab"><i class="fa fa-list"></i> Menü Ayarları</a></li>
                                                <?php if (AktifKontrol("resimler")) { ?>
                                                    <li><a href="#resimler" data-bs-toggle="tab"><i class="fa fa-image"></i>Resimler</a></li>
                                                <?php }
                                                if (AktifKontrol("dokuman")) { ?>
                                                    <li><a href="#dokuman" data-bs-toggle="tab"><i class="fa fa-file"></i> Dökümanlar</a></li>
                                                <?php }
                                                if (AktifKontrol("videolar")) { ?>
                                                    <li><a href="#videolar" data-bs-toggle="tab"><i class="fa fa-video-camera"></i> Videolar</a></li>
                                                <?php } ?>
                                            <?php } ?>
                                        </ul>
                                        <div class="tab-content">
                                            <div class="tab-pane show active" id="menuayar">

                                                <form action="" method="POST" enctype="multipart/form-data" id="demo-form2" data-parsley-validate class="form-horizontal form-label-left">

                                                    <input type="hidden" name="id" value="<?php echo $MenuVeri['id']; ?>">
                                                    <input type="hidden" name="sayfatip" value="<?php echo $sayfatip; ?>">
                                                    <input type="hidden" name="url" value="<?php echo $url; ?>">

                                                    <?php if (isset($_GET['id'])) { ?>
                                                        <input type="hidden" name="sonduzenleyen" value="<?php echo $yetkicek['kullanici_id']; ?>">
                                                        <input type="hidden" name="duzenlemetarihi" value="<?php echo date('Y-m-d H:i:s') ?>">
                                                    <?php } else { ?>
                                                        <input type="hidden" name="ekleyen" value="<?php echo $yetkicek['kullanici_id']; ?>">
                                                        <input type="hidden" name="eklemetarihi" value="<?php echo date('Y-m-d H:i:s') ?>">
                                                    <?php } ?>


                                                    <div class="row">
                                                        <div class="col-xl-12 col-sm-12 col-xs-12">
                                                            <div class="row">

                                                                <div class="form-group col-6 col-sm-6 col-xl-3 mb-2">
                                                                    <label class="control-label" for="first-name">Menu Sıra</label>
                                                                    <div>
                                                                        <input type="number" class="form-control col-md-12 col-xs-12" value="<?php echo $MenuVeri["menu_sira"]; ?>" name="menu_sira">
                                                                    </div>
                                                                </div>
                                                                <div class="form-group col-6 col-sm-6 col-xl-3 mb-2">
                                                                    <label class="control-label">Üst Menü</label>
                                                                    <div>

                                                                        <?php if ($MenuVeri) {
                                                                            $sira = $MenuVeri["menu_sira"] ?>
                                                                            <select class="form-control select2-show-search form-select" data-placeholder="Üst Menü Seçiniz" name="ustmenu" required tabindex="-1" onChange="if(this.value=='0'){$('[name=menu_sira]').val('<?php echo $sira; ?>');}else{$('[name=menu_sira]').val($('[name=ustmenu] option:selected').attr('sira'));}">
                                                                                <option sira="<?php echo $MenuVeri["sira"]; ?>" value="0">Üst Menü</option>
                                                                                <?php
                                                                                $UstMenuSor = $db->prepare("SELECT * FROM $tablo WHERE id != $id AND sayfatip=:sayfatip ORDER BY id ASC");
                                                                                $UstMenuSor->execute(array("sayfatip" => $sayfatip));

                                                                                while ($UstMenuler = $UstMenuSor->fetch(PDO::FETCH_ASSOC)) {
                                                                                    $UstMenuID = $UstMenuler['id'];
                                                                                    $KayitSayisi = $db->query("SELECT count(*) FROM $tablo WHERE ustmenu = $UstMenuID AND sayfatip = $sayfatip")->fetchColumn(); ?>

                                                                                    <option <?php if ($MenuVeri["ustmenu"] == $UstMenuID) {
                                                                                                echo "selected sira='$sira'";
                                                                                            } else {
                                                                                                echo "sira=" . $nRows + 1;
                                                                                            } ?> value="<?php echo $UstMenuler['id']; ?>">
                                                                                        <?php echo $UstMenuler['baslik_tr']; ?>
                                                                                    </option>

                                                                                <?php }
                                                                                ?>

                                                                            </select>
                                                                        <?php } else { ?>
                                                                            <select class="form-control select2-show-search form-select" data-placeholder="Üst Menü Seçiniz" name="ustmenu" required tabindex="-1" onChange="if(this.value=='0'){$('[name=menu_sira]').val('<?php echo sira(); ?>');}else{$('[name=menu_sira]').val($('[name=ustmenu] option:selected').attr('sira'));}">
                                                                                <option value="0">Seçiniz</option>
                                                                                <?php
                                                                                $UstMenuSor = $db->prepare("SELECT * FROM $tablo WHERE sayfatip=:sayfatip ORDER BY id ASC");
                                                                                $UstMenuSor->execute(array("sayfatip" => $sayfatip));

                                                                                while ($UstMenuler = $UstMenuSor->fetch(PDO::FETCH_ASSOC)) {
                                                                                    $UstMenuID = $UstMenuler['id'];
                                                                                    $KayitSayisi = $db->query("SELECT count(*) FROM $tablo WHERE ustmenu = $UstMenuID AND sayfatip = $sayfatip")->fetchColumn(); ?>

                                                                                    <option sira="<?php echo $KayitSayisi + 1; ?>" value="<?php echo $UstMenuler['id']; ?>">
                                                                                        <?php echo $UstMenuler['baslik_tr']; ?>
                                                                                    </option>

                                                                                <?php } ?>
                                                                            </select>
                                                                        <?php } ?>

                                                                    </div>
                                                                </div>


                                                                <div class="form-group col-6 col-sm-6 col-xl-3 mb-2">
                                                                    <label class="control-label " for="first-name">Menu Durum </label>
                                                                    <div>
                                                                        <select class="form-control select2-show-search form-select" data-placeholder="Menü Durumu Seçiniz" name="menu_durum" required>
                                                                            <?php if ($MenuVeri) { ?>
                                                                                <option <?= $MenuVeri['menu_durum'] == 1 ? "selected" : "" ?> value="1">Aktif</option>
                                                                                <option <?= $MenuVeri['menu_durum'] == 0 ? "selected" : "" ?> value="0">Pasif</option>
                                                                            <?php } else { ?>
                                                                                <option value="1">Aktif</option>
                                                                                <option value="0">Pasif</option>
                                                                            <?php } ?>
                                                                        </select>
                                                                    </div>
                                                                </div>

                                                                <?php if (AktifKontrol("kategorivarmi")) { ?>
                                                                    <div class="form-group  col-6 col-sm-6 col-xl-3 mb-2">
                                                                        <label class="control-label">Kategori</label>
                                                                        <div>
                                                                            <select style="width:100%" class="form-control select2-show-search form-select" data-placeholder="Kategori Seçiniz" name="kategori" required tabindex="-1">
                                                                                <?php
                                                                                $kategoriid = AktifKontrol("kategoriid");
                                                                                $kategoricek = $db->prepare("SELECT * FROM $tablo WHERE sayfatip = $kategoriid ORDER BY id");
                                                                                $kategoricek->execute();
                                                                                foreach ($kategoricek as $cek) { ?>
                                                                                    <option value="<?php echo $cek['id'] ?>"><?php echo $cek['baslik_tr'] ?></option>
                                                                                <?php } ?>
                                                                            </select>
                                                                        </div>
                                                                    </div>
                                                                <?php } ?>

                                                                <?php if (AktifKontrol("tarih")) { ?>
                                                                    <div class="form-group col-6 col-sm-6 col-xl-3 mb-2">
                                                                        <label class="control-label" for="first-name">Tarih</label>
                                                                        <div>
                                                                            <input type="date" class="form-control" name="tarih" value="<?php echo !empty($MenuVeri['tarih']) ? $MenuVeri['tarih'] : date('Y-m-d'); ?>" required>
                                                                        </div>
                                                                    </div>
                                                                <?php } ?>


                                                                <?php if (AktifKontrol("youtube")) { ?>
                                                                    <div class="form-group col-6 col-sm-6 col-xl-3 mb-2">
                                                                        <label class="control-label" for="first-name">YouTube Link</label>
                                                                        <div>
                                                                            <input type="text" class="form-control" name="youtube" value="<?php echo $MenuVeri['youtube']; ?>">
                                                                        </div>
                                                                    </div>
                                                                <?php } ?>

                                                                <?php if (AktifKontrol("manset")) { ?>
                                                                    <div class="col-6 col-sm-6 col-xl-3 mb-2">
                                                                        <div class="form-group">
                                                                            <label class="custom-switch form-switch mt-5">
                                                                                <span class="custom-switch-description me-3"><b>Manşette Göster</b></span>
                                                                                <input type="checkbox" name="manset" class="custom-switch-input" <?= $MenuVeri['manset'] == 1 ? "checked" : "" ?>>
                                                                                <span class="custom-switch-indicator custom-switch-indicator-lg"></span>
                                                                            </label>
                                                                        </div>
                                                                    </div>
                                                                <?php } ?>


                                                                <?php if (AktifKontrol("anasayfa")) { ?>
                                                                    <div class="col-6 col-sm-6 col-xl-3 mb-2">
                                                                        <div class="form-group">
                                                                            <label class="custom-switch form-switch mt-5">
                                                                                <span class="custom-switch-description me-3"><b>Anasayfada Göster</b></span>
                                                                                <input type="checkbox" name="anasayfa" class="custom-switch-input" <?= $MenuVeri['anasayfa'] == 1 ? "checked" : "" ?>>
                                                                                <span class="custom-switch-indicator custom-switch-indicator-lg"></span>
                                                                            </label>
                                                                        </div>
                                                                    </div>
                                                                <?php } ?>

                                                                <?php if (AktifKontrol("ebelediye")) { ?>
                                                                    <div class="col-6 col-sm-6 col-xl-3 mb-2">
                                                                        <div class="form-group">
                                                                            <label class="custom-switch form-switch mt-5">
                                                                                <span class="custom-switch-description me-3"><b>E-Belediyede Göster</b></span>
                                                                                <input type="checkbox" name="ebelediye" class="custom-switch-input" <?= $MenuVeri['ebelediye'] == 1 ? "checked" : "" ?>>
                                                                                <span class="custom-switch-indicator custom-switch-indicator-lg"></span>
                                                                            </label>
                                                                        </div>
                                                                    </div>
                                                                <?php } ?>

                                                                <?php if (AktifKontrol("etkinlik")) { ?>
                                                                    <div class="col-6 col-sm-6 col-xl-3 mb-2">
                                                                        <div class="form-group">
                                                                            <label class="custom-switch form-switch mt-5">
                                                                                <span class="custom-switch-description me-3"><b>Etkinlik</b></span>
                                                                                <input type="checkbox" name="etkinlik" class="custom-switch-input" <?= $MenuVeri['etkinlik'] == 1 ? "checked" : "" ?>>
                                                                                <span class="custom-switch-indicator custom-switch-indicator-lg"></span>
                                                                            </label>
                                                                        </div>
                                                                    </div>
                                                                <?php } ?>

                                                                

                                                            </div>


                                                            <?php if ($sayfatip == 1777) { ?>
                                                                <div class="row">
                                                                    <div class="form-group col-6 col-sm-6 col-xl-3 mb-2">
                                                                        <label class="control-label" for="first-name">İhale Başlangıç Tarihi</label>
                                                                        <div>
                                                                            <input type="date" class="form-control" name="ihale_baslangic_tarih" value="<?php echo $MenuVeri['ihale_baslangic_tarih']; ?>" required>
                                                                        </div>
                                                                    </div>

                                                                    <div class="form-group col-6 col-sm-6 col-xl-3 mb-2">
                                                                        <label class="control-label" for="first-name">İhale Başlangıç Saati</label>
                                                                        <div>
                                                                            <input type="time" class="form-control" name="ihale_baslangic_saat" value="<?php echo $MenuVeri['ihale_baslangic_saat']; ?>" required>
                                                                        </div>
                                                                    </div>

                                                                    <div class="form-group col-6 col-sm-6 col-xl-3 mb-2">
                                                                        <label class="control-label" for="first-name">İhale Bitiş Tarihi</label>
                                                                        <div>
                                                                            <input type="date" class="form-control" name="ihale_bitis_tarih" value="<?php echo $MenuVeri['ihale_bitis_tarih']; ?>" required>
                                                                        </div>
                                                                    </div>

                                                                    <div class="form-group col-6 col-sm-6 col-xl-3 mb-2">
                                                                        <label class="control-label" for="first-name">İhale Bitiş Saati</label>
                                                                        <div>
                                                                            <input type="time" class="form-control" name="ihale_bitis_saat" value="<?php echo $MenuVeri['ihale_bitis_saat']; ?>" required>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            <?php } ?>



                                                            <?php if ($sayfatip == 19) { ?>
                                                                <div class="row">
                                                                    <div class="form-group col-6 col-sm-6 col-xl-3 mb-2">
                                                                        <label class="control-label" for="first-name">Firma Adresi</label>
                                                                        <div>
                                                                            <input type="text" class="form-control" name="adres" value="<?php echo $MenuVeri['adres']; ?>">
                                                                        </div>
                                                                    </div>

                                                                    <div class="form-group col-6 col-sm-6 col-xl-3 mb-2">
                                                                        <label class="control-label" for="first-name">Firma Telefon</label>
                                                                        <div>
                                                                            <input type="text" class="form-control" name="telefon" value="<?php echo $MenuVeri['telefon']; ?>">
                                                                        </div>
                                                                    </div>

                                                                    <div class="form-group col-6 col-sm-6 col-xl-3 mb-2">
                                                                        <label class="control-label" for="first-name">Firma Eposta</label>
                                                                        <div>
                                                                            <input type="text" class="form-control" name="mail" value="<?php echo $MenuVeri['mail']; ?>">
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            <?php } ?>


                                                        </div>
                                                    </div>



                                                    <div class="panel panel-primary">
                                                        <div class="tab-menu-heading">
                                                            <div class="tabs-menu1">
                                                                <ul class="nav panel-tabs">

                                                                    <?php
                                                                    $dil = $db->prepare("SELECT * FROM dil WHERE aktif=1 ORDER BY id");
                                                                    $dil->execute();
                                                                    $saydir = 1;
                                                                    foreach ($dil as $dilcek) { ?>
                                                                        <li>
                                                                            <a href="#<?php echo $dilcek['kisaltma']; ?>" class="<?php echo $sekmeaktif = ($saydir == 1) ? "active" : ""; ?>" data-bs-toggle="tab">
                                                                                <i class="flag flag-<?php echo $dilcek['kisaltma']; ?>"></i>
                                                                                <?php echo $dilcek['dil']; ?>
                                                                            </a>
                                                                        </li>
                                                                    <?php $saydir++;
                                                                    } ?>
                                                                </ul>
                                                            </div>
                                                        </div>



                                                        <div class="panel-body tabs-menu-body">
                                                            <div class="tab-content">
                                                                <?php
                                                                $dil = $db->prepare("SELECT * FROM dil WHERE aktif=1");
                                                                $dil->execute();
                                                                $saydir = 1;
                                                                foreach ($dil as $dilcek) {
                                                                    $dil = $dilcek['kisaltma']; ?>


                                                                    <div class="tab-pane <?php echo $sekmeaktif = ($saydir == 1) ? "active" : ""; ?>" id="<?php echo $dilcek['kisaltma']; ?>">


                                                                        <?php if ($dilcek['kisaltma'] == "tr") {
                                                                        } else { ?>
                                                                            <div class="btn btn-success ms-3 mt-4 mb-5 float-none" onclick="cevir('<?php echo $dilcek['kisaltma']; ?>')">
                                                                                <i class="fe fe-check me-2" style="color:#fff !important"></i> <?php echo $dilcek['dil']; ?> diline otomatik çevir
                                                                            </div>
                                                                        <?php } ?>


                                                                        <?php if (AktifKontrol("baslik")) { ?>
                                                                            <div class="form-group">
                                                                                <label class="control-label col-md-12 col-sm-12 col-xs-12" for="first-name">Başlık</label>
                                                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                                                    <input type="text" class="form-control baslik" dil="<?php echo $dilcek["dil"] ?>" name="baslik_<?php echo $dilcek['kisaltma']; ?>" value="<?php echo $MenuVeri["baslik_$dil"]; ?>">
                                                                                </div>
                                                                            </div>
                                                                        <?php } ?>

                                                                        <?php if (AktifKontrol("baslik1")) { ?>
                                                                            <div class="form-group">
                                                                                <label class="control-label col-md-12 col-sm-12 col-xs-12" for="first-name">Alt Başlık </label>
                                                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                                                    <input type="text" class="form-control" name="baslik1_<?php echo $dilcek['kisaltma']; ?>" value="<?php echo $MenuVeri["baslik1_$dil"]; ?>">
                                                                                </div>
                                                                            </div>
                                                                        <?php } ?>

                                                                        <?php if (AktifKontrol("baslik2")) { ?>
                                                                            <div class="form-group">
                                                                                <label class="control-label col-md-12 col-sm-12 col-xs-12" for="first-name">Alt Başlık 2 </label>
                                                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                                                    <input type="text" class="form-control" name="baslik2_<?php echo $dilcek['kisaltma']; ?>" value="<?php echo $MenuVeri["baslik2_$dil"]; ?>">
                                                                                </div>
                                                                            </div>
                                                                        <?php } ?>

                                                                        <?php if (AktifKontrol("icerik")) { ?>
                                                                            <div class="form-group">
                                                                                <label class="control-label col-md-12 col-sm-12 col-xs-12" for="first-name">İçerik </label>
                                                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                                                    <textarea class="icerikalan<?php echo $dilcek['kisaltma']; ?>" id="icerik_<?php echo $dilcek['kisaltma']; ?>" name="icerik_<?php echo $dilcek['kisaltma']; ?>"><?php echo $MenuVeri["icerik_$dil"]; ?></textarea>
                                                                                </div>
                                                                            </div>
                                                                        <?php } ?>

                                                                        <?php if (AktifKontrol("icerik1")) { ?>
                                                                            <div class="form-group">
                                                                                <label class="control-label col-md-12 col-sm-12 col-xs-12" for="first-name">İçerik 2 </label>
                                                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                                                    <textarea id="icerik1_<?php echo $dilcek['kisaltma']; ?>" name="icerik1_<?php echo $dilcek['kisaltma']; ?>"><?php echo $MenuVeri["icerik1_$dil"]; ?></textarea>
                                                                                </div>
                                                                            </div>
                                                                        <?php } ?>

                                                                        <?php if (AktifKontrol("icerik2")) { ?>
                                                                            <div class="form-group">
                                                                                <label class="control-label col-md-12 col-sm-12 col-xs-12" for="first-name">İçerik 3</label>
                                                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                                                    <textarea id="icerik2_<?php echo $dilcek['kisaltma']; ?>" name="icerik2_<?php echo $dilcek['kisaltma']; ?>"><?php echo $MenuVeri["icerik2_$dil"]; ?></textarea>
                                                                                </div>
                                                                            </div>
                                                                        <?php } ?>



                                                                        <?php if (AktifKontrol("link")) { ?>
                                                                            <div class="form-group">
                                                                                <label class="control-label col-md-12 col-sm-12 col-xs-12" for="first-name">Link </label>
                                                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                                                    <input type="text" class="form-control" name="link_<?php echo $dilcek['kisaltma']; ?>" value="<?php echo $MenuVeri["link_$dil"]; ?>">
                                                                                </div>
                                                                            </div>
                                                                        <?php } ?>


                                                                        <div class="row">
                                                                            <?php if (AktifKontrol("title")) { ?>
                                                                                <div class="form-group col-md-6 col-sm-12 col-xs-12">
                                                                                    <label class="control-label col-md-12 col-sm-12 col-xs-12" for="first-name">Özel Title </label>
                                                                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                                                                        <input type="text" class="form-control" name="title_<?php echo $dilcek['kisaltma']; ?>" value="<?php echo $MenuVeri["title_$dil"]; ?>">
                                                                                    </div>
                                                                                </div>
                                                                            <?php } ?>


                                                                            <?php if (AktifKontrol("description")) { ?>
                                                                                <div class="form-group col-md-6 col-sm-12 col-xs-12">
                                                                                    <label class="control-label col-md-12 col-sm-12 col-xs-12" for="first-name">Özel Description </label>
                                                                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                                                                        <input type="text" class="form-control" name="description_<?php echo $dilcek['kisaltma']; ?>" value="<?php echo $MenuVeri["description_$dil"]; ?>">
                                                                                    </div>
                                                                                </div>
                                                                            <?php } ?>
                                                                        </div>


                                                                    </div>

                                                                <?php $saydir++;
                                                                } ?>


                                                                <hr>
                                                                <div class="row">
                                                                    <div class="col-xl-6 col-sm-12 col-xs-12">

                                                                        <?php $alan = "resim";
                                                                        if (AktifKontrol("resim1")) { ?>
                                                                            <div class="row">
                                                                                <div class="col-6">
                                                                                    <div class="form-group mb-5">
                                                                                        <label class="control-label col-12" for="logo">Resim Seç</label>
                                                                                        <div class="col-12">
                                                                                            <input type="file" class="form-control col-12" name="<?php echo $alan ?>" id="<?php echo $alan ?>">
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="col-6 mb-5">
                                                                                    <div style="margin-left:10px">
                                                                                        <?php if (strlen($MenuVeri[$alan]) > 0) { ?>
                                                                                            <div id="thumbwrap">
                                                                                                <a class="thumb">
                                                                                                    <img style="width: 80px; height: 80px;" src="../<?php echo $MenuVeri[$alan] ?>">
                                                                                                    <span><img src="../<?php echo $MenuVeri[$alan] ?>"></span>
                                                                                                </a>
                                                                                            </div>
                                                                                            <label class="custom-control custom-checkbox-md mt-2 colorinput">
                                                                                                <input type="checkbox" class="custom-control-input" name="<?php echo $alan . "sil" ?>">
                                                                                                <input type="hidden" name="<?php echo $alan . "link" ?>" value="<?php echo $MenuVeri[$alan] ?>">
                                                                                                <span class="custom-control-label">Resmi Sil</span>
                                                                                            </label>
                                                                                        <?php } else { ?>
                                                                                            <div class="mt-5">
                                                                                                <img style="width: 40px; height: 40px;" src="../images/logo-yok.webp">
                                                                                                Yüklü Resim Yok
                                                                                            </div>
                                                                                        <?php } ?>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        <?php } ?>

                                                                        <?php $alan = "resim1";
                                                                        if (AktifKontrol("resim2")) { ?>
                                                                            <div class="row">
                                                                                <div class="col-6">
                                                                                    <div class="form-group mb-5">
                                                                                        <label class="control-label col-12" for="logo">Resim Seç</label>
                                                                                        <div class="col-12">
                                                                                            <input type="file" class="form-control col-12" name="<?php echo $alan ?>" id="<?php echo $alan ?>">
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="col-6 mb-5">
                                                                                    <div style="margin-left:10px">
                                                                                        <?php if (strlen($MenuVeri[$alan]) > 0) { ?>
                                                                                            <div id="thumbwrap">
                                                                                                <a class="thumb">
                                                                                                    <img style="width: 80px; height: 80px;" src="../<?php echo $MenuVeri[$alan] ?>">
                                                                                                    <span><img src="../<?php echo $MenuVeri[$alan] ?>"></span>
                                                                                                </a>
                                                                                            </div>
                                                                                            <label class="custom-control custom-checkbox-md mt-2 colorinput">
                                                                                                <input type="checkbox" class="custom-control-input" name="<?php echo $alan . "sil" ?>">
                                                                                                <input type="hidden" name="<?php echo $alan . "link" ?>" value="<?php echo $MenuVeri[$alan] ?>">
                                                                                                <span class="custom-control-label">Resmi Sil</span>
                                                                                            </label>
                                                                                        <?php } else { ?>
                                                                                            <div class="mt-5">
                                                                                                <img style="width: 40px; height: 40px;" src="../images/logo-yok.webp">
                                                                                                Yüklü Resim Yok
                                                                                            </div>
                                                                                        <?php } ?>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        <?php } ?>

                                                                        <?php $alan = "resim2";
                                                                        if (AktifKontrol("resim3")) { ?>
                                                                            <div class="row">
                                                                                <div class="col-6">
                                                                                    <div class="form-group mb-5">
                                                                                        <label class="control-label col-12" for="logo">Resim Seç</label>
                                                                                        <div class="col-12">
                                                                                            <input type="file" class="form-control col-12" name="<?php echo $alan ?>" id="<?php echo $alan ?>">
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="col-6 mb-5">
                                                                                    <div style="margin-left:10px">
                                                                                        <?php if (strlen($MenuVeri[$alan]) > 0) { ?>
                                                                                            <div id="thumbwrap">
                                                                                                <a class="thumb">
                                                                                                    <img style="width: 80px; height: 80px;" src="../<?php echo $MenuVeri[$alan] ?>">
                                                                                                    <span><img src="../<?php echo $MenuVeri[$alan] ?>"></span>
                                                                                                </a>
                                                                                            </div>
                                                                                            <label class="custom-control custom-checkbox-md mt-2 colorinput">
                                                                                                <input type="checkbox" class="custom-control-input" name="<?php echo $alan . "sil" ?>">
                                                                                                <input type="hidden" name="<?php echo $alan . "link" ?>" value="<?php echo $MenuVeri[$alan] ?>">
                                                                                                <span class="custom-control-label">Resmi Sil</span>
                                                                                            </label>
                                                                                        <?php } else { ?>
                                                                                            <div class="mt-5">
                                                                                                <img style="width: 40px; height: 40px;" src="../images/logo-yok.webp">
                                                                                                Yüklü Resim Yok
                                                                                            </div>
                                                                                        <?php } ?>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        <?php } ?>

                                                                        <?php $alan = "resim3";
                                                                        if (AktifKontrol("resim4")) { ?>
                                                                            <div class="row">
                                                                                <div class="col-6">
                                                                                    <div class="form-group mb-5">
                                                                                        <label class="control-label col-12" for="logo">Resim Seç</label>
                                                                                        <div class="col-12">
                                                                                            <input type="file" class="form-control col-12" name="<?php echo $alan ?>" id="<?php echo $alan ?>">
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="col-6 mb-5">
                                                                                    <div style="margin-left:10px">
                                                                                        <?php if (strlen($MenuVeri[$alan]) > 0) { ?>
                                                                                            <div id="thumbwrap">
                                                                                                <a class="thumb">
                                                                                                    <img style="width: 80px; height: 80px;" src="../<?php echo $MenuVeri[$alan] ?>">
                                                                                                    <span><img src="../<?php echo $MenuVeri[$alan] ?>"></span>
                                                                                                </a>
                                                                                            </div>
                                                                                            <label class="custom-control custom-checkbox-md mt-2 colorinput">
                                                                                                <input type="checkbox" class="custom-control-input" name="<?php echo $alan . "sil" ?>">
                                                                                                <input type="hidden" name="<?php echo $alan . "link" ?>" value="<?php echo $MenuVeri[$alan] ?>">
                                                                                                <span class="custom-control-label">Resmi Sil</span>
                                                                                            </label>
                                                                                        <?php } else { ?>
                                                                                            <div class="mt-5">
                                                                                                <img style="width: 40px; height: 40px;" src="../images/logo-yok.webp">
                                                                                                Yüklü Resim Yok
                                                                                            </div>
                                                                                        <?php } ?>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        <?php } ?>





                                                                        <?php if (AktifKontrol("video")) { ?>
                                                                            <div class="row mb-5">
                                                                                <div class="col-6">
                                                                                    <div class="form-group mt-5">
                                                                                        <label class="control-label col-12" for="logo">Video Seç</label>
                                                                                        <div class="col-12">
                                                                                            <input type="file" class="form-control col-12" name="video" id="video">
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="col-6 mt-5">
                                                                                    <div style="margin-left:10px">
                                                                                        <?php if (strlen($MenuVeri["video"]) > 0) { ?>
                                                                                            <div><a href="/<?php echo $MenuVeri["video"] ?>" target="_blank">Videoyu Görmek İçin Tıkla</a></div>
                                                                                            <label class="custom-control custom-checkbox-md mt-2 colorinput">
                                                                                                <input type="checkbox" class="custom-control-input" name="videosil">
                                                                                                <input type="hidden" name="videolink" value="<?php echo $MenuVeri["video"] ?>">
                                                                                                <span class="custom-control-label">Videoyu Sil</span>
                                                                                            </label>
                                                                                        <?php } else { ?>
                                                                                            <div class="mt-5">
                                                                                                <img style="width: 40px; height: 40px;" src="../images/logo-yok.webp">
                                                                                                Yüklü Video Yok
                                                                                            </div>
                                                                                        <?php }
                                                                                        ?>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        <?php } ?>

                                                                    </div>

                                                                    <div class="col-xl-6 col-sm-12 col-xs-12">
                                                                        <?php if (AktifKontrol("dosya")) { ?>
                                                                            <div class="row mb-5">
                                                                                <div class="col-6">
                                                                                    <div class="form-group">
                                                                                        <label class="control-label col-12" for="logo">Dosya Yükle</label>
                                                                                        <div class="col-12">
                                                                                            <input type="file" class="form-control col-12" name="dosya" id="dosya">
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="col-6 mt-5">
                                                                                    <div style="margin-left:10px">
                                                                                        <?php if (strlen($MenuVeri["dosya"]) > 0) { ?>
                                                                                            <div><a download href="/<?php echo $MenuVeri["dosya"] ?>" target="_blank">Dosyayı Görmek İçin Tıkla</a></div>
                                                                                            <label class="custom-control custom-checkbox-md mt-2 colorinput">
                                                                                                <input type="checkbox" class="custom-control-input" name="dosyasil">
                                                                                                <input type="hidden" name="dosyalink" value="<?php echo $MenuVeri["dosya"] ?>">
                                                                                                <span class="custom-control-label">Ekli Dosyayı Sil</span>
                                                                                            </label>
                                                                                        <?php } else { ?>
                                                                                            <div class="">
                                                                                                <img style="width: 40px; height: 40px;" src="../images/logo-yok.webp">
                                                                                                Ekli dosya Yok
                                                                                            </div>
                                                                                        <?php } ?>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        <?php } ?>



                                                                        <?php if (AktifKontrol("dosya2")) { ?>
                                                                            <div class="row mb-5">
                                                                                <div class="col-6">
                                                                                    <div class="form-group">
                                                                                        <label class="control-label col-12" for="logo">Dosya 2 Yükle</label>
                                                                                        <div class="col-12">
                                                                                            <input type="file" class="form-control col-12" name="dosya2" id="dosya2">
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="col-6 mt-5">
                                                                                    <div style="margin-left:10px">
                                                                                        <?php
                                                                                        if (strlen($MenuVeri["dosya2"]) > 0) { ?>
                                                                                            <div><a download href="/<?php echo $MenuVeri["dosya2"] ?>" target="_blank">Dosyayı Görmek İçin Tıkla</a></div>
                                                                                            <label class="custom-control custom-checkbox-md mt-2 colorinput">
                                                                                                <input type="checkbox" class="custom-control-input" name="dosya2sil">
                                                                                                <input type="hidden" name="dosya2link" value="<?php echo $MenuVeri["dosya2"] ?>">
                                                                                                <span class="custom-control-label">Ekli Dosyayı Sil</span>
                                                                                            </label>
                                                                                        <?php } else { ?>
                                                                                            <div class="">
                                                                                                <img style="width: 40px; height: 40px;" src="../images/logo-yok.webp">
                                                                                                Ekli dosya Yok
                                                                                            </div>
                                                                                        <?php } ?>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        <?php } ?>

                                                                    </div>
                                                                </div>

                                                                <hr>
                                                                <div class="col-12">
                                                                    <button name="<?= $butonname = $MenuVeri ? "duzenle" : "ekle"; ?>" type="submit" class="btn btn-dark btn-lg"><i class="fa fa-floppy-o me-2" style="color:#fff !important"></i> Kaydet</button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </form>

                                            </div>

                                            <?php if (isset($_GET['id'])) { ?>

                                                <?php if (AktifKontrol("resimler")) { ?>
                                                    <div class="tab-pane" id="resimler">
                                                        <div class="col-lg-12">
                                                            <div class="card">
                                                                <div class="float-end" style="float:right !important"><button class="btn btn-green" data-bs-effect="effect-scale" data-bs-toggle="modal" onclick="javascript:ModalID(<?php echo $_GET['id']; ?>)" href="#resimekle"><i class="fe fe-plus me-2" style="color:#fff !important"></i>Resim Ekle</i></div>
                                                                <div class="card-header">
                                                                    <h3 class="card-title">Resimler</h3>
                                                                </div>
                                                                <div>
                                                                    <label>
                                                                        <input id="SilindifilitreCheck" type="checkbox" onchange="javascript:SilinenResimler(this)">Gizlenen Resimler
                                                                    </label>
                                                                </div>
                                                                <div class="card-body">
                                                                    <div class="row">

                                                                        <?php
                                                                        $resimsor = $db->prepare("SELECT * FROM resimler WHERE menu_id=:id AND sayfatip=:sayfatip");
                                                                        $resimsor->execute(array("id" => $MenuVeri['id'], "sayfatip" => $sayfatip));
                                                                        $sayac = 1;
                                                                        foreach ($resimsor as $resimcek) {
                                                                            if ($resimcek['sil'] == true) {
                                                                                $silindi = "true";
                                                                                $silindistyle = "display:none";
                                                                            } else {
                                                                                $silindi = "false";
                                                                                $silindistyle = "";
                                                                            }
                                                                        ?>

                                                                            <div id="RowResim<?php echo $resimcek['id']; ?>" silindi="<?php echo $silindi ?>" style="<?php echo $silindistyle ?>" class="col-md-6 col-xl-2 text-center mt-5 Rows" id="RowResim<?php echo $resimcek['id']; ?>">
                                                                                <form onsubmit="javascript:return TextKaydet(this)">
                                                                                    <img src="../<?php echo $resimcek['link']; ?>" class="resimlerclass">
                                                                                    <div class="mb-2" link="https://<?php echo $_SERVER['SERVER_NAME'] . "/" . $resimcek['link'] ?>" onclick="copyLink(this)">
                                                                                        <span class="input-group-text text-black"><i class="fa fa-copy me-2"></i> Linki Kopyalamak İçin Tıkla</span>
                                                                                    </div>


                                                                                    <?php
                                                                                    $dilsor = $db->prepare("SELECT * FROM dil WHERE aktif=:id ORDER BY id ASC");
                                                                                    $dilsor->execute(array("id" => 1));
                                                                                    foreach ($dilsor as $dilcek) {
                                                                                        $dil = $dilcek['kisaltma'];  ?>

                                                                                        <div class="input-group">
                                                                                            <span class="input-group-text" id="basic-addon1"><i class="flag flag-<?php echo $dil; ?>"></i></span>
                                                                                            <input type="text" class="form-control" name="<?php echo "resimbaslik_" . $dilcek['kisaltma']; ?>" id="<?php echo "baslik_" . $dilcek['kisaltma']; ?><?php echo $resimcek['id']; ?>" value="<?php echo $resimcek['baslik_' . $dil . '']; ?>">
                                                                                        </div>

                                                                                    <?php $sayac++;
                                                                                    } ?>
                                                                                    <input type="hidden" class="form-control" name="id" value="<?php echo $resimcek['id']; ?>">
                                                                                    <button style="<?php echo $silindistyle ?>" type="submit" id="gonder" name="resimler" class="btn btn-dark mt-2 me-2 mb-0 col-12 notice1 mt-2 mb-2 KaydetBtn"><i class="fe fe-check me-2" style="color:#fff !important"></i>Kaydet</button>
                                                                                </form>

                                                                                <button onClick="ResimDilCevir('<?php echo $resimcek['id']; ?>','<?php echo $dilcek['kisaltma']; ?>');" class="btn btn-success w-100 mb-4 float-none "><i class="fe fe-check me-2" style="color:#fff !important"></i>Otomatik Çevir</button>
                                                                                <div class="row ps-2 pe-2">
                                                                                    <button style="<?php echo $silindistyle ?>" id="" onclick="javascript:KayitSilConfirm(<?php echo $resimcek['id']; ?>)" name="sil" class="btn btn-indigo col notice1 me-1 SilBtn"><i class="fe fe-check me-2 " style="color:#fff !important"></i>Gizle</button>
                                                                                    <button onclick="javascript:ResimKaliciSilConfirm(<?php echo $resimcek['id']; ?>)" name="kalicisil" class="btn btn-danger col notice1 ms-1 KaliciSilBtn"><i class="fe fe-check me-2 " style="color:#fff !important"></i>Sil</button>
                                                                                </div>
                                                                                <button style="<?php echo $resimcek['sil'] ? "" : "display:none"; ?>" id="" onclick="javascript:GeriAlConfirm(<?php echo $resimcek['id']; ?>)" name="sil" class="btn btn-warning mt-4 me-2 mb-0 col-12 notice1 mb-5 GeriAlBtn">
                                                                                    <i class="fe fe-check me-2" style="color:#fff !important"></i>Geri Al
                                                                                </button>
                                                                            </div>

                                                                        <?php }
                                                                        ?>

                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php } ?>

                                                <?php if (AktifKontrol("dokuman")) { ?>
                                                    <div class="tab-pane" id="dokuman">
                                                        <div class="col-lg-12">
                                                            <div class="card">
                                                                <div class="float-end" style="float:right !important"><button class="btn btn-green" data-bs-effect="effect-scale" data-bs-toggle="modal" onclick="javascript:ModalID_dosya(<?php echo $_GET['id']; ?>)" href="#dosyaekle"><i class="fe fe-plus me-2" style="color:#fff !important"></i>Dosya Ekle</i></div>
                                                                <div class="card-header">
                                                                    <h3 class="card-title">Dökümanlar</h3>
                                                                </div>
                                                                <div>
                                                                    <label>
                                                                        <input id="DosyaSilindifilitreCheck" type="checkbox" onchange="javascript:SilinenDosyalar(this)">Gizlenen Dökümanlar
                                                                    </label>
                                                                </div>
                                                                <div class="card-body">
                                                                    <div class="row">
                                                                        <?php
                                                                        $dosyasor = $db->prepare("select * from dokuman where menu_id=:id and sayfatip =:sayfatip");
                                                                        $dosyasor->execute(array("id" => $MenuVeri['id'], "sayfatip" => $sayfatip));

                                                                        foreach ($dosyasor as $dosyacek) {
                                                                        ?>
                                                                            <div class="col-md-6 col-xl-2 text-center mt-5 Rows" id="RowDosya<?php echo $dosyacek['id']; ?>">
                                                                                <form onsubmit="javascript:return DosyaTextKaydet(this)">
                                                                                    <img src="../images/images/<?php
                                                                                                                $dosyaadi = $dosyacek['link'];
                                                                                                                if (strstr($dosyaadi, "xlsx")) {
                                                                                                                    echo "excel.png";
                                                                                                                } elseif (strstr($dosyaadi, "docx")) {
                                                                                                                    echo "word.png";
                                                                                                                } elseif (strstr($dosyaadi, "pdf")) {
                                                                                                                    echo "pdf.png";
                                                                                                                } elseif (strstr($dosyaadi, "rar") || strstr($dosyaadi, "zip")) {
                                                                                                                    echo "rar.png";
                                                                                                                } else {
                                                                                                                    echo "dosya.png";
                                                                                                                }
                                                                                                                ?>" style="object-fit: contain; width: 50%; height: 100px; padding-bottom: 30px; cursor: default;">
                                                                                    <input type="text" class="form-control" name="baslik" value="<?php echo $dosyacek['baslik']; ?>">
                                                                                    <input type="hidden" class="form-control" name="id" value="<?php echo $dosyacek['id']; ?>">
                                                                                    <button style="<?= $videocek['sil'] ? "display:none;" : "" ?>" type="submit" id="gonder" name="resimler" class="btn btn-dark mt-4 me-2 mb-2 col-12 notice1 KaydetBtn">
                                                                                        <i class="fe fe-check me-2" style="color:#fff !important"></i>Kaydet
                                                                                    </button>
                                                                                </form>
                                                                                <div class="mb-2">
                                                                                    <span class="input-group-text text-black">
                                                                                        <i class="fa fa-copy me-2"></i>
                                                                                        <?php echo dosyaboyutu($dosyacek['boyut']); ?>
                                                                                    </span>
                                                                                </div>
                                                                                <div class="mb-2" link="https://<?php echo $_SERVER['SERVER_NAME'] . "/" . $dosyacek['link'] ?>" onclick="copyLink(this)">
                                                                                    <span class="input-group-text text-black">
                                                                                        <i class="fa fa-copy me-2"></i> Linki Kopyalamak İçin Tıkla
                                                                                    </span>
                                                                                </div>
                                                                                <div class="row ps-2 pe-2">
                                                                                    <button style="<?php echo $dosyacek['yaziicidosya'] ? "" : "display:none;"; ?>" id="" onclick="javascript:DosyaSilConfirm(<?php echo $dosyacek['id']; ?>)" name="sil" class="btn btn-indigo col notice1 ms-1 SilBtn">
                                                                                        <i class="fe fe-check me-2 " style="color:#fff !important"></i>Gizle
                                                                                    </button>
                                                                                    <button style="<?php echo $dosyacek['yaziicidosya'] ? "" : "display:none;"; ?>" id="" onclick="javascript:DosyaGeriAlConfirm(<?php echo $dosyacek['id']; ?>)" name="sil" class="btn btn-warning ms-1 col mb-0 notice1 GeriAlBtn">
                                                                                        <i class="fe fe-check me-2" style="color:#fff !important"></i>Göster
                                                                                    </button>
                                                                                    <button id="" onclick="javascript:DosyaKaliciSilConfirm(<?php echo $dosyacek['id']; ?>)" name="sil" class="btn btn-danger col notice1 ms-1 SilBtn">
                                                                                        <i class="fe fe-check me-2 " style="color:#fff !important"></i>Sil
                                                                                    </button>

                                                                                </div>
                                                                            </div>
                                                                        <?php } ?>
                                                                    </div>
                                                                </div>

                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php } ?>

                                                <?php if (AktifKontrol("videolar")) { ?>
                                                    <div class="tab-pane" id="videolar">
                                                        <div class="col-lg-12">
                                                            <div class="card">
                                                                <div class="float-end" style="float:right !important"><button class="btn btn-green" data-bs-effect="effect-scale" data-bs-toggle="modal" onclick="javascript:ModalID_video(<?php echo $_GET['id']; ?>)" href="#videoekle"><i class="fe fe-plus me-2" style="color:#fff !important"></i>Video Ekle</i></div>
                                                                <div class="card-header">
                                                                    <h3 class="card-title">Videolar</h3>
                                                                </div>
                                                                <div class="card-body">
                                                                    <div class="row">
                                                                        <?php
                                                                        $videosor = $db->prepare("SELECT * FROM videolar WHERE menu_id=:id AND sayfatip =:sayfatip");
                                                                        $videosor->execute(array("id" => $MenuVeri['id'], "sayfatip" => $sayfatip));
                                                                        foreach ($videosor as $videocek) {
                                                                            if ($videocek["videoresim"]) {
                                                                                $videoresim = $videocek["videoresim"];
                                                                            } else {
                                                                                $videoresim = "../images/images/video.png";
                                                                            }

                                                                            if ($videocek["link"]) {
                                                                                $videolink = "https://" . $_SERVER['SERVER_NAME'] . "/" . $videocek["link"];
                                                                            } else {
                                                                                $videolink = $videocek["youtube"];
                                                                            }

                                                                            if ($videocek['boyut']) {
                                                                                $videoboyut = dosyaboyutu($videocek['boyut']);
                                                                            } else {
                                                                                $videoboyut = "Youtube Videosu";
                                                                            }

                                                                        ?>
                                                                            <div class="col-md-6 col-xl-2 text-center mt-5 Rows" id="RowDosya<?php echo $videocek['id']; ?>">
                                                                                <form onsubmit="javascript:return VideoTextKaydet(this)">
                                                                                    <?php if ($videocek["youtube"]) { ?>
                                                                                        <img src="<?php echo $videoresim ?>" style="object-fit: cover; width: 100%; height: 250px; cursor: default;">
                                                                                    <?php } else { ?>
                                                                                        <video width="100%" height="245" controls>
                                                                                            <source src="<?php echo $videolink ?>" type="video/mp4">
                                                                                        </video>
                                                                                    <?php } ?>
                                                                                    <input type="text" class="form-control mt-3" name="baslik" value="<?php echo $videocek['baslik']; ?>">
                                                                                    <input type="hidden" class="form-control" name="id" value="<?php echo $videocek['id']; ?>">
                                                                                    <button style="<?= $videocek['sil'] ? "display:none;" : "" ?>" type="submit" id="gonder" name="resimler" class="btn btn-dark mt-4 me-2 mb-2 col-12 notice1 KaydetBtn">
                                                                                        <i class="fe fe-check me-2" style="color:#fff !important"></i>Kaydet
                                                                                    </button>
                                                                                </form>
                                                                                <div class="mb-2">
                                                                                    <span class="input-group-text text-black">
                                                                                        <i class="fa fa-copy me-2"></i>
                                                                                        <?php echo $videoboyut ?>
                                                                                    </span>
                                                                                </div>
                                                                                <div class="mb-2" link="<?php echo $videolink ?>" onclick="copyLink(this)">
                                                                                    <span class="input-group-text text-black">
                                                                                        <i class="fa fa-copy me-2"></i> Linki Kopyalamak İçin Tıkla
                                                                                    </span>
                                                                                </div>
                                                                                <div class="row ps-2 pe-2">
                                                                                    <button id="" onclick="javascript:VideoKaliciSilConfirm(<?php echo $videocek['id']; ?>)" name="sil" class="btn btn-danger col notice1 ms-1 SilBtn">
                                                                                        <i class="fe fe-check me-2 " style="color:#fff !important"></i>Sil
                                                                                    </button>

                                                                                </div>
                                                                            </div>
                                                                        <?php } ?>
                                                                    </div>
                                                                </div>

                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php } ?>

                                            <?php } ?>
                                        </div>
                                    </div>
                                </div>



                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>

        <div class="modal fade" id="resimekle">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content modal-content-demo">
                    <div class="modal-header">
                        <h6 class="modal-title">Resim Yükle</h6><button aria-label="Close" class="btn-close" data-bs-dismiss="modal"><span aria-hidden="true">&times;</span></button>
                    </div>
                    <div class="modal-body">
                        <form class="form-horizontal" action="resim.php" method="post" enctype="multipart/form-data">
                            <div class="input">
                                <input class="form-control" type="file" name="resim[]" multiple="multiple" />
                                <input type="hidden" name="menu_id" id="menu_id" value="" />
                                <input type="hidden" name="url" value="<?php echo $url; ?>">
                                <input type="hidden" name="st" value="<?php echo $sayfatip; ?>">
                                <div class="form-check mt-2 mb-2">
                                    <label class="custom-control custom-checkbox-md mt-2 colorinput">
                                        <input type="checkbox" class="custom-control-input" name="yaziiciresim">
                                        <span class="custom-control-label">Gizli Resim</span>
                                    </label>
                                </div>
                                <button class="btn btn-dark">Yükle</button>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-light" data-bs-dismiss="modal">Kapat</button>
                    </div>
                </div>
            </div>
        </div>


        <div class="modal fade" id="dosyaekle">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content modal-content-demo">
                    <div class="modal-header">
                        <h6 class="modal-title">Döküman Yükle</h6><button aria-label="Close" class="btn-close" data-bs-dismiss="modal"><span aria-hidden="true">&times;</span></button>
                    </div>
                    <div class="modal-body">
                        <form name="from_file_upload" action="dosya.php" method="post" enctype="multipart/form-data" class="frm-upload">
                            <div class="input">
                                <input class="form-control" type="file" name="dosya[]" multiple="multiple" />
                                <input type="hidden" name="menu_id_dosya" id="menu_id_dosya" />
                                <input type="hidden" name="url" value="<?php echo $url; ?>">
                                <input type="hidden" name="st" value="<?php echo $sayfatip; ?>">
                                <div class="form-check mt-2 mb-2">
                                    <label class="custom-control custom-checkbox-md mt-2 colorinput">
                                        <input type="checkbox" class="custom-control-input" name="yaziicidosya">
                                        <span class="custom-control-label">Gizli Dosya</span>
                                    </label>
                                </div>
                                <button name="upload" class="btn btn-dark">Yükle</button>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-light" data-bs-dismiss="modal">Kapat</button>
                    </div>
                </div>
            </div>
        </div>


        <div class="modal fade" id="videoekle">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content modal-content-demo">
                    <div class="modal-header">
                        <h6 class="modal-title">Video Yükle</h6>
                        <button aria-label="Close" class="btn-close" data-bs-dismiss="modal">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form name="from_file_upload" action="video.php" method="post" enctype="multipart/form-data" class="frm-upload">
                            <div class="input">
                                <input class="form-control" type="file" name="dosya[]" multiple="multiple" accept="video/*" />
                                <input type="text" class="form-control mt-2" name="youtube" placeholder="YouTube Linki (Opsiyonel)">
                                <input type="hidden" name="menu_id_video" id="menu_id_video" />
                                <input type="hidden" name="url" value="<?php echo $url; ?>">
                                <input type="hidden" name="st" value="<?php echo $sayfatip; ?>">
                                <button name="upload" class="btn btn-dark mt-2">Yükle</button>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-light" data-bs-dismiss="modal">Kapat</button>
                    </div>
                </div>
            </div>
        </div>




<?php } else {
        require 'yetkisizislem.php';
    }
    require 'footer.php';
} ?>