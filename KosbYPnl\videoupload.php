<?php
require 'class.upload.php';

$dir_pics = (isset($_GET['pics']) ? $_GET['pics'] : $dir_dest);

$log = '';
use Verot\Upload\Upload;

if (isset($_FILES['dosya'])) {


    $files = array();
    foreach ($_FILES['dosya'] as $k => $l) {
        foreach ($l as $i => $v) {
            if (!array_key_exists($i, $files))
                $files[$i] = array();
            $files[$i][$k] = $v;
        }
    }

    foreach ($files as $file) {

        $handle = new Upload($file);
        if ($handle->uploaded) {
            $handle->file_max_size = $dosyaboyutu * 1000000;
            $handle->allowed = array ( 'video/*' );
            $handle->process('../'.$yol);

            if ($handle->processed) {
                $isim = $handle->file_dst_name;
                $boyut = $handle->file_src_size;
                $dosyaekle = $db->prepare("INSERT INTO videolar (menu_id, sayfatip, baslik, link, boyut) VALUES(?,?,?,?,?)");
                $dosyaekle->execute(array($menuid, $sayfatip, $isim, $yol.SEOdosya($isim), $boyut));

                rename("../$yol/$isim","../$yol/".SEOdosya($isim)."");
                session_start();
                $_SESSION['videoyuklendi'] = 1;
                header("Location:".$yonlenurl."");
                
            }
             else {
                session_start();
                $_SESSION['desteklenmiyor'] = 1;
                header("Location:".$yonlenurl."");
            }

        }

        $log .= $handle->log . '<br />';
    }

}

?>