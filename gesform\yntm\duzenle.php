<?php 
session_start();
if ($_SESSION['user_level'] == '1')
{	
include('fonksiyon.php');

$sql		= "SELECT * FROM ".TABLO." WHERE id='".$_GET['id']."'";
$result		= mysqli_query($con, $sql);
$row		= mysqli_fetch_array($result);
	
?>
<link rel="stylesheet" href="css/style.css" type="text/css" />
<center>
<form name="yeni" action="javascript:void(0);" method="post">
<b>
<table class="table_radius_border" border="0" style="margin:5px;font-size:12px;width:620px;">
<tr class="popup_5">
 <td>
	<table class="table_radius_border" border="0" style="margin:1px;font-size:12px;width:98%;">
	<tr class="popup_1">
		<td class="popup_2" >ID :</td>
		<td class="popup_3">
		<input value="<?=$row['id']?>" class="input_text" type="text" disabled>
		<input value="<?=$row['id']?>" class="input_text" type="hidden" name="id">
		</td>
	</tr>
	<tr class="popup_1">
		<td class="popup_2" >Vekil Adı Soyadı :</td>
		<td class="popup_3"><input style="width:410px;" value="<?=$row['vekil_adi']?>" class="input_text" type="text" name="vekil_adi"></td>
	</tr>
	<tr class="popup_1">
		<td class="popup_2">Vekil E-Posta :</td>
		<td class="popup_3"><input value="<?=$row['vekil_eposta']?>" class="input_text" type="text" name="vekil_eposta"></td>
	</tr>
	<tr class="popup_1">
		<td class="popup_2">Vekil Telefon :</td>
		<td class="popup_3"><input value="<?=$row['vekil_tel_no']?>" class="input_text" type="text" name="vekil_tel_no"></td>
	</tr>
	<tr class="popup_1">
		<td class="popup_2">Vekil Adres :</td>
		<td class="popup_3"><input value="<?=$row['vekil_note']?>" class="input_text" type="text" name="vekil_note"></td>
	</tr>
	<tr class="popup_1">
		<td class="popup_2">Başvuru Türü :</td>
		<td class="popup_3"><input value="<?=$row['sahip_basvuru_turu']?>" class="input_text" type="text" name="sahip_basvuru_turu"></td>
	</tr>
	<tr class="popup_1">
		<td class="popup_2">Sahip Adı Soyadı :</b></td>
		<td class="popup_3"><input value="<?=$row['sahip_adi']?>" class="input_text" type="text" name="sahip_adi"></td>
	</tr>
	<tr class="popup_1">
		<td class="popup_2">Sahip Kişi Türü :</td>
		<td class="popup_3"><input value="<?=$row['sahip_kisi_turu']?>" class="input_text" type="text" name="sahip_kisi_turu"></td>
	</tr>
	<tr class="popup_1">
		<td class="popup_2">Sahip Vergi/Kimlik No :</td>
		<td class="popup_3"><input value="<?=$row['sahip_vergi_kimlik_no']?>" class="input_text" type="text" name="sahip_vergi_kimlik_no"></td>
	</tr>
	<tr class="popup_1">
		<td class="popup_2">Sahip E-Posta :</td>
		<td class="popup_3"><input value="<?=$row['sahip_eposta']?>" class="input_text" type="text" name="sahip_eposta"></td>
	</tr>
	<tr class="popup_1">
		<td class="popup_2">Sahip KEP Adresi :</td>
		<td class="popup_3"><input value="<?=$row['sahip_kep_adresi']?>" class="input_text" type="text" name="sahip_kep_adresi"></td>
	</tr>
	<tr class="popup_1">
		<td class="popup_2">Sahip Telefon :</td>
		<td class="popup_3"><input value="<?=$row['sahip_tel_no']?>" class="input_text" type="text" name="sahip_tel_no"></td>
	</tr>
	<tr class="popup_1">
		<td class="popup_2">Sahip Yazışma Adresi :</td>
		<td class="popup_3"><input value="<?=$row['sahip_note']?>" class="input_text" type="text" name="sahip_note"></td>
	</tr>
	<tr class="popup_1">
		<td class="popup_2">GÜÇ ARTTIRIM :</td>
		<td class="popup_3"><input value="<?=$row['proje_info_guc_arttirim']?>" class="input_text" type="text" name="proje_info_guc_arttirim"></td>
	</tr>
	<tr class="popup_1">
		<td class="popup_2">Proje Adı :</td>
		<td class="popup_3"><input value="<?=$row['proje_info_proje_adi']?>" class="input_text" type="text" name="proje_info_proje_adi"></td>
	</tr>
	<tr class="popup_1">
		<td class="popup_2">Proje Üretim Tüketim Tesisi :</td>
		<td class="popup_3"><input value="<?=$row['proje_info_uret_tuket_tesis']?>" class="input_text" type="text" name="proje_info_uret_tuket_tesis"></td>
	</tr>
	<tr class="popup_1">
		<td class="popup_2">Proje Uygulama Yeri :</td>
		<td class="popup_3"><input value="<?=$row['proje_info_uygulama_yeri']?>" class="input_text" type="text" name="proje_info_uygulama_yeri"></td>
	</tr>
	<tr class="popup_1">
		<td class="popup_2">Proje İlçe/İl :</td>
		<td class="popup_3"><input value="<?=$row['proje_info_ilce_il']?>" class="input_text" type="text" name="proje_info_ilce_il"></td>
	</tr>
	<tr class="popup_1">
		<td class="popup_2">Proje Kurulacağı Mevki :</td>
		<td class="popup_3"><input value="<?=$row['proje_info_kurulacak_mevki']?>" class="input_text" type="text" name="proje_info_kurulacak_mevki"></td>
	</tr>
	<tr class="popup_1">
		<td class="popup_2">Proje Kurulacağı Ada :</td>
		<td class="popup_3"><input value="<?=$row['proje_info_kurulacak_ada']?>" class="input_text" type="text" name="proje_info_kurulacak_ada"></td>
	</tr>
	<tr class="popup_1">
		<td class="popup_2">Proje Kurulacağı Parsel :</td>
		<td class="popup_3"><input value="<?=$row['proje_info_kurulacak_parsel']?>" class="input_text" type="text" name="proje_info_kurulacak_parsel"></td>
	</tr>
	<tr class="popup_1">
		<td class="popup_2">Proje Geçici Parsel :</td>
		<td class="popup_3"><input value="<?=$row['proje_info_gecici_parsel']?>" class="input_text" type="text" name="proje_info_gecici_parsel"></td>
	</tr>
	<tr class="popup_1">
		<td class="popup_2">Proje DC Kurulu Güç :</td>
		<td class="popup_3"><input value="<?=$row['proje_info_tesis_dc_guc']?>" class="input_text" type="text" name="proje_info_tesis_dc_guc"></td>
	</tr>
	<tr class="popup_1">
		<td class="popup_2">Proje AC Kurulu Güç :</td>
		<td class="popup_3"><input value="<?=$row['proje_info_tesis_ac_guc']?>" class="input_text" type="text" name="proje_info_tesis_ac_guc"></td>
	</tr>
	<tr class="popup_1">
		<td class="popup_2">Proje Kaynak Türü :</td>
		<td class="popup_3"><input value="<?=$row['proje_info_kaynak_turu']?>" class="input_text" type="text" name="proje_info_kaynak_turu"></td>
	</tr>
	<tr class="popup_1">
		<td class="popup_2">Proje Bağlantı Şekli :</td>
		<td class="popup_3"><input value="<?=$row['proje_info_baglanti_sekli']?>" class="input_text" type="text" name="proje_info_baglanti_sekli"></td>
	</tr>
	<tr class="popup_1">
		<td class="popup_2">Proje Koordinat Enlem (X):</td>
		<td class="popup_3"><textarea style="width:410px;height:58px;" class="input_text" name="proje_info_santral_enlemx"><?=$row['proje_info_santral_enlemx']?></textarea></td>
	</tr>
	<tr class="popup_1">
		<td class="popup_2">Proje Koordinat Boylam (Y):</td>
		<td class="popup_3"><textarea style="width:410px;height:61px;" class="input_text" name="proje_info_santral_boylamy"><?=$row['proje_info_santral_boylamy']?></textarea></td>
	</tr>
	<tr class="popup_1">
		<td class="popup_2">Proje Abonelik Seçimi :</td>
		<td class="popup_3"><input value="<?=$row['proje_info_abonelik_secimi']?>" class="input_text" type="text" name="proje_info_abonelik_secimi"></td>
	</tr>
	<tr class="popup_1">
		<td class="popup_2">Proje İlgili Yönetmelik Maddesi :</td>
		<td class="popup_3"><input value="<?=$row['proje_info_yonetmelik']?>" class="input_text" type="text" name="proje_info_yonetmelik"></td>
	</tr>
	<tr class="popup_1">
		<td class="popup_2">Proje Notlar :</td>
		<td class="popup_3"><input value="<?=$row['proje_info_note']?>" class="input_text" type="text" name="proje_info_note"></td>
	</tr>
	<tr class="popup_1">
		<td class="popup_2">Keywords:</td>
		<td class="popup_3"><textarea style="width:410px;height:61px;" class="input_text" disabled><?=$row['keywords']?></textarea></td>
	</tr>

	</table>
 </td>
</tr>
<tr>
 <td align="right">
	<input type="hidden" name="pass_id" value="<?=$row['id']?>">
	<input class="input_btn" type="submit" value="Düzenle" onclick="duzenle();">&nbsp;
 </td>
</tr>
</table>
</b>
<div id="duzenleniyor"></div>
</form>
</center>

<?php
}else{include ("login.php");}
?>