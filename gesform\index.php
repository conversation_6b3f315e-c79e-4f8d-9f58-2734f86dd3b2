<?php
include ("head.php");
?>

    
<!-- BAŞVURU FORMU BAŞ -->

<div class="container">
    <h1 class="title mb-20">Lisanssız Elektrik Üretim Başvuru Sayfası</h1>
    <div class="row">
        <div class="col-lg-9">
            <div class="info-card info-card-success mb-40" style="font-size:15px;">
				Kurulması planlanan lisanssız üretim tesisi için aynı ay içerisinde Şirketimiz internet sitesi başvuru formu aracılığıyla yalnızca bir kez başvuru yapılabilecek olup başvuru üzerinde düzeltme yapılamayacaktır. İlave olarak fiziki evrak alınmayacaktır.
			</div>
            <div style="display:none" class="info-card info-card-sm info-card-alert errorFormMessage mb-30"></div>
        </div>
    </div>

    <div class="container-detail-block pl-0">
        <div class="container-detail---shadow-box">
            <form id="lisanssizUretimBasvuruForm" enctype="multipart/form-data" action="formgonder.php" method="POST">

                <div class="row">
                    <div class="col-lg-12">
                        <h3 class="container-box--title mb-10">Tesis Sahibini Temsile Yetkili Gerçek / Tüzel Kişi Bilgileri</h3>
                    </div>
                    <div class="col-lg-8">
                        <div class="form-group floating text-input-full" style="height:40px;">
                            <input class="form-control floating" type="text" data-val="true" data-val-length="Bu alan 200 karakterden uzun olamaz!" data-val-length-max="200" id="TemsilBasvuruSahibi" maxlength="200" name="TemsilBasvuruSahibi" value="" />
                            <label for="TemsilBasvuruSahibi">Adı Soyadı</label>
                        </div>
                        <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="TemsilBasvuruSahibi" data-valmsg-replace="true"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-8">
                        <div class="form-group floating text-input-full" style="height:40px;">
                            <input class="form-control floating" type="email" data-val="true" data-val-email="E-posta adresi geçerli değildir, lütfen geçerli bir e-posta adresi giriniz." data-val-length="Bu alan 100 karakterden uzun olamaz!" data-val-length-max="100" id="TemsilMailAdresi" maxlength="100" name="TemsilMailAdresi" value="" />
                            <label for="TemsilMailAdresi">E-posta Adresi</label>
                        </div>
                        <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="TemsilMailAdresi" data-valmsg-replace="true"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-8">
                        <div class="form-group floating text-input-full" style="height:40px;">
                            <input inputmode="numeric" class="form-control floating" type="text" data-val="true" data-val-length="Bu alan 50 karakterden uzun olamaz!" data-val-length-max="50" id="TemsilTelNo" maxlength="50" name="TemsilTelNo" value="" />
                            <label for="TemsilTelNo">Cep Telefon No - (5xx - xxx - xx - xx)</label>
                        </div>
                        <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="TemsilTelNo" data-valmsg-replace="true"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-8 mb-20">
                        <div class="form-row-textarea">
                            <textarea class="form-control input-textarea" rows="3" maxlength="1800" placeholder="Yazışma Adresi" data-val="true" data-val-length="Bu alan 1800 karakterden uzun olamaz!" data-val-length-max="1800" id="TemsilYazismaAdresi" name="TemsilYazismaAdresi"></textarea>
                            <span class="max_char_info char_count_js">Max. 1800 karakter</span>
                        </div>
                        <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="TemsilYazismaAdresi" data-valmsg-replace="true"></span>
                    </div>
                </div>


                <div class="row">
                    <div class="col-lg-12">
                        <h3 class="container-box--title mb-10">Tesis Sahibinin Bilgileri</h3>
                    </div>
                    <div class="col-lg-8">
                        <select class="selectpicker" data-display="static" data-show-subtext="true" data-val="true" data-val-required="&#8657; <font style='color:darkred;'>Başvuru Türü</font> alanı boş geçilemez." id="SelectedBasvuruTuru" name="SelectedBasvuruTuru" title="Başvuru Türü">
						<option value="0001">Lisanssız Üretim Tesisleri</option>
						<option value="0002">25 kW Altı GES Tesisleri</option>
						</select>
                        <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="SelectedBasvuruTuru" data-valmsg-replace="true"></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-8">
                        <div class="form-group floating text-input-full" style="height:40px;">
                            <input class="form-control floating" required="required" type="text" data-val="true" data-val-length="Bu alan 200 karakterden uzun olamaz!" data-val-length-max="200" data-val-required="&#8657; <font style='color:darkred;'>Başvuru Sahibi Adı Soyadı</font> alanı boş geçilemez." id="BasvuruSahibi" maxlength="200" name="BasvuruSahibi" value="" />
                            <label for="BasvuruSahibi">Başvuru Sahibi Adı Soyadı</label>
                        </div>
                        <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="BasvuruSahibi" data-valmsg-replace="true"></span>
                    </div>
                </div>


                <div class="row">
                    <div class="col-lg-8">
                        <select class="selectpicker" data-display="static" data-show-subtext="true" data-val="true" data-val-required="&#8657; <font style='color:darkred;'>Başvuru Sahibi Kişi Türü</font> alanı boş geçilemez." id="SelectedBasvuruSahibiKisiTuru" name="SelectedBasvuruSahibiKisiTuru" title="Başvuru Sahibi Kişi Türü">
						<option value="G">Gerçek Kişi</option>
						<option value="T">Tüzel Kişi</option>
						<option value="K">Kamu</option>
						<option value="O">Kooperatif</option>
						</select>
                        <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="SelectedBasvuruSahibiKisiTuru" data-valmsg-replace="true"></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-8">
                        <div class="form-group floating text-input-full" style="height:40px;">
                            <input class="form-control floating" required="required" type="text" data-val="true" data-val-length="Vergi/Kimlik No 10/11 hane olmalıdır." data-val-length-max="11" data-val-length-min="10" data-val-required="&#8657; <font style='color:darkred;'>Vergi/Kimlik No</font> alanı boş geçilemez." id="VergiKimlikNumarasi" maxlength="11" name="VergiKimlikNumarasi" value="" />
                            <label for="VergiKimlikNumarasi">Vergi/Kimlik No</label>
                        </div>
                        <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="VergiKimlikNumarasi" data-valmsg-replace="true"></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-8">
                        <div class="form-group floating text-input-full" style="height:40px;">
                            <input class="form-control floating" required="required" type="email" data-val="true" data-val-email="E-posta adresi geçerli değildir, lütfen geçerli bir e-posta adresi giriniz." data-val-length="Bu alan 100 karakterden uzun olamaz!" data-val-length-max="100" data-val-required="&#8657; <font style='color:darkred;'>E-posta Adresi</font> alanı boş geçilemez." id="MailAdresi" maxlength="100" name="MailAdresi" value="" />
                            <label for="MailAdresi">E-posta Adresi</label>
                        </div>
                        <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="MailAdresi" data-valmsg-replace="true"></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-8">
                        <div class="form-group floating text-input-full" style="height:40px;">
                            <input class="form-control floating" type="email" data-val="true" data-val-email="Kep adresi geçerli değildir, lütfen geçerli bir mail adresi giriniz." data-val-length="Bu alan 100 karakterden uzun olamaz!" data-val-length-max="100" id="KepAdresi" maxlength="100" name="KepAdresi" value="" />
                            <label for="KepAdresi">Kep Adresi</label>
                        </div>
                        <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="KepAdresi" data-valmsg-replace="true"></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-8">
                        <div class="form-group floating text-input-full" style="height:40px;">
                            <input inputmode="numeric" class="form-control floating" required="required" type="text" data-val="true" data-val-length="Bu alan 50 karakterden uzun olamaz!" data-val-length-max="50" data-val-required="&#8657; <font style='color:darkred;'>Telefon no</font> alanı boş geçilemez." id="TelNo" maxlength="50" name="TelNo" value="" />
                            <label for="TelNo">Cep Telefon No - (5xx - xxx - xx - xx)</label>
                        </div>
                        <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="TelNo" data-valmsg-replace="true"></span>
                    </div>
                </div>


                <div class="row">
                    <div class="col-lg-8 mb-20">
                        <div class="form-row-textarea">
                            <textarea class="form-control input-textarea" rows="3" required="required" maxlength="1800" placeholder="Yazışma Adresi" data-val="true" data-val-length="Bu alan 1800 karakterden uzun olamaz!" data-val-length-max="1800" data-val-required="&#8657; <font style='color:darkred;'>İletişim-Yazışma Adresi</font> alanı boş geçilemez." id="YazismaAdresi" name="YazismaAdresi"></textarea>
                            <span class="max_char_info char_count_js">Max. 1800 karakter</span>
                        </div>
                        <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="YazismaAdresi" data-valmsg-replace="true"></span>
                    </div>
                </div>


                <div class="row">
                    <div class="col-lg-12">
                        <h3 class="container-box--title mb-10">Proje Bilgileri</h3>
                    </div>
                    <div class="col-lg-12 mb-20">
                        <label class="input-checkbox">
                            Güç Arttırım
                            <img id="guc-arttirimi-img" src="img/info-small-black.svg" alt="bilgi simgesi" class="warning" data-container="body" data-toggle="popover" data-trigger="hover" data-html="true" data-placement="right" data-content="<div><h1>Mevcut üretim santraliniz ile mahsuplaşan tüketim tesisatınız ile güç arttırımı başvurusu yapacaksanız bu alanı işaretleyiniz</h1></div>">
                            <input type="checkbox" data-val="true" data-val-required="&#8657; <font style='color:darkred;'>The Güç Arttırım</font> field is required." id="GucArttirim" name="GucArttirim" value="true" />
                            <span class="checkmark"></span>
                        </label>
                        <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="GucArttirim" data-valmsg-replace="true"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-8">
                        <div class="form-group floating text-input-full" style="height:40px;">
                            <input class="form-control floating" required="required" type="text" data-val="true" data-val-length="Bu alan 200 karakterden uzun olamaz!" data-val-length-max="200" data-val-required="<font style='color:darkred;'>Proje Adı</font> alanı boş geçilemez." id="ProjeAdi" maxlength="200" name="ProjeAdi" value="" />
                            <label for="ProjeAdi">Proje Adı</label>
                        </div>
                        <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="ProjeAdi" data-valmsg-replace="true"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-8">
                        <select class="selectpicker" data-display="static" data-show-subtext="true" data-size="6" data-val="true" data-val-required="&#8657; <font style='color:darkred;'>Üretim-Tüketim Yeri</font> alanı boş geçilemez." id="SelectedUretimTuketimYeri" name="SelectedUretimTuketimYeri" title="Üretim Tüketim Tesisi">
						<option value="AYNI">Aynı Yerde</option>
						<option value="FARKLI">Farklı Yerde</option>
						</select>
                        <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="SelectedUretimTuketimYeri" data-valmsg-replace="true"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-8">
                        <select class="selectpicker" data-display="static" data-show-subtext="true" data-size="6" data-val="true" data-val-required="&#8657; <font style='color:darkred;'>Uygulama Yeri</font> alanı boş geçilemez." id="SelectedUygulamaYeri" name="SelectedUygulamaYeri" title="Uygulama Yeri">
						<option value="03">Arazi</option>
						<option value="04">Çatı-Cephe</option>
						<option value="05">Arazi-Çatı-Cephe</option>
						<option value="06">Diğer</option>
						</select>
                        <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="SelectedUygulamaYeri" data-valmsg-replace="true"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-8">
                        <select class="selectpicker" data-display="static" data-show-subtext="true" data-size="6" data-val="true" data-val-required="&#8657; <font style='color:darkred;'>Üretim Santralinin Kurulacağı İl</font> alanı boş geçilemez." id="SelectedIl" name="SelectedIl" title="İlçe / İL">
						<option value="3801_Melikgazi">Melikgazi / KAYSERİ</option>
						<option value="3802_Hacilar">Hacılar / KAYSERİ</option>
						</select>
                        <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="SelectedIl" data-valmsg-replace="true"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-8">
                        <div class="form-group floating text-input-full" style="height:40px;">
                            <input class="form-control floating" required="required" type="text" data-val="true" data-val-length="Bu alan 200 karakterden uzun olamaz!" data-val-length-max="200" data-val-required="&#8657; <font style='color:darkred;'>Üretim Santralinin Kurulucağı Mevki-Köy-Mahalle</font> alanı boş geçilemez." id="MahalleMevkiKoy" maxlength="200" name="MahalleMevkiKoy" value="" />
                            <label for="MahalleMevkiKoy">Üretim Santralinin Kurulucağı Mevki-Köy-Mahalle</label>
                        </div>
                        <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="MahalleMevkiKoy" data-valmsg-replace="true"></span>
                    </div>
                </div>


                <div class="row">
                    <div class="col-lg-8">
                        <div class="form-group floating text-input-full" style="height:40px;">
                            <input class="form-control floating" required="required" type="number" data-val="true" data-val-length="Bu alan 50 karakterden uzun olamaz!" data-val-length-max="50" data-val-required="&#8657; <font style='color:darkred;'>Üretim Santralinin Kurulucağı Ada</font> alanı boş geçilemez." id="Ada" name="Ada" value="" />
                            <label for="Ada">Üretim Santralinin Kurulucağı Ada</label>
                        </div>
                        <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="Ada" data-valmsg-replace="true"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-8">
                        <div class="form-group floating text-input-full" style="height:40px;">
                            <input class="form-control floating" required="required" type="text" data-val="true" data-val-length="Bu alan 50 karakterden uzun olamaz!" data-val-length-max="50" data-val-required="&#8657; <font style='color:darkred;'>Üretim Santralinin Kurulucağı Parsel</font> alanı boş geçilemez." id="Parsel" maxlength="50" name="Parsel" value="" />
                            <label for="Parsel">Üretim Santralinin Kurulucağı Parsel</label>
                        </div>
                        <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="Parsel" data-valmsg-replace="true"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-8">
                        <div class="form-group floating text-input-full" style="height:40px;">
                            <input class="form-control floating" required="required" type="text" data-val="true" data-val-length="Bu alan 50 karakterden uzun olamaz!" data-val-length-max="50" data-val-required="&#8657; <font style='color:darkred;'>Geçici Parsel</font> alanı boş geçilemez." id="GeciciParsel" maxlength="50" name="GeciciParsel" value="" />
                            <label for="GeciciParsel">Geçici Parsel</label>
                        </div>
                        <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="GeciciParsel" data-valmsg-replace="true"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-8">
                        <div class="form-group floating text-input-full" style="height:40px;">
                            <input class="form-control floating" required="required" type="text" max="999999,999" data-val="true" data-val-length="Bu alan 9 karakterden uzun olamaz!" data-val-length-max="9" data-val-regex="Girilen değer sayı ya da ondalık sayı olmalıdır." data-val-regex-pattern="^[0-9]\d{0,9}(\.\d{1,3})?%?$" data-val-required="&#8657; <font style='color:darkred;'>Üretim Tesisi DC Kurulu Güç (kWp)</font> alanı boş geçilemez." id="UretimTesisiDCKuruluGuc" maxlength="9" name="UretimTesisiDCKuruluGuc" value="" />
                            <label for="UretimTesisiDCKuruluGuc">Üretim Tesisi DC Kurulu Güç (kWp)</label>
                        </div>
                        <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="UretimTesisiDCKuruluGuc" data-valmsg-replace="true"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-8">
                        <div class="form-group floating text-input-full" style="height:40px;">
                            <input class="form-control floating" required="required" type="text" max="999999,999" data-val="true" data-val-length="Bu alan 9 karakterden uzun olamaz!" data-val-length-max="9" data-val-regex="Girilen değer sayı ya da ondalık sayı olmalıdır." data-val-regex-pattern="^[0-9]\d{0,9}(\.\d{1,3})?%?$" data-val-required="&#8657; <font style='color:darkred;'>Üretim Tesisi AC Kurulu Güç (kWe)</font> alanı boş geçilemez." id="UretimTesisiACKuruluGuc" maxlength="9" name="UretimTesisiACKuruluGuc" value="" />
                            <label for="UretimTesisiACKuruluGuc">Üretim Tesisi AC Kurulu Güç (kWe)</label>
                        </div>
                        <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="UretimTesisiACKuruluGuc" data-valmsg-replace="true"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-8">
                        <select class="selectpicker" data-display="static" data-show-subtext="true" data-size="6" data-val="true" data-val-required="&#8657; <font style='color:darkred;'>Kaynak Türü</font> alanı boş geçilemez." id="SelectedKaynakTuru" name="SelectedKaynakTuru" title="Kaynak Türü">
						<option value="GÜNEŞ">Güneş</option>						
						</select>
						
                        <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="SelectedKaynakTuru" data-valmsg-replace="true"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-8">
                        <select class="selectpicker" data-display="static" data-show-subtext="true" data-size="6" data-val="true" data-val-required="&#8657; <font style='color:darkred;'>Ölçü Gerilim Seviyesi</font> alanı boş geçilemez." id="SelectedOlcuGerilimSeviyesi" name="SelectedOlcuGerilimSeviyesi" title="Bağlantı Şekli">
						<option value="AG">AG</option>
						<option value="OG">OG</option>
						</select>
                        <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="SelectedOlcuGerilimSeviyesi" data-valmsg-replace="true"></span>
                    </div>
                </div>





                <div class="row">
				<table border="0px" class="col-lg-8">
				<tr>
				<td align="left">
                    <div class="col-lg-12 form-row-textarea" style="text-align:left;">
                        <textarea class="form-control input-textarea" rows="3" maxlength="1800" data-val="true" data-val-length="Bu alan 1800 karakterden uzun olamaz!" data-val-length-max="1800" name="UretimSantralEnlem" data-val-required="&#8657; <font style='color:darkred;'>ENLEM</font> boş geçilemez." placeholder=
"11.11111111
11.11111111
11.11111111"></textarea>
						<span class="max_char_info_koor_1">Üretim Santrali <b>ENLEM</b> Değerleri (X)</span>
						<span class="max_char_info_koor_2"><b>ENLEM</b> değerlerini alt alta ekleyin.</span>
                    </div>
					<span class="text-danger mb-2 field-validation-valid" data-valmsg-for="UretimSantralEnlem" data-valmsg-replace="true"></span>
				</td>
				<td>
                    <div class="col-lg-12 form-row-textarea">
                        <textarea class="form-control input-textarea" rows="3" maxlength="1800" data-val="true" data-val-length="Bu alan 1800 karakterden uzun olamaz!" data-val-length-max="1800" name="UretimSantralBoylam" data-val-required="&#8657; <font style='color:darkred;'>BOYLAM</font> boş geçilemez." placeholder=
"11.11111111
11.11111111
11.11111111"></textarea>
						<span class="max_char_info_koor_1">Üretim Santrali <b>BOYLAM</b> Değerleri (Y)</span>
						<span class="max_char_info_koor_2"><b>BOYLAM</b> değerlerini alt alta ekleyin.</span>
                    </div>
                    <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="UretimSantralBoylam" data-valmsg-replace="true"></span>
				</td>
				</tr>
				</table>
				</div>

                <div class="row">
                    <div class="col-lg-8">
                        <select class="selectpicker" data-display="static" data-show-subtext="true" data-size="6" data-val="true" data-val-required="&#8657; <font style='color:darkred;'>Abonelik Seçimi</font> boş geçilemez." id="SelectedAbonelik" name="SelectedAbonelik" title="Abonelik Seçimi">
						<option value="1">Tüketim Aboneliği Henüz Oluşmamıştır</option>
						<option value="2">Tüketim Aboneliği Organize Sanayi Bölgesindedir</option>
						</select>
                        <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="SelectedAbonelik" data-valmsg-replace="true"></span>
                    </div>
                </div>


                <div class="row">
                    <div class="col-lg-8">
                        <select class="selectpicker" data-display="static" data-show-subtext="true" data-size="6" data-val="true" data-val-required="&#8657; <font style='color:darkred;'>İlgili Yönetmelik Maddesi</font> alanı boş geçilemez." id="SelectedIlgiliYonetmelikMaddesi" name="SelectedIlgiliYonetmelikMaddesi" title="İlgili Yönetmelik Maddesi">
						<option value="01">25 kW Usul ve Esas</option>
						<option value="02">11. Madde 2. Fıkra</option>
						<option value="03">11. Madde 3. Fıkra</option>
						<option value="04">30. Madde 1. Fıkra</option>
						<option value="05">5. Madde 1. Fıkra (a) bendi</option>
						<option value="06">5. Madde 1. Fıkra (b) bendi</option>
						<option value="07">5. Madde 1. Fıkra (c) bendi</option>
						<option value="08">5. Madde 1. Fıkra (ç) bendi</option>
						<option value="09">5. Madde 1. Fıkra (d) bendi</option>
						<option value="10">5. Madde 1. Fıkra (e) bendi</option>
						<option value="11">5. Madde 1. Fıkra (f) bendi</option>
						<option value="12">5. Madde 1. Fıkra (g) bendi</option>
						<option value="16">5. Madde 1. Fıkra (h) bendi</option>
						<option value="13">Mülga yönetmelik 7. Madde 7. Fıkra</option>
						<option value="14">Mülga yönetmelik 7. Madde 8. Fıkra</option>
						<option value="15">Diğer</option>
						</select>
                        <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="SelectedIlgiliYonetmelikMaddesi" data-valmsg-replace="true"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-8 mb-20">
                        <div class="form-row-textarea">
                            <textarea class="form-control input-textarea" rows="3" maxlength="1800" placeholder="Başvurunuzla ilgili belirtmek istediğiniz ilave hususları burada belirtebilirsiniz" data-val="true" data-val-length="Bu alan 1800 karakterden uzun olamaz!" data-val-length-max="1800" id="Aciklama" name="Aciklama"></textarea>
                            <span class="max_char_info char_count_js">Max. 1800 karakter</span>
                        </div>
                        <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="Aciklama" data-valmsg-replace="true"></span>
                    </div>
                </div>

                <div id="basvuruEvraklari">
                    <div id="LisanssizUretimTesisleriEvraklari" style="display:none">
					



                        <div class="row" id="BelgelerBaslik">
                            <div class="col-lg-12 mt-10">
                                <p class="belge_baslik">
									YÜKLENECEK BELGELER
								</p>
                                <p class="belge_info">
                                    <img src="img/info-small-yellow.svg" style="width:20px;" />
                                    - Yükleyeceğiniz dökümanların okunabilir olduğundan emin olunuz!. Okunamayan evrak, başvurunuzun reddedilmesine sebep olacaktır!.
									<br>
									<img src="img/info-small-yellow.svg" style="width:20px;" />
									- Her bir yükleme alanına tek bir evrak yüklenebilir. Aynı konu altında birden fazla evrak var ise onları birleştirip tek bir evrak yaptıktan sonra yükleyiniz.
									<br>
									<img src="img/info-small-yellow.svg" style="width:20px;" />
									- <b>(<font color="orange">*</font>)</b> (<font color="orange">Yıldız</font>)'lı evraklar zorunlu evraklardır. Boş geçemezsiniz!.
                                </p>
                            </div>
                        </div>
					<hr />



                        <div class="row" id="BasvuruDilekce">
                            <div class="col-lg-12 mt-10">
                                <p class="belge_baslik">
									1 - (<font color="orange">*</font>) Lisanssız Üretim Başvuru Dilekçesi
								</p>
                                <p class="belge_info">
                                    <img src="img/info-small-yellow.svg" style="width:20px;" />
                                    Yüklenecek dokümanın <b>E-imza</b> ile imzalanmış olması gerekmektedir. Aksi takdirde başvurunuz kabul edilmeyecektir.
                                </p>
                            </div>
							
                            <div class="col-lg-8">
                                <div class="file-input">
                                    <input type="file" accept=".jpeg, .tif, .png, .pdf" name="BasvuruDilekce" required />
                                    <div class="img-button">
                                        <img src="img/icon-plus.svg" alt="belge ekle" />
                                        <span>Belge Ekle</span>
                                    </div>
                                    <div class="img-show" style="display:none;">
                                        <img class="remove" src="img/icon-plus.svg" alt="kaldır" />
                                        <img class="preview" src="#" alt="önizleme" />
                                        <button style="display:none;" type="button" class="btn btn-sm btn-success mt-5 float-left show-file">Görüntüle</button>
                                    </div>
                                </div>
                                <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="BasvuruDilekce" data-valmsg-replace="true"></span>
                            </div>
                        </div>
					<hr />
                        <div class="row" id="BasvuruFormuEk1">
                            <div class="col-lg-12 mt-10">
                                <p class="belge_baslik">
									2 - (<font color="orange">*</font>) Lisanssız Üretim Bağlantı Başvuru Formu (EK-1)
								</p>
                                <p class="belge_info">
                                    <img src="img/info-small-yellow.svg" style="width:20px;" />
                                    Yüklenecek dokümanın <b>E-imza</b> ile imzalanmış olması gerekmektedir. Aksi takdirde başvurunuz kabul edilmeyecektir.
                                </p>
                            </div>
                            <div class="col-lg-8">
                                <div class="file-input">
                                    <input type="file" accept=".jpeg, .tif, .png, .pdf" id="BasvuruFormuEk1" name="BasvuruFormuEk1" required />
                                    <div class="img-button">
                                        <img src="img/icon-plus.svg" alt="belge ekle" />
                                        <span>Belge Ekle</span>
                                    </div>
                                    <div class="img-show" style="display:none;">
                                        <img class="remove" src="img/icon-plus.svg" alt="kaldır" />
                                        <img class="preview" src="#" alt="önizleme" />
                                        <button style="display:none;" type="button" class="btn btn-sm btn-success mt-5 float-left show-file">Görüntüle</button>
                                    </div>
                                </div>
                                <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="BasvuruFormuEk1" data-valmsg-replace="true"></span>
                            </div>
                        </div>
					<hr />
                        <div class="row" id="YetkiBelgeleri">
                            <div class="col-lg-12 mt-10">
                                <p class="belge_baslik">
									3 - (<font color="orange">*</font>) Tüzel Kişiyi Temsil ve İmzaya Yetkili Şahıs/Şahısların 'Yetki Belgeleri'
								</p>
								<p class="belge_info">
                                    <img src="img/info-small-yellow.svg" style="width:20px;" />
                                    (<b>İmza Sirküsü, İmza Beyannamesi, Vekaletname vs.</b> evraklarını birleştirip tek evrak olarak yükleyiniz.)
                                </p>
                            </div>
                            <div class="col-lg-8">
                                <div class="file-input">
                                    <input type="file" accept=".jpeg, .tif, .png, .pdf" id="YetkiBelgeleri" name="YetkiBelgeleri" required />
                                    <div class="img-button">
                                        <img src="img/icon-plus.svg" alt="belge ekle" />
                                        <span>Belge Ekle</span>
                                    </div>
                                    <div class="img-show" style="display:none;">
                                        <img class="remove" src="img/icon-plus.svg" alt="kaldır" />
                                        <img class="preview" src="#" alt="önizleme" />
                                        <button style="display:none;" type="button" class="btn btn-sm btn-success mt-5 float-left show-file">Görüntüle</button>
                                    </div>
                                </div>
                                <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="YetkiBelgeleri" data-valmsg-replace="true"></span>
                            </div>
                        </div>
					<hr />
                        <div class="row" id="OrtaklikYapisiBelgeleri">
                            <div class="col-lg-12 mt-10">
                                <p class="belge_baslik">
									4 - (<font color="orange">*</font>) Ortaklık Yapısını Gösterir Belgeler
								</p>
								<p class="belge_info">
                                    <img src="img/info-small-yellow.svg" style="width:20px;" />
                                    Başvuruda bulunan tüzel kişinin, tüzel kişilikte doğrudan veya dolaylı pay sahibi olan Gerçek veya Tüzel kişilerin Ortaklık Yapısını ve Varlığı Halinde Kontrol İlişkisini ortaya koyan bilgi ve belgeler.<br>(Ticaret Sicil Müdürlüğünden güncel hali alınmalıdır.)
                                </p>
                            </div>
                            <div class="col-lg-8">
                                <div class="file-input">
                                    <input type="file" accept=".jpeg, .tif, .png, .pdf" id="OrtaklikYapisiBelgeleri" name="OrtaklikYapisiBelgeleri" required />
                                    <div class="img-button">
                                        <img src="img/icon-plus.svg" alt="belge ekle" />
                                        <span>Belge Ekle</span>
                                    </div>
                                    <div class="img-show" style="display:none;">
                                        <img class="remove" src="img/icon-plus.svg" alt="kaldır" />
                                        <img class="preview" src="#" alt="önizleme" />
                                        <button style="display:none;" type="button" class="btn btn-sm btn-success mt-5 float-left show-file">Görüntüle</button>
                                    </div>
                                </div>
                                <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="OrtaklikYapisiBelgeleri" data-valmsg-replace="true"></span>
                            </div>
                        </div>
					<hr />
                        <div class="row" id="KiraSozlesmesi">
                            <div class="col-lg-12 mt-10">
                                <p class="belge_baslik">
									5 - (<font color="orange">*</font>) Tapu/Tahsis Belgesi ya da Kira Sözleşmesi
								</p>
                            </div>
                            <div class="col-lg-8">
                                <div class="file-input">
                                    <input type="file" accept=".jpeg, .tif, .png, .pdf" id="KiraSozlesmesi" name="KiraSozlesmesi" required />
                                    <div class="img-button">
                                        <img src="img/icon-plus.svg" alt="belge ekle" />
                                        <span>Belge Ekle</span>
                                    </div>
                                    <div class="img-show" style="display:none;">
                                        <img class="remove" src="img/icon-plus.svg" alt="kaldır" />
                                        <img class="preview" src="#" alt="önizleme" />
                                        <button style="display:none;" type="button" class="btn btn-sm btn-success mt-5 float-left show-file">Görüntüle</button>
                                    </div>
                                </div>
                                <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="KiraSozlesmesi" data-valmsg-replace="true"></span>
                            </div>
                        </div>
					<hr />
                        <div class="row" id="AboneNumarasiBelgesi">
                            <div class="col-lg-12 mt-10">
                                <p class="belge_baslik">
									6 - (<font color="orange">*</font>) Abone Numarası veya Numaralarını İçeren Belge
								</p>
                                <p class="belge_info">
                                    <img src="img/info-small-yellow.svg" style="width:20px;" />
                                    Güncel elektrik tüketim faturaları yüklenmelidir.
                                </p>
                            </div>
                            <div class="col-lg-8">
                                <div class="file-input">
                                    <input type="file" accept=".jpeg, .tif, .png, .pdf" id="AboneNumarasiBelgesi" name="AboneNumarasiBelgesi" required />
                                    <div class="img-button">
                                        <img src="img/icon-plus.svg" alt="belge ekle" />
                                        <span>Belge Ekle</span>
                                    </div>
                                    <div class="img-show" style="display:none;">
                                        <img class="remove" src="img/icon-plus.svg" alt="kaldır" />
                                        <img class="preview" src="#" alt="önizleme" />
                                        <button style="display:none;" type="button" class="btn btn-sm btn-success mt-5 float-left show-file">Görüntüle</button>
                                    </div>
                                </div>
                                <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="AboneNumarasiBelgesi" data-valmsg-replace="true"></span>
                            </div>
                        </div>
					<hr />
                        <div class="row" id="YapiRuhsati">
                            <div class="col-lg-12 mt-10">
                                <p class="belge_baslik">
									7 - (<font color="orange">*</font>) Yapı Ruhsatı ve Yapı Kullanma İzin Belgesi
								</p>
                            </div>
                            <div class="col-lg-8">
                                <div class="file-input">
                                    <input type="file" accept=".jpeg, .tif, .png, .pdf" id="YapiRuhsati" name="YapiRuhsati" required />
                                    <div class="img-button">
                                        <img src="img/icon-plus.svg" alt="belge ekle" />
                                        <span>Belge Ekle</span>
                                    </div>
                                    <div class="img-show" style="display:none;">
                                        <img class="remove" src="img/icon-plus.svg" alt="kaldır" />
                                        <img class="preview" src="#" alt="önizleme" />
                                        <button style="display:none;" type="button" class="btn btn-sm btn-success mt-5 float-left show-file">Görüntüle</button>
                                    </div>
                                </div>
                                <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="YapiRuhsati" data-valmsg-replace="true"></span>
                            </div>
                        </div>
					<hr />
                        <div class="row" id="BasvuruBedeliDekontu">
                            <div class="col-lg-12 mt-10">
                                <p class="belge_baslik">
									8 - (<font color="orange">*</font>) Başvuru Bedeli Dekontu ya da Tahsilat Makbuzu 
								</p>
                                <p class="belge_info">
                                    <img src="img/info-small-yellow.svg" style="width:20px;" />
                                    (EPDK tarafından yıllık belirlenen bedel)
                                </p>
                            </div>
                            <div class="col-lg-8">
                                <div class="file-input">
                                    <input type="file" accept=".jpeg, .tif, .png, .pdf" id="BasvuruBedeliDekontu" name="BasvuruBedeliDekontu" required />
                                    <div class="img-button">
                                        <img src="img/icon-plus.svg" alt="belge ekle" />
                                        <span>Belge Ekle</span>
                                    </div>
                                    <div class="img-show" style="display:none;">
                                        <img class="remove" src="img/icon-plus.svg" alt="kaldır" />
                                        <img class="preview" src="#" alt="önizleme" />
                                        <button style="display:none;" type="button" class="btn btn-sm btn-success mt-5 float-left show-file">Görüntüle</button>
                                    </div>
                                </div>
                                <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="BasvuruBedeliDekontu" data-valmsg-replace="true"></span>
                            </div>
                        </div>
					<hr />
                        <div class="row" id="TekHatSemasi">
                            <div class="col-lg-12 mt-10">
                                <p class="belge_baslik">
									9 - (<font color="orange">*</font>) Kurulacak Tesisin Teknik Özelliklerini Gösteren Tek Hat Şeması
								</p>
                            </div>
                            <div class="col-lg-8">
                                <div class="file-input">
                                    <input type="file" accept=".jpeg, .tif, .png, .pdf" id="TekHatSemasi" name="TekHatSemasi" required />
                                    <div class="img-button">
                                        <img src="img/icon-plus.svg" alt="belge ekle" />
                                        <span>Belge Ekle</span>
                                    </div>
                                    <div class="img-show" style="display:none;">
                                        <img class="remove" src="img/icon-plus.svg" alt="kaldır" />
                                        <img class="preview" src="#" alt="önizleme" />
                                        <button style="display:none;" type="button" class="btn btn-sm btn-success mt-5 float-left show-file">Görüntüle</button>
                                    </div>
                                </div>
                                <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="TekHatSemasi" data-valmsg-replace="true"></span>
                            </div>
                        </div>
					<hr />
                        <div class="row" id="EigmTeknikDegerlendirmeFormuPdf">
                            <div class="col-lg-12 mt-10">
                                <p class="belge_baslik">
									10 - (<font color="orange">*</font>) EİGM Teknik Değerlendirme Formu <b>(.pdf)</b>
								</p>
                                <p class="belge_info">
                                    <img src="img/info-small-yellow.svg" style="width:20px;" />
                                    Tesisin kurulacağı arazi köşe numaraları, ITRF(96)-3&deg; projeksiyon sisteminden girilmelidir.
                                </p>
                            </div>
                            <div class="col-lg-8">
                                <div class="file-input">
                                    <input type="file" accept=".jpeg, .tif, .png, .pdf" id="EigmTeknikDegerlendirmeFormuPdf" name="EigmTeknikDegerlendirmeFormuPdf" required />
                                    <div class="img-button">
                                        <img src="img/icon-plus.svg" alt="belge ekle" />
                                        <span>Belge Ekle</span>
                                    </div>
                                    <div class="img-show" style="display:none;">
                                        <img class="remove" src="img/icon-plus.svg" alt="kaldır" />
                                        <img class="preview" src="#" alt="önizleme" />
                                        <button style="display:none;" type="button" class="btn btn-sm btn-success mt-5 float-left show-file">Görüntüle</button>
                                    </div>
                                </div>
                                <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="EigmTeknikDegerlendirmeFormuPdf" data-valmsg-replace="true"></span>
                            </div>
                        </div>
					<hr />
                        <div class="row" id="EigmTeknikDegerlendirmeFormuXlsx">
                            <div class="col-lg-12 mt-10">
                                <p class="belge_baslik">
									11 - (<font color="orange">*</font>) EİGM Teknik Değerlendirme Formu <b>(.xlsx)</b>
								</p>
                                <p class="belge_info">
                                    <img src="img/info-small-yellow.svg" style="width:20px;" />
                                    Tesisin kurulacağı arazi köşe numaraları, ITRF(96)-3&deg; projeksiyon sisteminden girilmelidir.
                                </p>
                            </div>
                            <div class="col-lg-8">
                                <div class="file-input">
                                    <input type="file" accept=".xlsx" id="EigmTeknikDegerlendirmeFormuXlsx" name="EigmTeknikDegerlendirmeFormuXlsx" />
                                    <div class="img-button">
                                        <img src="img/icon-plus.svg" alt="belge ekle" />
                                        <span>Belge Ekle</span>
                                    </div>
                                    <div class="img-show" style="display:none;">
                                        <img class="remove" src="img/icon-plus.svg" alt="kaldır" />
                                        <img class="preview" src="#" alt="önizleme" />
                                        <button style="display:none;" type="button" class="btn btn-sm btn-success mt-5 float-left show-file">Görüntüle</button>
                                    </div>
                                </div>
                                <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="EigmTeknikDegerlendirmeFormuXlsx" data-valmsg-replace="true"></span>
                            </div>
                        </div>
					<hr />
                        <div class="row" id="KoordinatliAplikasyonKrokisi">
                            <div class="col-lg-12 mt-10">
                                <p class="belge_baslik">
									12 - (<font color="orange">*</font>) Koordinatlı Aplikasyon Krokisi
								</p>
                            </div>
                            <div class="col-lg-8">
                                <div class="file-input">
                                    <input type="file" accept=".jpeg, .tif, .png, .pdf" id="KoordinatliAplikasyonKrokisi" name="KoordinatliAplikasyonKrokisi" required />
                                    <div class="img-button">
                                        <img src="img/icon-plus.svg" alt="belge ekle" />
                                        <span>Belge Ekle</span>
                                    </div>
                                    <div class="img-show" style="display:none;">
                                        <img class="remove" src="img/icon-plus.svg" alt="kaldır" />
                                        <img class="preview" src="#" alt="önizleme" />
                                        <button style="display:none;" type="button" class="btn btn-sm btn-success mt-5 float-left show-file">Görüntüle</button>
                                    </div>
                                </div>
                                <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="KoordinatliAplikasyonKrokisi" data-valmsg-replace="true"></span>
                            </div>
                        </div>
					<hr />
                        <div class="row" id="FaaliyetYasaginaIliskinBeyanEk4">
                            <div class="col-lg-12 mt-10">
                                <p class="belge_baslik">
									13 - (<font color="orange">*</font>) Faaliyet Yasağına İlişkin Beyan (Ek-4)
								</p>
                                <p class="belge_info">
                                    <img src="img/info-small-yellow.svg" style="width:20px;" />
                                    Bu evrakta firma imza sirküsünde ki <b>yetkili kişinin</b> imzası gerekmektedir.
                                </p>
                            </div>
                            <div class="col-lg-8">
                                <div class="file-input">
                                    <input type="file" accept=".jpeg, .tif, .png, .pdf" id="FaaliyetYasaginaIliskinBeyanEk4" name="FaaliyetYasaginaIliskinBeyanEk4" required />
                                    <div class="img-button">
                                        <img src="img/icon-plus.svg" alt="belge ekle" />
                                        <span>Belge Ekle</span>
                                    </div>
                                    <div class="img-show" style="display:none;">
                                        <img class="remove" src="img/icon-plus.svg" alt="kaldır" />
                                        <img class="preview" src="#" alt="önizleme" />
                                        <button style="display:none;" type="button" class="btn btn-sm btn-success mt-5 float-left show-file">Görüntüle</button>
                                    </div>
                                </div>
                                <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="FaaliyetYasaginaIliskinBeyanEk4" data-valmsg-replace="true"></span>
                            </div>
                        </div>
					<hr />
                        <div class="row" id="TicaretSicilGazetesi">
                            <div class="col-lg-12 mt-10">
                                <p class="belge_baslik">
									14 - (<font color="orange">*</font>) Ticaret Sicil Gazetesi
								</p>
                            </div>
                            <div class="col-lg-8">
                                <div class="file-input">
                                    <input type="file" accept=".jpeg, .tif, .png, .pdf" id="TicaretSicilGazetesi" name="TicaretSicilGazetesi" required />
                                    <div class="img-button">
                                        <img src="img/icon-plus.svg" alt="belge ekle" />
                                        <span>Belge Ekle</span>
                                    </div>
                                    <div class="img-show" style="display:none;">
                                        <img class="remove" src="img/icon-plus.svg" alt="kaldır" />
                                        <img class="preview" src="#" alt="önizleme" />
                                        <button style="display:none;" type="button" class="btn btn-sm btn-success mt-5 float-left show-file">Görüntüle</button>
                                    </div>
                                </div>
                                <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="TicaretSicilGazetesi" data-valmsg-replace="true"></span>
                            </div>
                        </div>
					<hr />
                        <div class="row" id="SanayiSicilBelgesi">
                            <div class="col-lg-12 mt-10">
                                <p class="belge_baslik">
									15 - Sanayi Sicil Belgesi
								</p>
								<p class="belge_info">
                                    <img src="img/info-small-yellow.svg" style="width:20px;" />
                                    Abone türü; 'Sanayi Tarife' grubunda yer alan firmalar için zorunludur.
									<br>
                                    <img src="img/info-small-yellow.svg" style="width:20px;" />
                                    Vize tarihi geçerli evrak yükleyiniz.
                                </p>
                            </div>
                            <div class="col-lg-8">
                                <div class="file-input">
                                    <input type="file" accept=".jpeg, .tif, .png, .pdf" id="SanayiSicilBelgesi" name="SanayiSicilBelgesi" />
                                    <div class="img-button">
                                        <img src="img/icon-plus.svg" alt="belge ekle" />
                                        <span>Belge Ekle</span>
                                    </div>
                                    <div class="img-show" style="display:none;">
                                        <img class="remove" src="img/icon-plus.svg" alt="kaldır" />
                                        <img class="preview" src="#" alt="önizleme" />
                                        <button style="display:none;" type="button" class="btn btn-sm btn-success mt-5 float-left show-file">Görüntüle</button>
                                    </div>
                                </div>
                                <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="SanayiSicilBelgesi" data-valmsg-replace="true"></span>
                            </div>
                        </div>
                    </div>
					
					
					
					
					
					
                    <div id="KwAltiGesTesisleriEvraklari" style="display:none">
                        <div class="row" id="BasvuruDilekce2">
                            <div class="col-lg-12 mt-10">
                                <p class="belge_baslik">
									1 - (<font color="orange">*</font>) Lisanssız Üretim Başvuru Dilekçesi A
								</p>
                                <p class="belge_info">
                                    <img src="img/info-small-yellow.svg" style="width:20px;" />
                                    Yüklenecek dokümanın E-imza ile imzalanmış olması gerekmektedir. Aksi takdirde başvurunuz kabul edilmeyecektir.
                                </p>
                            </div>
                            <div class="col-lg-8">
                                <div class="file-input">
                                    <input type="file" accept=".jpeg, .tif, .png, .pdf" id="BasvuruDilekce2" name="BasvuruDilekce2" />
                                    <div class="img-button">
                                        <img src="img/icon-plus.svg" alt="belge ekle" />
                                        <span>Belge Ekle</span>
                                    </div>
                                    <div class="img-show" style="display:none;">
                                        <img class="remove" src="img/icon-plus.svg" alt="kaldır" />
                                        <img class="preview" src="#" alt="önizleme" />
                                        <button style="display:none;" type="button" class="btn btn-sm btn-success mt-5 float-left show-file">Görüntüle</button>
                                    </div>
                                </div>
                                <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="BasvuruDilekce2" data-valmsg-replace="true"></span>
                            </div>
                        </div>
					<hr />
                        <div class="row" id="BasvuruFormuEk12">
                            <div class="col-lg-12 mt-10">
                                <p class="belge_baslik">
									2 - (<font color="orange">*</font>) Lisanssız Üretim Bağlantı Başvuru Formu (EK-1)
								</p>
                                <p class="belge_info">
                                    <img src="img/info-small-yellow.svg" style="width:20px;" />
                                    Yüklenecek dokümanın E-imza ile imzalanmış olması gerekmektedir. Aksi takdirde başvurunuz kabul edilmeyecektir.
                                </p>
                            </div>
                            <div class="col-lg-8">
                                <div class="file-input">
                                    <input type="file" accept=".jpeg, .tif, .png, .pdf" id="BasvuruFormuEk12" name="BasvuruFormuEk12" />
                                    <div class="img-button">
                                        <img src="img/icon-plus.svg" alt="belge ekle" />
                                        <span>Belge Ekle</span>
                                    </div>
                                    <div class="img-show" style="display:none;">
                                        <img class="remove" src="img/icon-plus.svg" alt="kaldır" />
                                        <img class="preview" src="#" alt="önizleme" />
                                        <button style="display:none;" type="button" class="btn btn-sm btn-success mt-5 float-left show-file">Görüntüle</button>
                                    </div>
                                </div>
                                <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="BasvuruFormuEk12" data-valmsg-replace="true"></span>
                            </div>
                        </div>
					<hr />
                        <div class="row" id="YetkiBelgeleri2">
                            <div class="col-lg-12 mt-10">
                                <p class="belge_baslik">
									3 - (<font color="orange">*</font>) Tüzel Kişiyi Temsil ve İmzaya Yetkili Şahıs/Şahısların 'Yetki Belgeleri'
								</p>
								<p class="belge_info">
                                    <img src="img/info-small-yellow.svg" style="width:20px;" />
                                    (İmza Sirküsü, İmza Beyannamesi, Vekaletname)
                                </p>
                            </div>
                            <div class="col-lg-8">
                                <div class="file-input">
                                    <input type="file" accept=".jpeg, .tif, .png, .pdf" id="YetkiBelgeleri2" name="YetkiBelgeleri2" />
                                    <div class="img-button">
                                        <img src="img/icon-plus.svg" alt="belge ekle" />
                                        <span>Belge Ekle</span>
                                    </div>
                                    <div class="img-show" style="display:none;">
                                        <img class="remove" src="img/icon-plus.svg" alt="kaldır" />
                                        <img class="preview" src="#" alt="önizleme" />
                                        <button style="display:none;" type="button" class="btn btn-sm btn-success mt-5 float-left show-file">Görüntüle</button>
                                    </div>
                                </div>
                                <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="YetkiBelgeleri2" data-valmsg-replace="true"></span>
                            </div>
                        </div>
					<hr />
                        <div class="row" id="OrtaklikYapisiBelgeleri2">
                            <div class="col-lg-12 mt-10">
                                <p class="belge_baslik">
									4 - (<font color="orange">*</font>) Ortaklık Yapısını Gösterir Belgeler
								</p>
								<p class="belge_info">
                                    <img src="img/info-small-yellow.svg" style="width:20px;" />
                                    Başvuruda Bulunan Tüzel Kişinin, Tüzel Kişilikte Doğrudan Veya Dolaylı Pay Sahibi Olan Gerçek Veya Tüzel Kişilerin Ortaklık Yapısını ve Varlığı Halinde Kontrol İlişkisini Ortaya Koyan Bilgi Ve Belgeler.<br>(Ticaret Sicil Müdürlüğünden güncel hali alınarak)
                                </p>
                            </div>
                            <div class="col-lg-8">
                                <div class="file-input">
                                    <input type="file" accept=".jpeg, .tif, .png, .pdf" id="OrtaklikYapisiBelgeleri2" name="OrtaklikYapisiBelgeleri2" />
                                    <div class="img-button">
                                        <img src="img/icon-plus.svg" alt="belge ekle" />
                                        <span>Belge Ekle</span>
                                    </div>
                                    <div class="img-show" style="display:none;">
                                        <img class="remove" src="img/icon-plus.svg" alt="kaldır" />
                                        <img class="preview" src="#" alt="önizleme" />
                                        <button style="display:none;" type="button" class="btn btn-sm btn-success mt-5 float-left show-file">Görüntüle</button>
                                    </div>
                                </div>
                                <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="OrtaklikYapisiBelgeleri2" data-valmsg-replace="true"></span>
                            </div>
                        </div>
					<hr />
                        <div class="row" id="KiraSozlesmesi2">
                            <div class="col-lg-12 mt-10">
                                <p class="belge_baslik">
									5 - (<font color="orange">*</font>) Tapu/Tahsis Belgesi ya da Kira Sözleşmesi
								</p>
                            </div>
                            <div class="col-lg-8">
                                <div class="file-input">
                                    <input type="file" accept=".jpeg, .tif, .png, .pdf" id="KiraSozlesmesi2" name="KiraSozlesmesi2" />
                                    <div class="img-button">
                                        <img src="img/icon-plus.svg" alt="belge ekle" />
                                        <span>Belge Ekle</span>
                                    </div>
                                    <div class="img-show" style="display:none;">
                                        <img class="remove" src="img/icon-plus.svg" alt="kaldır" />
                                        <img class="preview" src="#" alt="önizleme" />
                                        <button style="display:none;" type="button" class="btn btn-sm btn-success mt-5 float-left show-file">Görüntüle</button>
                                    </div>
                                </div>
                                <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="KiraSozlesmesi2" data-valmsg-replace="true"></span>
                            </div>
                        </div>
					<hr />
                        <div class="row" id="AboneNumarasiBelgesi2">
                            <div class="col-lg-12 mt-10">
                                <p class="belge_baslik">
									6 - (<font color="orange">*</font>) Abone Numarası veya Numaralarını İçeren Belge
								</p>
                                <p class="belge_info">
                                    <img src="img/info-small-yellow.svg" style="width:20px;" />
                                    Güncel elektrik tüketim faturaları ya da Abone numarası henüz oluşturulmamış ise inşa halindeki veya güç artışı kapsamındaki tüketim tesisatına ait trafonun geçici kabulune ilişkin kabul tutanakları yüklenmelidir.
                                </p>
                            </div>
                            <div class="col-lg-8">
                                <div class="file-input">
                                    <input type="file" accept=".jpeg, .tif, .png, .pdf" id="AboneNumarasiBelgesi2" name="AboneNumarasiBelgesi2" />
                                    <div class="img-button">
                                        <img src="img/icon-plus.svg" alt="belge ekle" />
                                        <span>Belge Ekle</span>
                                    </div>
                                    <div class="img-show" style="display:none;">
                                        <img class="remove" src="img/icon-plus.svg" alt="kaldır" />
                                        <img class="preview" src="#" alt="önizleme" />
                                        <button style="display:none;" type="button" class="btn btn-sm btn-success mt-5 float-left show-file">Görüntüle</button>
                                    </div>
                                </div>
                                <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="AboneNumarasiBelgesi2" data-valmsg-replace="true"></span>
                            </div>
                        </div>
					<hr />
                        <div class="row" id="YapiRuhsati2">
                            <div class="col-lg-12 mt-10">
                                <p class="belge_baslik">
									7 - (<font color="orange">*</font>) Yapı Ruhsatı ve Yapı Kullanma İzin Belgesi
								</p>
                            </div>
                            <div class="col-lg-8">
                                <div class="file-input">
                                    <input type="file" accept=".jpeg, .tif, .png, .pdf" id="YapiRuhsati2" name="YapiRuhsati2" />
                                    <div class="img-button">
                                        <img src="img/icon-plus.svg" alt="belge ekle" />
                                        <span>Belge Ekle</span>
                                    </div>
                                    <div class="img-show" style="display:none;">
                                        <img class="remove" src="img/icon-plus.svg" alt="kaldır" />
                                        <img class="preview" src="#" alt="önizleme" />
                                        <button style="display:none;" type="button" class="btn btn-sm btn-success mt-5 float-left show-file">Görüntüle</button>
                                    </div>
                                </div>
                                <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="YapiRuhsati2" data-valmsg-replace="true"></span>
                            </div>
                        </div>
					<hr />
                        <div class="row" id="BasvuruBedeliDekontu2">
                            <div class="col-lg-12 mt-10">
                                <p class="belge_baslik">
									8 - (<font color="orange">*</font>) Başvuru Bedeli Dekontu ya da Tahsilat Makbuzu (EPDK tarafından yıllık belirlenen bedel)
								</p>
                            </div>
                            <div class="col-lg-8">
                                <div class="file-input">
                                    <input type="file" accept=".jpeg, .tif, .png, .pdf" id="BasvuruBedeliDekontu2" name="BasvuruBedeliDekontu2" />
                                    <div class="img-button">
                                        <img src="img/icon-plus.svg" alt="belge ekle" />
                                        <span>Belge Ekle</span>
                                    </div>
                                    <div class="img-show" style="display:none;">
                                        <img class="remove" src="img/icon-plus.svg" alt="kaldır" />
                                        <img class="preview" src="#" alt="önizleme" />
                                        <button style="display:none;" type="button" class="btn btn-sm btn-success mt-5 float-left show-file">Görüntüle</button>
                                    </div>
                                </div>
                                <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="BasvuruBedeliDekontu2" data-valmsg-replace="true"></span>
                            </div>
                        </div>
					<hr />
                        <div class="row" id="TekHatSemasi2">
                            <div class="col-lg-12 mt-10">
                                <p class="belge_baslik">
									9 - (<font color="orange">*</font>) Kurulacak Tesisin Teknik Özelliklerini Gösteren Tek Hat Şeması
								</p>
                            </div>
                            <div class="col-lg-8">
                                <div class="file-input">
                                    <input type="file" accept=".jpeg, .tif, .png, .pdf" id="TekHatSemasi2" name="TekHatSemasi2" />
                                    <div class="img-button">
                                        <img src="img/icon-plus.svg" alt="belge ekle" />
                                        <span>Belge Ekle</span>
                                    </div>
                                    <div class="img-show" style="display:none;">
                                        <img class="remove" src="img/icon-plus.svg" alt="kaldır" />
                                        <img class="preview" src="#" alt="önizleme" />
                                        <button style="display:none;" type="button" class="btn btn-sm btn-success mt-5 float-left show-file">Görüntüle</button>
                                    </div>
                                </div>
                                <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="TekHatSemasi2" data-valmsg-replace="true"></span>
                            </div>
                        </div>
					<hr />
                        <div class="row" id="EigmTeknikDegerlendirmeFormuPdf2">
                            <div class="col-lg-12 mt-10">
                                <p class="belge_baslik">
									10 - (<font color="orange">*</font>) EİGM Teknik Değerlendirme Formu(pdf)
								</p>
                                <p class="belge_info">
                                    <img src="img/info-small-yellow.svg" style="width:20px;" />
                                    Tesisin kurulacağı arazi köşe numaraları ITRF(96)-3&deg;
                                </p>
                            </div>
                            <div class="col-lg-8">
                                <div class="file-input">
                                    <input type="file" accept=".pdf" id="EigmTeknikDegerlendirmeFormuPdf2" name="EigmTeknikDegerlendirmeFormuPdf2" />
                                    <div class="img-button">
                                        <img src="img/icon-plus.svg" alt="belge ekle" />
                                        <span>Belge Ekle</span>
                                    </div>
                                    <div class="img-show" style="display:none;">
                                        <img class="remove" src="img/icon-plus.svg" alt="kaldır" />
                                        <img class="preview" src="#" alt="önizleme" />
                                        <button style="display:none;" type="button" class="btn btn-sm btn-success mt-5 float-left show-file">Görüntüle</button>
                                    </div>
                                </div>
                                <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="EigmTeknikDegerlendirmeFormuPdf2" data-valmsg-replace="true"></span>
                            </div>
                        </div>
					<hr />
                        <div class="row" id="EigmTeknikDegerlendirmeFormuXlsx2">
                            <div class="col-lg-12 mt-10">
                                <p class="belge_baslik">
									11 - (<font color="orange">*</font>) EİGM Teknik Değerlendirme Formu(xlsx)
								</p>
                            </div>
                            <div class="col-lg-8">
                                <div class="file-input">
                                    <input type="file" accept=".xlsx" id="EigmTeknikDegerlendirmeFormuXlsx2" name="EigmTeknikDegerlendirmeFormuXlsx2" />
                                    <div class="img-button">
                                        <img src="img/icon-plus.svg" alt="belge ekle" />
                                        <span>Belge Ekle</span>
                                    </div>
                                    <div class="img-show" style="display:none;">
                                        <img class="remove" src="img/icon-plus.svg" alt="kaldır" />
                                        <img class="preview" src="#" alt="önizleme" />
                                        <button style="display:none;" type="button" class="btn btn-sm btn-success mt-5 float-left show-file">Görüntüle</button>
                                    </div>
                                </div>
                                <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="EigmTeknikDegerlendirmeFormuXlsx2" data-valmsg-replace="true"></span>
                            </div>
                        </div>
					<hr />
                        <div class="row" id="KoordinatliAplikasyonKrokisi2">
                            <div class="col-lg-12 mt-10">
                                <p class="belge_baslik">
									12 - (<font color="orange">*</font>) Koordinatlı Aplikasyon Krokisi
								</p>
                            </div>
                            <div class="col-lg-8">
                                <div class="file-input">
                                    <input type="file" accept=".jpeg, .tif, .png, .pdf" id="KoordinatliAplikasyonKrokisi2" name="KoordinatliAplikasyonKrokisi2" />
                                    <div class="img-button">
                                        <img src="img/icon-plus.svg" alt="belge ekle" />
                                        <span>Belge Ekle</span>
                                    </div>
                                    <div class="img-show" style="display:none;">
                                        <img class="remove" src="img/icon-plus.svg" alt="kaldır" />
                                        <img class="preview" src="#" alt="önizleme" />
                                        <button style="display:none;" type="button" class="btn btn-sm btn-success mt-5 float-left show-file">Görüntüle</button>
                                    </div>
                                </div>
                                <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="KoordinatliAplikasyonKrokisi2" data-valmsg-replace="true"></span>
                            </div>
                        </div>
					<hr />
                        <div class="row" id="FaaliyetYasaginaIliskinBeyanEk42">
                            <div class="col-lg-12 mt-10">
                                <p class="belge_baslik">
									13 - (<font color="orange">*</font>) Faaliyet Yasağına İlişkin Beyan (Ek-4)
								</p>
                            </div>
                            <div class="col-lg-8">
                                <div class="file-input">
                                    <input type="file" accept=".jpeg, .tif, .png, .pdf" id="FaaliyetYasaginaIliskinBeyanEk42" name="FaaliyetYasaginaIliskinBeyanEk42" />
                                    <div class="img-button">
                                        <img src="img/icon-plus.svg" alt="belge ekle" />
                                        <span>Belge Ekle</span>
                                    </div>
                                    <div class="img-show" style="display:none;">
                                        <img class="remove" src="img/icon-plus.svg" alt="kaldır" />
                                        <img class="preview" src="#" alt="önizleme" />
                                        <button style="display:none;" type="button" class="btn btn-sm btn-success mt-5 float-left show-file">Görüntüle</button>
                                    </div>
                                </div>
                                <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="FaaliyetYasaginaIliskinBeyanEk42" data-valmsg-replace="true"></span>
                            </div>
                        </div>
					<hr />
                        <div class="row" id="TicaretSicilGazetesi2">
                            <div class="col-lg-12 mt-10">
                                <p class="belge_baslik">
									14 - (<font color="orange">*</font>) Ticaret Sicil Gazetesi
								</p>
                            </div>
                            <div class="col-lg-8">
                                <div class="file-input">
                                    <input type="file" accept=".jpeg, .tif, .png, .pdf" id="TicaretSicilGazetesi2" name="TicaretSicilGazetesi2" />
                                    <div class="img-button">
                                        <img src="img/icon-plus.svg" alt="belge ekle" />
                                        <span>Belge Ekle</span>
                                    </div>
                                    <div class="img-show" style="display:none;">
                                        <img class="remove" src="img/icon-plus.svg" alt="kaldır" />
                                        <img class="preview" src="#" alt="önizleme" />
                                        <button style="display:none;" type="button" class="btn btn-sm btn-success mt-5 float-left show-file">Görüntüle</button>
                                    </div>
                                </div>
                                <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="TicaretSicilGazetesi2" data-valmsg-replace="true"></span>
                            </div>
                        </div>
					<hr />
                        <div class="row" id="SanayiSicilBelgesi2">
                            <div class="col-lg-12 mt-10">
                                <p class="belge_baslik">
									15 - Sanayi Sicil Belgesi
								</p>
                            </div>
                            <div class="col-lg-8">
                                <div class="file-input">
                                    <input type="file" accept=".jpeg, .tif, .png, .pdf" id="SanayiSicilBelgesi2" name="SanayiSicilBelgesi2" />
                                    <div class="img-button">
                                        <img src="img/icon-plus.svg" alt="belge ekle" />
                                        <span>Belge Ekle</span>
                                    </div>
                                    <div class="img-show" style="display:none;">
                                        <img class="remove" src="img/icon-plus.svg" alt="kaldır" />
                                        <img class="preview" src="#" alt="önizleme" />
                                        <button style="display:none;" type="button" class="btn btn-sm btn-success mt-5 float-left show-file">Görüntüle</button>
                                    </div>
                                </div>
                                <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="SanayiSicilBelgesi2" data-valmsg-replace="true"></span>
                            </div>
                        </div>
                    </div>
					<hr />
					<hr />
                </div>


                <div class="row">
                    <div class="col-lg-8 mt-40 mb-40">
                        <label class="input-checkbox">
						Elektrik Piyasasında Lisanssız Elektrik Üretim Yönetmeliği kapsamında Şirketiniz internet sitesi başvuru formu aracılığıyla kurmayı planladığım lisanssız elektrik üretim tesisine dair sunmuş olduğum bilgilerin doğruluğunu; yanlış, hatalı, eksik bilgi olması durumunda yapmış olduğum lisanssız üretici başvurusunun iptal edileceğini kabul ederim.
                            <input type="checkbox" data-val="true" data-val-range="&#8657; Lütfen <font style='color:black;'>Taahhüt Metnini</font> onaylayınız.!" data-val-range-max="True" data-val-range-min="True" data-val-required="&#8657; Lütfen <font style='color:darkred;'>Taahhüt Metnini</font> onaylayınız.!" id="IsMetinOkudum1" name="IsMetinOkudum1" value="true" />
                            <span class="checkmark"></span>
                        </label>
                        <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="IsMetinOkudum1" data-valmsg-replace="true"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-8 mt-40 mb-40">
                        <label class="input-checkbox">
                            <a class="black-link" style="line-height:20px; color:#edaf00 !important" target="_blank" href="https://www.kayseriosb.org/tr/kvkk-aydinlatma-metni">
                                Kişisel Verilerin Korunması ve İşlenmesi Hakkındaki Aydınlatma Metnini
                            </a>okudum, onaylıyorum.
                            <input type="checkbox" data-val="true" data-val-range="&#8657; Lütfen <font style='color:black;'>KVKK Metnini</font> onaylayınız.!" data-val-range-max="True" data-val-range-min="True" data-val-required="&#8657; Lütfen <font style='color:darkred;'>KVKK Metnini</font> onaylayınız.!" id="IsMetinOkudum" name="IsMetinOkudum" value="true" />
                            <span class="checkmark"></span>
                        </label>
                        <span class="text-danger mb-2 field-validation-valid" data-valmsg-for="IsMetinOkudum" data-valmsg-replace="true"></span>
                    </div>
                </div>
                <div>
					<div class="g-recaptcha lisanssizUretimBasvuruForm" style="display:none" data-sitekey="6Lf-guEjAAAAACfe7xBwtEae8GLiLgBvQqmII-iE"></div>
                    <div id="captchaErrorMesajDiv" style="display:none" class="field-validation-error text-danger mb-2 col-lg-6"></div>

					<button type="submit" id="kisaOnListeBasvuruTamamlaBtn" class="button--default float-right mobile-100">Başvuruyu Tamamla</button>
                </div>
			</form>
        </div>
    </div>
</div>


<?php
include ("foot.php");
?>