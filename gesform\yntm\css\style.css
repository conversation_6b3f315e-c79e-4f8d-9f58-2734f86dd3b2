img {border:0px; }
form { padding: 0px; margin: 0px;}



body { 
	font-family: tahoma, verdana, arial, sans-serif;
	background-color: #000;
	font-size:14px;
	color:#000033;
	text-align:center;
		background: url('../resimler/ges5.jpg') fixed;
		background-position: center center;
		background-repeat: no-repeat;
		background-attachment: fixed;
		background-size: cover;
} 


.center
{
  align-items: center;
  justify-content: center;
  position: absolute;
  margin: auto;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: repeating-linear-gradient(rgba(0, 0, 0, 0.08), rgba(0, 0, 0, 0.08) 2px,
  rgba(0, 0, 0, 0.0) 2px, rgba(0, 0, 0, 0.0) 4px) fixed;
  z-index: -100;
}

.menuler td 
{ 
	-webkit-border-radius: 5px 5px 5px 5px;
    -moz-border-radius: 5px 5px 5px 5px;
    -ms-border-radius: 5px 5px 5px 5px;
    -o-border-radius: 5px 5px 5px 5px;
    border-radius: 5px 5px 5px 5px;
	border:1px solid #fff;
	width:111px; 
	height:25px; 
	text-align:center; 
	margin:0px; 
	padding:0px;
	background-color:#000033;
	color: orange;
	text-decoration:none;
	cursor: pointer;
}
.menuler td:hover 
{
	-webkit-border-radius: 5px 5px 5px 5px;
    -moz-border-radius: 5px 5px 5px 5px;
    -ms-border-radius: 5px 5px 5px 5px;
    -o-border-radius: 5px 5px 5px 5px;
    border-radius: 5px 5px 5px 5px;
	border:1px solid #fff;
	color: orange;
	width:111px; 
	height:25px; 
	text-align:center; 
	margin:0px; 
	padding:0px; 
	background-color:#4682B4;
	background-repeat:no-repeat; 
	background-position:top center;
	text-decoration:none;
	cursor: pointer;
}
.menuler a
{
	color:#fff;
	font-weight: bold;
	text-align:center;
	margin:0px;
	padding:0px;
	text-decoration:none;
}
.menuler a:hover
{
	color:#fff;
	font-weight: bold;
	text-align:center;
	margin:0px;
	padding:0px;
	text-decoration:none;
}


.menuler_red td 
{ 
	-webkit-border-radius: 5px 5px 5px 5px;
    -moz-border-radius: 5px 5px 5px 5px;
    -ms-border-radius: 5px 5px 5px 5px;
    -o-border-radius: 5px 5px 5px 5px;
    border-radius: 5px 5px 5px 5px;
	border:1px solid #000;
	width:111px; 
	height:25px; 
	text-align:center; 
	margin:0px; 
	padding:0px;
	background-color:#940707;
	text-decoration:none;
	cursor: pointer;
	color: white;
}
.menuler_red td:hover 
{
	-webkit-border-radius: 5px 5px 5px 5px;
    -moz-border-radius: 5px 5px 5px 5px;
    -ms-border-radius: 5px 5px 5px 5px;
    -o-border-radius: 5px 5px 5px 5px;
    border-radius: 5px 5px 5px 5px;
	border:1px solid orange;
	color:#fff;
	width:111px; 
	height:25px; 
	text-align:center; 
	margin:0px; 
	padding:0px; 
	background-color:#540606;
	background-repeat:no-repeat; 
	background-position:top center;
	text-decoration:none;
	cursor: pointer;
	color: white;
}
.menuler_red a
{
	color:#fff;
	font-weight: bold;
	text-align:center;
	margin:0px;
	padding:0px;
	text-decoration:none;
}
.menuler_red a:hover
{
	color:#fff;
	font-weight: bold;
	text-align:center;
	margin:0px;
	padding:0px;
	text-decoration:none;
}


.icerik_td_baslik td 
{
	border:1px solid #000;
	width:100px; 
	height:25px; 
	text-align:center; 
	margin:0px; 
	padding:0px;
	background-color:#6DBB2D;
	color: #121F07;
	text-decoration:none;
	cursor: pointer;
}


.top_btn {
	border:none;
	text-decoration:none;
}
.top_btn a {
	color:#fff;
	margin:0px;
	padding:0px;
	text-decoration:none;
}
.top_btn a:hover {
	color:#fff;
	margin:0px;
	padding:0px;
	text-decoration:none;
}



.top_btn_warn {
	border:none; 
	height:20px; 
}
.top_btn_warn a {
	padding-top:9px;
	display: inline-block;
	width:105px;
	height:20px; 
	background-image:url(../resimler/buton_1b.png);
	background-repeat: no-repeat;
	color: red;
	font-family: tahoma, Verdana, Arial, Helvetica, sans-serif;
	text-align:middle;
	text-decoration:none;
}
.top_btn_warn a:hover {
	background-image:url(../resimler/buton_1b.png);
	background-repeat: no-repeat;
	color:white; 
	text-align:middle;
	text-decoration:none;
}



.top_btn2 {
	border:none;
	height:20px; 

}
.top_btn2 a {
	padding-top:0px;
	display:inline-block;
	width:15px;
	height:15px; 
	background-image:url(../resimler/button-green.png);
	background-repeat: no-repeat;
	color:#000033;
	font-family: tahoma, Verdana, Arial, Helvetica, sans-serif;
	text-align:middle;
	text-decoration:none;
	margin-left: 1px;
}
.top_btn2 a:hover {
	background-image:url(../resimler/button-green.png);
	background-repeat: no-repeat;
	color:white; 
	text-align:middle;
	text-decoration:none;

}



.top_btn_warn2 {
	border:none; 
	height:20px; 
}
.top_btn_warn2 a {
	padding-top: 0px;
	display: inline-block;
	width: 25px;
	height: 25px; 
	background-image: url(../resimler/button-red.png);
	background-repeat: no-repeat;
	color: red;
	font-family: tahoma, Verdana, Arial, Helvetica, sans-serif;
	text-align: middle;
	text-decoration: none;
	margin-left: 3px;
}
.top_btn_warn2 a:hover {
	background-image: url(../resimler/button-red.png);
	background-repeat: no-repeat;
	color: white; 
	text-align: middle;
	text-decoration: none;
}




#div_top {
	-webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
    border-radius: 5px;
	margin:0px;
	padding: 2px 2px 2px 2px;
	color: yellow;
	border: 0px solid yellow;
	
}

#div_say {
	-webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
    border-radius: 5px;
	width:60px;
	height:15px;
	margin:0px;
	padding: 0px 5px 0px 0px;
	color: yellow;
	border: 1px solid #C8C8C8;
	background-color: rgba(0,0,0,0.4);
}

.div_arama {
	-webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -ms-border-radius: 10px;
    -o-border-radius: 10px;
    border-radius: 10px;
	width:1335px;
	height:96px;
	margin:0px;
	padding: 0px 0px 0 0;
	color: yellow;
	border: 1px solid white;
	background-color: rgba(0,0,0,0.6);
	vertical-align: middle;

}


.div_icerik
{
	-webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -ms-border-radius: 10px;
    -o-border-radius: 10px;
    border-radius: 10px;
	width:1328px;
	border: 1px solid white;
	vertical-align: middle;
	padding: 3px;
	background-color: #262222;
	background: rgba(0,0,0,0.6);

}

.div_sayfala 
{
	color: grey;
	font-size: 11px;
	font-weight: bold;
	text-align:center;
	border: 0px solid white;
	padding: 2px 0px 0px 0px;
}
.div_sayfala a
{
	color:#fff;
	font-weight: bold;
	font-size: 11px;
	text-align:center;
	text-decoration:none;
}
.div_sayfala a:hover
{
	color:orange;
	font-weight: bold;
	font-size: 11px;
	text-align:center;
	text-decoration:none;
}




.cf:before, .cf:after {content:"";display:table;}.cf:after{clear:both;}.cf{zoom:1;}
.form-wrapper {background: #444;background: rgba(0,0,0,.2);border-radius: 1px;margin: 4px auto 5px auto;padding: 0px;width: 252px;}
.form-wrapper input {background: #eee;border-radius: 3px 0 0 3px;border: 0;float: left;font: bold 15px 'lucida sans', 'trebuchet MS', 'Tahoma';height: 7px;padding: 10px 5px;width: 202px;}
.form-wrapper input:focus {background: #fff;box-shadow: 0 0 2px rgba(0,0,0,.8) inset;outline: 0;}
.form-wrapper input::-webkit-input-placeholder {color: #999;font-style: italic;font-weight: normal;}
.form-wrapper input:-moz-placeholder {color: #999;font-style: italic;font-weight: normal;}
.form-wrapper input:-ms-input-placeholder {color: #999;font-style: italic;font-weight: normal;}
.form-wrapper button {background: #335d30;border-radius: 0 0px 0px 0;border: 0;color: #fff;cursor: pointer;float: right;font: bold 15px/10px 'lucida sans', 'trebuchet MS', 'Tahoma';height: 27px;overflow: visible;padding: 0;position: relative;text-shadow: 0 -1px 0 rgba(0, 0 ,0, .3);text-transform: uppercase;width: 40px;}
.form-wrapper button:hover{background: #486d5c;}
.form-wrapper button:active,.form-wrapper button:focus{background: #000;outline: 0;}
.form-wrapper button:before {content: '';border-color: transparent #335d30 transparent;border-style: solid solid solid none;    border-width: 8px 8px 8px 0;left: -6px;position: absolute;top: 6px;}
.form-wrapper button:hover:before{border-right-color: #486d5c;}
.form-wrapper button:focus:before,
.form-wrapper button:active:before{border-right-color: #c42f2f;}
.form-wrapper button::-moz-focus-inner {border: 0;padding: 0;}


.m_button {
  background-color: #4CAF50; /* Green */
  border: none;
  color: white;
  padding: 1px 2px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 14px;
  margin: 0px 0px;
  cursor: pointer;
}

.m_button_blue {background-color: #008CBA;} /* Blue */
.m_button_red {background-color: #f44336;} /* Red */ 
.m_button_gray {background-color: #e7e7e7; color: black;} /* Gray */ 
.m_button_black {background-color: #555555;} /* Black */
.m_button_orange {background-color: orange;} /* Black */


.input_arama {
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    -ms-border-radius: 4px;
    -o-border-radius: 4px;
    border-radius: 4px;
    -webkit-box-shadow: 0 1px 0 #ccc, 0 -2px 5px rgba(0, 0, 0, 0.08) inset;
    -moz-box-shadow: 0 1px 0 #ccc, 0 -2px 5px rgba(0, 0, 0, 0.08) inset;
    -ms-box-shadow: 0 1px 0 #ccc, 0 -2px 5px rgba(0, 0, 0, 0.08) inset;
    -o-box-shadow: 0 1px 0 #ccc, 0 -2px 5px rgba(0, 0, 0, 0.08) inset;
    box-shadow: 0 1px 0 #ccc, 0 -2px 5px rgba(0, 0, 0, 0.08) inset;
    -webkit-transition: all 0.5s ease;
    -moz-transition: all 0.5s ease;
    -ms-transition: all 0.5s ease;
    -o-transition: all 0.5s ease;
    transition: all 0.5s ease;
    background: #000;
    border: 1px solid #d4e8a3;
    color: #ccc;
    font: 13px Helvetica, Arial, sans-serif;
    margin: 0 0 0px;
    padding: 5px 2px 5px 2px;
    width: 160px;
}
.input_arama:focus {
    -webkit-box-shadow: 0 0 2px #350104 inset;
    -moz-box-shadow: 0 0 2px #350104 inset;
    -ms-box-shadow: 0 0 2px #350104 inset;
    -o-box-shadow: 0 0 2px #350104 inset;
    box-shadow: 0 0 2px #350104 inset;
    background-color: #d4e8a3;
    border: 1px solid #000;
    outline: none;
	color: #000;
}
.ara_btn {

	border:0px solid #000;
	background-color: rgba(0, 0, 0, 0.0);
	background-image:url(../resimler/ara.png);
	background-size: contain;
	background-repeat: no-repeat;
	padding:18px;
	height:8px;
}


.input_btn {
	-webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -ms-border-radius: 3px;
    -o-border-radius: 3px;
    border-radius: 3px;
	border:1px solid #00A400;
	background-color: #E9FFF6;
	padding:1px 15px 1px 15px;
	height:20px;
	text-align:center;
	font-weight:bold;
	cursor: pointer;
}
.input_btn:hover{
	-webkit-border-radius: 9px;
    -moz-border-radius: 9px;
    -ms-border-radius: 9px;
    -o-border-radius: 9px;
    border-radius: 9px;
	border:1px solid #00A400;
	background-color: #C0FF00;
	padding:1px 15px 1px 15px;
	height:20px;
	text-align:center;
	cursor: pointer;
}


.table_radius_border td{
	-webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -ms-border-radius: 3px;
    -o-border-radius: 3px;
    border-radius: 3px;
	border:1px solid #000;
}

.table_info td{
	-webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -ms-border-radius: 3px;
    -o-border-radius: 3px;
    border-radius: 3px;
	border:2px solid green;
	text-align: center;
	align: center;
}

.table_border td{
	border:1px solid #000;
}




.headercss {
	padding:2px;
	padding-left:5px;
	font-size:35px;
	font-family: Georgia;
}





.ic_baslik {

	font-size:17px;
	font-weight:bold;
	font-family: Calibri;
	color:#FEECBB;
	height:25px;
	text-align:center;
}
.ic_num {
	padding:1px;	
	padding-left:2px;
	margin:0px;
	font-size:11px;
}
.ic_yazi {
	padding:1px;
	padding-left:2px;
	font-size:11px;
}
.ic_yazi_b {
	padding:1px;
	padding-left:2px;
	font-size:11px;
}
.ic_islem {
	padding:1px;
	font-size:11px;
}


.usercss {
	padding:1px;
	padding-left:2px;
	font-size:10px;
	text-align:center;
}
.isimcss {
	padding:0px 2px 0 2px;

	font-size:11px;
}
.isimcss a{
	padding:1px;
	padding-left:2px;
	font-size:11px;
	color: #2585cd;
	text-decoration: none;
}
.linkcss {
	padding: 1px;
	padding: 0 2px 0px 2px;
	font-size: 11px;
	vertical-align: middle;
	border: 0px;
	text-align: left;
}
.linkcss td{
	padding: 0 0 0px 0px;
	font-size: 11px;
	vertical-align: middle;
	border: 0px;
	text-align: center;
}
.linkcss a{
	color:#02197F;
	font-family:Calibri;
	font-weight: bold;
	font-size:13px;
	margin:0px;
	padding:0px;
	text-decoration:none;
	text-align: center;
}
.linkcss a:hover{
	color: white;
	font-family:Calibri;
	font-weight: bold;
	text-decoration:none;
}
.siracss {
	padding:0px 1px 0px 0px;
	margin:0px;
	font-size:11px;
	text-align:center;
	font-family:Calibri;
}
.siracss a{
	color:#02197F;
	font-weight: bold;
	text-decoration:none;
}
.durumcss {
	font-family:Calibri;
	font-weight: bold;
	font-size:11px;
	text-align:center;
}
.durumcss a{
	font-family:Calibri;
	font-weight: bold;
}


.topisim {
	font-size:10px;
	color:#009933;
}
.topisim a{
	padding:5px 5px 5px 5px;
	color:#000033;
	text-decoration:none;
}
.topisim a:hover{
	color:white;
	text-decoration:none;
}



.satir_1 {
	background-color: #C0FFC0;
	height: 21px;
}
.satir_2 {
	background-color: #FAE0A5;
	height: 21px;
}

.popup_1 {
	background-color: #B0E0E6;
	height: 11px;
	white-space: nowrap;
}
.popup_2 {
	background-color: #def0f2;
	height: 11px;
	white-space: nowrap;
	padding-right: 3px;
	text-align: right;
}
.popup_3 {
	background-color: #fff;
	height: 19px;
	font-weight: 200;
	min-width: 400px;
	max-width: 420px;
	white-space: wrap;
	padding-left: 3px;
	margin-left: 0px;
}
.popup_3 a{
	color: #06448a;
	font-weight: bold;
	text-decoration: none;
}
.popup_3 a:hover{
	color: darkred;
}
.popup_4 {
	background-color: #879fa2;
	height: 31px;
	white-space: nowrap;
	padding-left: 3px;
}
.popup_5 {
	background-color: #4a4a4a;
	height: 31px;
	white-space: nowrap;
	padding-left: 3px;
	align-items: bottom;
}
.popup_6 {
	background-color: #fff;
	height: 29px;
	font-weight: 200;
	min-width: 300px;
	max-width: 310px;
	white-space: wrap;
	padding: 0 0px 0 0px;	
}



.popup_baslik {

	font-size:12px;
	font-weight:bold;
	font-family: Tahoma;
	color:orange;
	align-items: bottom;
}
.popup_baslik_2 {
	font-size:10px;
	font-weight:bold;
	font-family: Tahoma;
	color: #5e5e5e;
	min-width: 32%;
	align-items: bottom;
}
.popup_baslik_2 a{
	color: #f8f4dd;
}
.popup_baslik_2 a:hover{
	color: #fff;
}


.popup_baslik_sil {

	font-size:12px;
	font-weight:bold;
	font-family: Tahoma;
	color:darkred;
	align-items: bottom;
}


.satir_1_asil {
	background-color: #e2ebed;
	height: 23px;
	font-size: 1px;
}
.satir_1_asil a{
	background-color: #e2ebed;
	height: 23px;
}
.satir_1_asil:hover {
	background-color: #a6c7d8;
	height: 23px;
	text-decoration:none;
}
.satir_1_asil a:hover {
	background-color: #a6c7d8;
	height: 23px;
	text-decoration:none;
}

.satir_2_asil {
	background-color: #cadde2;
	height: 23px;
	font-size: 1px;
}
.satir_2_asil a{
	background-color: #cadde2;
	height: 23px;
}
.satir_2_asil:hover {

	background-color: #a6c7d8;
	height: 23px;
	text-decoration:none;
}
.satir_2_asil a:hover {

	background-color: #a6c7d8;
	height: 23px;
	text-decoration:none;
}



.satir_1_sh {
	background-color: #fef9e5;
	height: 23px;
}
.satir_1_sh:hover {
	color:white;
	background-color: #926505;
	height: 23px;
	text-decoration:none;
}
.satir_2_sh {
	background-color: #f5e18a;
	height: 23px;
}
.satir_2_sh:hover {
	color:white;
	background-color: #926505;
	height: 23px;
	text-decoration:none;
}




.satir_1_ush {
	background-color: #add597;
	height: 23px;
}
.satir_1_ush:hover {
	color:white;
	background-color: #4f9b26;
	height: 23px;
	text-decoration:none;
}
.satir_2_ush {
	background-color: #f1fbe4;
	height: 23px;
}
.satir_2_ush:hover {
	color:white;
	background-color: #4f9b26;
	height: 23px;
	text-decoration:none;
}

.input_text_uname {
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -ms-border-radius: 3px;
    -o-border-radius: 3px;
    border-radius: 3px;
    -webkit-box-shadow: 0 1px 0 #FFF, 0 -2px 5px rgba(0, 0, 0, 0.08) inset;
    -moz-box-shadow: 0 1px 0 #fff, 0 -2px 5px rgba(0, 0, 0, 0.08) inset;
    -ms-box-shadow: 0 1px 0 #fff, 0 -2px 5px rgba(0, 0, 0, 0.08) inset;
    -o-box-shadow: 0 1px 0 #fff, 0 -2px 5px rgba(0, 0, 0, 0.08) inset;
    box-shadow: 0 1px 0 #FFF, 0 -2px 5px rgba(0, 0, 0, 0.08) inset;
    -webkit-transition: all 0.5s ease;
    -moz-transition: all 0.5s ease;
    -ms-transition: all 0.5s ease;
    -o-transition: all 0.5s ease;
    transition: all 0.5s ease;
    background: #fff;
    border: 2px solid #C8C8C8;
    color: #000;
    font: 12px Helvetica, Arial, sans-serif;
    margin: 0 0 0px;
    padding: 1px 1px 1px 1px;
    width: 120px;
	height: 15px;
}
.input_text_uname:focus {
    -webkit-box-shadow: 0 0 3px #0BD6E5 inset;
    -moz-box-shadow: 0 0 3px #0BD6E5 inset;
    -ms-box-shadow: 0 0 3px #0BD6E5 inset;
    -o-box-shadow: 0 0 3px #0BD6E5 inset;
    box-shadow: 0 0 3px #0BD6E5 inset;
    background-color: #FFF;
    border: 2px solid #0733B6;
    outline: none;
}
.input_text_uname[disabled] { 
    background: #ccc; 
}



.input_textarea {
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -ms-border-radius: 3px;
    -o-border-radius: 3px;
    border-radius: 3px;
    -webkit-box-shadow: 0 1px 0 #FFF, 0 -2px 5px rgba(0, 0, 0, 0.08) inset;
    -moz-box-shadow: 0 1px 0 #fff, 0 -2px 5px rgba(0, 0, 0, 0.08) inset;
    -ms-box-shadow: 0 1px 0 #fff, 0 -2px 5px rgba(0, 0, 0, 0.08) inset;
    -o-box-shadow: 0 1px 0 #fff, 0 -2px 5px rgba(0, 0, 0, 0.08) inset;
    box-shadow: 0 1px 0 #FFF, 0 -2px 5px rgba(0, 0, 0, 0.08) inset;
    -webkit-transition: all 0.5s ease;
    -moz-transition: all 0.5s ease;
    -ms-transition: all 0.5s ease;
    -o-transition: all 0.5s ease;
    transition: all 0.5s ease;
    background: #fff;
    border: 2px solid #C8C8C8;
    color: #000;
    font: 12px Helvetica, Arial, sans-serif;
    margin: 0 0 0px;
    padding: 1px 1px 1px 1px;
    width: 200px;
	height:50px;
}
.input_textarea:focus {
    -webkit-box-shadow: 0 0 3px #0BD6E5 inset;
    -moz-box-shadow: 0 0 3px #0BD6E5 inset;
    -ms-box-shadow: 0 0 3px #0BD6E5 inset;
    -o-box-shadow: 0 0 3px #0BD6E5 inset;
    box-shadow: 0 0 3px #0BD6E5 inset;
    background-color: #FFF;
    border: 2px solid #0733B6;
    outline: none;
}

.input_pass {
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
    border-radius: 5px;
    -webkit-box-shadow: 0 1px 0 #FFF, 0 -2px 5px rgba(0, 0, 0, 0.08) inset;
    -moz-box-shadow: 0 1px 0 #fff, 0 -2px 5px rgba(0, 0, 0, 0.08) inset;
    -ms-box-shadow: 0 1px 0 #fff, 0 -2px 5px rgba(0, 0, 0, 0.08) inset;
    -o-box-shadow: 0 1px 0 #fff, 0 -2px 5px rgba(0, 0, 0, 0.08) inset;
    box-shadow: 0 1px 0 #FFF, 0 -2px 5px rgba(0, 0, 0, 0.08) inset;
    -webkit-transition: all 0.5s ease;
    -moz-transition: all 0.5s ease;
    -ms-transition: all 0.5s ease;
    -o-transition: all 0.5s ease;
    transition: all 0.5s ease;
    background: #fff;
    border: 2px solid #2A2929;
    color: #000;
    font: 12px Helvetica, Arial, sans-serif;
	font-weight: bold;
    margin: 0 0 0px;
    padding: 1px 1px 1px 1px;
    width: 120px;
}
.input_pass:focus {
    -webkit-box-shadow: 0 0 3px #0BD6E5 inset;
    -moz-box-shadow: 0 0 3px #0BD6E5 inset;
    -ms-box-shadow: 0 0 3px #0BD6E5 inset;
    -o-box-shadow: 0 0 3px #0BD6E5 inset;
    box-shadow: 0 0 3px #0BD6E5 inset;
    background-color: #FDD6E5;
    border: 2px solid #0733B6;
    outline: none;
}



.input_text {
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
    border-radius: 5px;
    -webkit-box-shadow: 0 1px 0 #FFF, 0 -2px 5px rgba(0, 0, 0, 0.08) inset;
    -moz-box-shadow: 0 1px 0 #fff, 0 -2px 5px rgba(0, 0, 0, 0.08) inset;
    -ms-box-shadow: 0 1px 0 #fff, 0 -2px 5px rgba(0, 0, 0, 0.08) inset;
    -o-box-shadow: 0 1px 0 #fff, 0 -2px 5px rgba(0, 0, 0, 0.08) inset;
    box-shadow: 0 1px 0 #FFF, 0 -2px 5px rgba(0, 0, 0, 0.08) inset;
    -webkit-transition: all 0.5s ease;
    -moz-transition: all 0.5s ease;
    -ms-transition: all 0.5s ease;
    -o-transition: all 0.5s ease;
    transition: all 0.5s ease;
    background: #fff;
    border: 0px solid #2A2929;
    color: #000;
    font: 12px Helvetica, Arial, sans-serif;
    margin: 0 0 0px;
    padding: 1px 1px 1px 1px;
    width: 410px;
	height: 25px;
}
.input_text:focus {
    -webkit-box-shadow: 0 0 3px #0BD6E5 inset;
    -moz-box-shadow: 0 0 3px #0BD6E5 inset;
    -ms-box-shadow: 0 0 3px #0BD6E5 inset;
    -o-box-shadow: 0 0 3px #0BD6E5 inset;
    box-shadow: 0 0 3px #0BD6E5 inset;
    background-color: #c8eed0;
    outline: none;
	font-weight: bold;
}
.input_text:disabled {
    -webkit-box-shadow: 0 0 3px #0BD6E5 inset;
    -moz-box-shadow: 0 0 3px #0BD6E5 inset;
    -ms-box-shadow: 0 0 3px #0BD6E5 inset;
    -o-box-shadow: 0 0 3px #0BD6E5 inset;
    box-shadow: 0 0 3px #0BD6E5 inset;
    background-color: #ddd;
    outline: none;
	font-weight: bold;
}





.input_text_sil {
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
    border-radius: 5px;
    -webkit-box-shadow: 0 1px 0 #FFF, 0 -2px 5px rgba(0, 0, 0, 0.08) inset;
    -moz-box-shadow: 0 1px 0 #fff, 0 -2px 5px rgba(0, 0, 0, 0.08) inset;
    -ms-box-shadow: 0 1px 0 #fff, 0 -2px 5px rgba(0, 0, 0, 0.08) inset;
    -o-box-shadow: 0 1px 0 #fff, 0 -2px 5px rgba(0, 0, 0, 0.08) inset;
    box-shadow: 0 1px 0 #FFF, 0 -2px 5px rgba(0, 0, 0, 0.08) inset;
    -webkit-transition: all 0.5s ease;
    -moz-transition: all 0.5s ease;
    -ms-transition: all 0.5s ease;
    -o-transition: all 0.5s ease;
    transition: all 0.5s ease;
    background: #fff;
    border: 0px solid #2A2929;
    color: #000;
    font: 12px Helvetica, Arial, sans-serif;
    margin: 0 0 0px;
    padding: 1px 1px 1px 1px;
    width: 200px;
	height: 25px;
}
.input_text_sil:focus {
    -webkit-box-shadow: 0 0 3px #0BD6E5 inset;
    -moz-box-shadow: 0 0 3px #0BD6E5 inset;
    -ms-box-shadow: 0 0 3px #0BD6E5 inset;
    -o-box-shadow: 0 0 3px #0BD6E5 inset;
    box-shadow: 0 0 3px #0BD6E5 inset;
    background-color: #c8eed0;
    outline: none;
	font-weight: bold;
}
.input_text_sil:disabled {
    -webkit-box-shadow: 0 0 3px #0BD6E5 inset;
    -moz-box-shadow: 0 0 3px #0BD6E5 inset;
    -ms-box-shadow: 0 0 3px #0BD6E5 inset;
    -o-box-shadow: 0 0 3px #0BD6E5 inset;
    box-shadow: 0 0 3px #0BD6E5 inset;
    background-color: #ddd;
    outline: none;
	font-weight: bold;
}



.input_text_profil {
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
    border-radius: 5px;
    -webkit-box-shadow: 0 1px 0 #FFF, 0 -2px 5px rgba(0, 0, 0, 0.08) inset;
    -moz-box-shadow: 0 1px 0 #fff, 0 -2px 5px rgba(0, 0, 0, 0.08) inset;
    -ms-box-shadow: 0 1px 0 #fff, 0 -2px 5px rgba(0, 0, 0, 0.08) inset;
    -o-box-shadow: 0 1px 0 #fff, 0 -2px 5px rgba(0, 0, 0, 0.08) inset;
    box-shadow: 0 1px 0 #FFF, 0 -2px 5px rgba(0, 0, 0, 0.08) inset;
    -webkit-transition: all 0.5s ease;
    -moz-transition: all 0.5s ease;
    -ms-transition: all 0.5s ease;
    -o-transition: all 0.5s ease;
    transition: all 0.5s ease;
    background: #fff;
    border: 0px solid #2A2929;
    color: #000;
    font: 12px Helvetica, Arial, sans-serif;
    margin: 0 0 0 0px;
    padding: 0px 0px 0px 3px;
    width: 300px;
	height: 25px;
}
.input_text_profil:focus {
    -webkit-box-shadow: 0 0 3px #0BD6E5 inset;
    -moz-box-shadow: 0 0 3px #0BD6E5 inset;
    -ms-box-shadow: 0 0 3px #0BD6E5 inset;
    -o-box-shadow: 0 0 3px #0BD6E5 inset;
    box-shadow: 0 0 3px #0BD6E5 inset;
    background-color: #c8eed0;
    outline: none;
	font-weight: bold;
}
.input_text_profil:disabled {
    -webkit-box-shadow: 0 0 3px #0BD6E5 inset;
    -moz-box-shadow: 0 0 3px #0BD6E5 inset;
    -ms-box-shadow: 0 0 3px #0BD6E5 inset;
    -o-box-shadow: 0 0 3px #0BD6E5 inset;
    box-shadow: 0 0 3px #0BD6E5 inset;
    background-color: #ddd;
    outline: none;
	font-weight: bold;
}





td.tooltips_td {

  position: relative;
  white-space: nowrap;
  background: rgba(0,0,0,0.4);
}

td.tooltips_td a {
  font-size:14px;
  margin:0;
  padding:0;
  color: orange;
  text-align: center;
  text-decoration: none;
}
td.tooltips_td a:hover {
  color: white;
}

td.tooltips_td span {
  font-size:14px;
  margin:0;
  padding:0;
  position: absolute;
  color: orange;
  background: #000000;
  border: 2px solid #6D6D6D;
  height: 54px;
  line-height: 54px;
  text-align: center;
  visibility: hidden;
  border-radius: 6px;
  box-shadow: 0px 0px 8px #FFFFFF;
}
td.tooltips_td span:before {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  margin-left: -12px;
  margin-top: 2px;
  width: 0; height: 0;
  border-bottom: 12px solid #6D6D6D;
  border-right: 12px solid transparent;
  border-left: 12px solid transparent;
}
td.tooltips_td span:after {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  margin-left: -8px;
  width: 0; height: 0;
  border-bottom: 8px solid rgba(0,0,0,0.8);
  border-right: 8px solid transparent;
  border-left: 8px solid transparent;
}
td:hover.tooltips_td span {
  margin:0;
  padding:0;
  visibility: visible;
  background: rgba(0,0,0,0.5);
  top: 30px;
  left: 50%;
  z-index: 999;
}




a.tooltips {

  position: relative;

}
a.tooltips span {
  font-size:14px;
  margin:0;
  padding:0;
  position: absolute;
  color: orange;
  background: #000000;
  border: 2px solid #6D6D6D;
  height: 54px;
  line-height: 54px;
  text-align: center;
  visibility: hidden;
  border-radius: 6px;
  box-shadow: 0px 0px 8px #FFFFFF;
}
a.tooltips span:before {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  margin-left: -12px;
  margin-top: 2px;
  width: 0; height: 0;
  border-bottom: 12px solid #6D6D6D;
  border-right: 12px solid transparent;
  border-left: 12px solid transparent;
}
a.tooltips span:after {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  margin-left: -8px;
  width: 0; height: 0;
  border-bottom: 8px solid rgba(0,0,0,0.8);
  border-right: 8px solid transparent;
  border-left: 8px solid transparent;
}
a:hover.tooltips span {
  margin:0;
  padding:0;
  visibility: visible;
  background: rgba(0,0,0,0.5);
  top: 30px;
  left: 50%;
  z-index: 999;
}


