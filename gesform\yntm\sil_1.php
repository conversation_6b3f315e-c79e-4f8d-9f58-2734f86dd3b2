

<?php

session_start();
if ($_SESSION['user_level'] == '1')
{
include('fonksiyon.php');

$sql = mysqli_query($con, "SELECT * FROM ".TABLO." WHERE id='".$_GET['id']."'");
$row = mysqli_fetch_array($sql);
?>
<center>
<form name="yeni" action="javascript:void(0);" method="post">
<b>
<TABLE class="table_radius_border" border="0" style="margin:5px;font-size:12px;width:270px;">
<tr class="popup_2">
	<td align="center" class="popup_baslik_sil">
		BU İÇERİĞİ SİLMEK İSTEDİĞİNİZE EMİN MİSİNİZ?
	</td>
</tr>
<tr>
	<td>
		<table border="0" style="margin:5px;font-size:12px;width:250px;">
			<tr class="popup_1">
				<td align="left">ID :</td>
				<td align="left">Proje Adı :</td>
				<td align="left">Sahip Adı :</td>
			</tr>
			<tr class="popup_1">
				<td align="left"><input value="<?=$row['id']?>" class="input_text_sil" type="text" readonly></td>
				<td align="left"><input value="<?=$row['proje_info_proje_adi']?>" class="input_text_sil" type="text" readonly></td>
				<td align="left"><input value="<?=$row['sahip_adi']?>" class="input_text_sil" type="text" readonly></td>
			</tr>
		</table>
	</td>
</tr>
<tr>
	<td align="right">
		<input type="hidden" name="id" value="<?=$row['id']?>">
		<input class="input_btn" type="submit" value="Sil" onclick="sil_1();">&nbsp;
	</td>
</tr>
</TABLE>
</b>
<div id="siliniyor"></div>
</form>
</center>
<?php
}else{
	include('login.php');
}
?>