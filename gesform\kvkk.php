<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/bootstrap-select.min.css">
    <link rel="stylesheet" href="css/main.css">
    <link rel="preconnect" href="https://fonts.gstatic.com">
    <link href="https://fonts.googleapis.com/css2?family=Hind:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/pygment_trac.css">

    <link rel="stylesheet" type="text/css" href="css/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="css/animate.css" />
    <link rel="stylesheet" type="text/css" href="css/bot.css" />
    <link href="css/media.css" rel="stylesheet" />

    <link href="css/bootstrap-datepicker3.min.css" rel="stylesheet" />


    <link rel="icon" href="img/favicon.ico" />
    <script src="css/jquery-3.6.0.min.js"></script>
    <script src="css/jquery.inputmask.js"></script>
    <script src="css/custom.js"></script>
    <script src="css/jquery.validate.min.js"></script>
    <script src="css/jquery.validate.unobtrusive.js"></script>
    <script src="css/jquery.unobtrusive-ajax.min.js"></script>
    <script src="css/fuse.js"></script>
    <script src="https://www.google.com/recaptcha/api.js?hl=tr"></script>
    <script src="css/bootstrap-datepicker.min.js"></script>
    <script src="css/bootstrap-datepicker.tr.min.js"></script>



    <title>
Ba&#x15F;kent</title>
</head>
<body>

    <header class="header">
        <div class="container" style="position:relative">
            <div class="header-top">
                <div class="float-right">
                    <ul>
                        <li><a href="/sss/elektrik-kesintisi">S&#x131;k&#xE7;a Sorulan Sorular</a></li>
                        <li><a href="https://online.baskentedas.com.tr/basvuru-takip">Ba&#x15F;vuru Takibi</a></li>
                        <li><a href="/yasal-bildirim/ticari-kalite">Elektrik Kalite G&#xF6;stergeleri</a></li>
                    </ul>
                    <ul class="lang">
                            <li>
                                <a class="active" href="/SiteBase/DilDegisim?culture=tr&amp;returnUrl=%2Fsayfa%2Fkisisel-verilerin-islenmesine-iliskin-aydinlatma-metni">
                                    TR
                                </a>
                            </li>
                            <li>
                                <a class="" href="/SiteBase/DilDegisim?culture=en&amp;returnUrl=%2Fsayfa%2Fkisisel-verilerin-islenmesine-iliskin-aydinlatma-metni">
                                    EN
                                </a>
                            </li>
                    </ul>
                </div>
            </div>

            <nav class="navbar navbar-expand-lg">
                <a class="navbar-brand" href="/">
                    
    <img src="img/baskent-logo-black.svg" class="img-fluid" alt="logo">

                </a>
                <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent"
                        aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse menu" id="navbarSupportedContent">
                    

    <ul class="navbar-nav mr-auto">
                    <li class="nav-item">
                        <a class="nav-link" target="_self">
                            Ba&#x15F;kent EDA&#x15E; Hakk&#x131;nda
                            <div class="menu-border"></div>
                        </a>
                        <div class="sub_menu-box">
                            <ul>
                                    <li>
                                        <a href="/sayfa/hakkimizda" target="_self">
                                            Hakk&#x131;m&#x131;zda
                                            <div class="sub_menu-border"></div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="/sayfa/tarihce" target="_self">
                                            Tarih&#xE7;e
                                            <div class="sub_menu-border"></div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="/sayfa/misyon-ve-degerler" target="_self">
                                            Misyon ve De&#x11F;erler
                                            <div class="sub_menu-border"></div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="/sayfa/kurumsal-bilgiler" target="_self">
                                            Kurumsal Bilgiler
                                            <div class="sub_menu-border"></div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="/sayfa/politikalarimiz" target="_self">
                                            Politikalar&#x131;m&#x131;z
                                            <div class="sub_menu-border"></div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="/sayfa/kariyer" target="_self">
                                            Kariyer
                                            <div class="sub_menu-border"></div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="/sayfa/politikalarimiz" target="_self">
                                            Sosyal Sorumluluk
                                            <div class="sub_menu-border"></div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="/sayfa/medya" target="_self">
                                            Medya
                                            <div class="sub_menu-border"></div>
                                        </a>
                                    </li>
                            </ul>
                        </div>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" target="_self">
                            Hizmetler
                            <div class="menu-border"></div>
                        </a>
                        <div class="sub_menu-box">
                            <ul>
                                    <li>
                                        <a href="/sayfa/sebeke-operasyonlari" target="_self">
                                            Da&#x11F;&#x131;t&#x131;m Operasyonlar&#x131;
                                            <div class="sub_menu-border"></div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="/sayfa/dagitim-sistemleri-ve-yatirimlar" target="_self">
                                            Da&#x11F;&#x131;t&#x131;m Sistemleri ve Yat&#x131;r&#x131;mlar
                                            <div class="sub_menu-border"></div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="/sayfa/akilli-sebekeler-ve-ar-ge-projeleri" target="_self">
                                            Ak&#x131;ll&#x131; &#x15E;ebeke ve Ar-Ge
                                            <div class="sub_menu-border"></div>
                                        </a>
                                    </li>
                            </ul>
                        </div>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" target="_self">
                            Bilgilendirme
                            <div class="menu-border"></div>
                        </a>
                        <div class="sub_menu-box">
                            <ul>
                                    <li>
                                        <a href="/yasal-bildirim" target="_self">
                                            Yasal Bildirimler
                                            <div class="sub_menu-border"></div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="/yayindaki-ihaleler" target="_self">
                                            &#x130;haleler
                                            <div class="sub_menu-border"></div>
                                        </a>
                                    </li>
                            </ul>
                        </div>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" target="_self">
                            KVKK
                            <div class="menu-border"></div>
                        </a>
                        <div class="sub_menu-box">
                            <ul>
                                    <li>
                                        <a href="/sayfa/kisisel-verilerin-islenmesine-iliskin-aydinlatma-metni" target="_self">
                                            Ayd&#x131;nlatma Metni
                                            <div class="sub_menu-border"></div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="/site-dosya/dosya-on-izleme/8818" target="_blank">
                                            KVKK Politikas&#x131;
                                            <div class="sub_menu-border"></div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="/sayfa/memnuniyet-anketi-aydinlatma-metni" target="_self">
                                            Memnuniyet Anketi Ayd&#x131;nlatma Metni
                                            <div class="sub_menu-border"></div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="/site-dosya/dosya-on-izleme/9601" target="_blank">
                                            &#x130;lgili Ki&#x15F;i Ba&#x15F;vuru Formu
                                            <div class="sub_menu-border"></div>
                                        </a>
                                    </li>
                            </ul>
                        </div>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/iletisim" target="_self">
                            &#x130;leti&#x15F;im
                            <div class="menu-border"></div>
                        </a>
                    </li>
    </ul>

                    <a href="https://online.baskentedas.com.tr"
                       class="button--default button--default-padding">Online &#x130;&#x15F;lemler</a>
                </div>
            </nav>
            <div class="search-input search-input-focus search-input-radius search-input--autocomplete">
                <input type="text" id="searchLayoutId" class="searchInputinnerLayout" placeholder="&#x130;&#x15F;lem, i&#xE7;erik, proje ara" />
                <div class="button-box">
                    <div class="clear">Temizle</div>
                    <div class="close"><img src="img/close.svg" alt="kapat" /></div>
                </div>
                <div class="dropdown-menu">
                    <div class="row oneri-text">
                        <div class="col-lg-12 mb-15 mt-20">
                            <p class="pl-20" style="font-size:16px;line-height: 22px;">
                                <img class="pr-10" src="img/icons-general-info.svg" alt="genel bilgi simgesi" />
                                &#xD6;nerileri g&#xF6;rmek i&#xE7;in en az 3 karakter yazmal&#x131;s&#x131;n&#x131;z.
                            </p>
                        </div>
                    </div>

                    <div class="s-body" style="display:none;">
                        <div class="row">
                            <div class="scrollable">
                                <div class="col-lg-12 mb-15 mt-20">
                                    <div class="input-title pl-30">Online &#x130;&#x15F;lemler</div>
                                    <a class="detay-link detay-link--negative" id="islemSayisiContainerUstinner" style="margin-top:-4px;" href="#"><span id="islemSayisiUstInner">0</span> Sonucu G&#xF6;r</a>
                                    <ul class="mt-0 pl-20 float-left pr-20 w-100" id="islemIcerikUstInner">
                                    </ul>
                                </div>
                                <div class="col-lg-12 mb-15 mt-20">
                                    <div class="input-title pl-30">S&#x131;k&#xE7;a Sorulan Sorular</div>
                                    <a class="detay-link detay-link--negative" id="sssSayisiContainerUstinner" style="margin-top:-4px;" href="#"><span id="sssSayisiUstInner">0</span> Sonucu G&#xF6;r</a>
                                    <ul class="mt-0 pl-20 float-left pr-20 w-100" id="sssIcerikUstInner">
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <ol>
                                <li>Sonu&#xE7; Bulunan Di&#x11F;er Kategoriler:</li>
                                <li><a id="yasalSayisiContainerUstinner"> Yasal Bildirimler (<span id="yasalSayisiUstInner">0</span>)</a></li>
                                <li><a id="medyaSayisiContainerUstinner"> Medya (<span id="medyaSayisiUstInner">0</span>)</a></li>
                                <li><a id="sayfaSayisiContainerUstinner"> Sayfalar (<span id="sayfaSayisiUstInner">0</span>)</a></li>
                            </ol>
                        </div>
                    </div>


                </div>
            </div>
        </div>
    </header>


    <header class="header-mobile">
        <div class="dropdown">
            <button class="btn-menu-open" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
            </button>
            <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                <div class="menu-header">
                    <button class="btn-menu-close" type="button" onclick="$('.btn-menu-close').closest('.dropdown').dropdown('toggle');"></button>
                    <a href="https://online.baskentedas.com.tr" class="button--default">Online &#x130;&#x15F;lemler</a>
                </div>
                <div class="menu-top">
                    

    <ul class="menu">
        <li>
            <div>
                <a href="/">
                    Ana Sayfa
                    <div class="sub_menu-border"></div>
                </a>
            </div>
        </li>
                <li class="sub_menu-icon">
                    <div class="menu_href">
                        <a target="_self">
                            Ba&#x15F;kent EDA&#x15E; Hakk&#x131;nda
                            <div class="sub_menu-border"></div>
                        </a>
                    </div>
                    <div class="sub_menu-box">
                        <ul class="mt-0">
                                <li>
                                    <a href="/sayfa/hakkimizda" target="_self">
                                        Hakk&#x131;m&#x131;zda
                                    </a>
                                </li>
                                <li>
                                    <a href="/sayfa/tarihce" target="_self">
                                        Tarih&#xE7;e
                                    </a>
                                </li>
                                <li>
                                    <a href="/sayfa/misyon-ve-degerler" target="_self">
                                        Misyon ve De&#x11F;erler
                                    </a>
                                </li>
                                <li>
                                    <a href="/sayfa/kurumsal-bilgiler" target="_self">
                                        Kurumsal Bilgiler
                                    </a>
                                </li>
                                <li>
                                    <a href="/sayfa/politikalarimiz" target="_self">
                                        Politikalar&#x131;m&#x131;z
                                    </a>
                                </li>
                                <li>
                                    <a href="/sayfa/kariyer" target="_self">
                                        Kariyer
                                    </a>
                                </li>
                                <li>
                                    <a href="/sayfa/politikalarimiz" target="_self">
                                        Sosyal Sorumluluk
                                    </a>
                                </li>
                                <li>
                                    <a href="/sayfa/medya" target="_self">
                                        Medya
                                    </a>
                                </li>
                        </ul>
                    </div>
                </li>
                <li class="sub_menu-icon">
                    <div class="menu_href">
                        <a target="_self">
                            Hizmetler
                            <div class="sub_menu-border"></div>
                        </a>
                    </div>
                    <div class="sub_menu-box">
                        <ul class="mt-0">
                                <li>
                                    <a href="/sayfa/sebeke-operasyonlari" target="_self">
                                        Da&#x11F;&#x131;t&#x131;m Operasyonlar&#x131;
                                    </a>
                                </li>
                                <li>
                                    <a href="/sayfa/dagitim-sistemleri-ve-yatirimlar" target="_self">
                                        Da&#x11F;&#x131;t&#x131;m Sistemleri ve Yat&#x131;r&#x131;mlar
                                    </a>
                                </li>
                                <li>
                                    <a href="/sayfa/akilli-sebekeler-ve-ar-ge-projeleri" target="_self">
                                        Ak&#x131;ll&#x131; &#x15E;ebeke ve Ar-Ge
                                    </a>
                                </li>
                        </ul>
                    </div>
                </li>
                <li class="sub_menu-icon">
                    <div class="menu_href">
                        <a target="_self">
                            Bilgilendirme
                            <div class="sub_menu-border"></div>
                        </a>
                    </div>
                    <div class="sub_menu-box">
                        <ul class="mt-0">
                                <li>
                                    <a href="/yasal-bildirim" target="_self">
                                        Yasal Bildirimler
                                    </a>
                                </li>
                                <li>
                                    <a href="/yayindaki-ihaleler" target="_self">
                                        &#x130;haleler
                                    </a>
                                </li>
                        </ul>
                    </div>
                </li>
                <li class="sub_menu-icon">
                    <div class="menu_href">
                        <a target="_self">
                            KVKK
                            <div class="sub_menu-border"></div>
                        </a>
                    </div>
                    <div class="sub_menu-box">
                        <ul class="mt-0">
                                <li>
                                    <a href="/sayfa/kisisel-verilerin-islenmesine-iliskin-aydinlatma-metni" target="_self">
                                        Ayd&#x131;nlatma Metni
                                    </a>
                                </li>
                                <li>
                                    <a href="/site-dosya/dosya-on-izleme/8818" target="_blank">
                                        KVKK Politikas&#x131;
                                    </a>
                                </li>
                                <li>
                                    <a href="/sayfa/memnuniyet-anketi-aydinlatma-metni" target="_self">
                                        Memnuniyet Anketi Ayd&#x131;nlatma Metni
                                    </a>
                                </li>
                                <li>
                                    <a href="/site-dosya/dosya-on-izleme/9601" target="_blank">
                                        &#x130;lgili Ki&#x15F;i Ba&#x15F;vuru Formu
                                    </a>
                                </li>
                        </ul>
                    </div>
                </li>
                <li>
                    <div class="menu_href">
                        <a href="/iletisim" target="_self">
                            &#x130;leti&#x15F;im
                            <div class="sub_menu-border"></div>
                        </a>
                        </div>
                </li>
    </ul>

                </div>

                <div class="menu-bottom">
                    <ul>
                        <li>
                            <a href="/sss/elektrik-kesintisi">
                                S&#x131;k&#xE7;a Sorulan Sorular
                                <div class="sub_menu-border"></div>
                            </a>
                        </li>
                        <li>
                            <a href="https://online.baskentedas.com.tr/basvuru-takip">
                                Ba&#x15F;vuru Takibi
                                <div class="sub_menu-border"></div>
                            </a>
                        </li>
                        <li>
                            <a href="/yasal-bildirim/ticari-kalite">
                                Elektrik Kalite G&#xF6;stergeleri
                                <div class="sub_menu-border"></div>
                            </a>
                        </li>
                    </ul>
                    <ul class="lang">
                            <li>
                                <a class="active" href="/SiteBase/DilDegisim?culture=tr&amp;returnUrl=%2Fsayfa%2Fkisisel-verilerin-islenmesine-iliskin-aydinlatma-metni">
                                    T&#xFC;rk&#xE7;e
                                </a>
                            </li>
                            <li>
                                <a class="" href="/SiteBase/DilDegisim?culture=en&amp;returnUrl=%2Fsayfa%2Fkisisel-verilerin-islenmesine-iliskin-aydinlatma-metni">
                                    English
                                </a>
                            </li>
                    </ul>
                </div>
            </div>
        </div>

        
    <a class="logo" href="/">
        <img src="img/baskent-logo-black.svg" alt="logo">
    </a>


        <div class="float-right">
            <a href="https://online.baskentedas.com.tr"
               class="button--default button-online-islemler-xs">Online &#x130;&#x15F;lemler</a>
        </div>
        <div class="search-input search-input-focus search-input-radius search-input--autocomplete">
            <div class="bg_search-input"></div>
            <input type="text" id="searchLayoutIdMobile" class="searchInputinnerLayout" placeholder="&#x130;&#x15F;lem, i&#xE7;erik, proje ara" />
            <div class="button-box">
                <div class="clear">Temizle</div>
                <div class="close"><img src="img/close.svg" alt="kapat" /></div>
            </div>
            <div class="dropdown-menu">
                <div class="row oneri-text">
                    <div class="col-lg-12 mb-15 mt-20">
                        <p class="pl-20" style="font-size:16px;line-height: 22px;">
                            <img class="pr-10" src="img/icons-general-info.svg" alt="genel bilgi simgesi" />
                            &#xD6;nerileri g&#xF6;rmek i&#xE7;in en az 3 karakter yazmal&#x131;s&#x131;n&#x131;z.
                        </p>
                    </div>
                </div>

                <div class="s-body" style="display:none;">
                    <div class="row">
                        <div class="scrollable">
                            <div class="col-lg-12 mb-15 mt-20">
                                <div class="input-title pl-30">Online &#x130;&#x15F;lemler</div>
                                <a class="detay-link detay-link--negative" id="islemSayisiContainerUstinnerMobile" style="margin-top:-4px;" href="#"><span id="islemSayisiUstInnerMobile">0</span> Sonucu G&#xF6;r</a>
                                <ul class="mt-0 pl-20 float-left pr-20 w-100" id="islemIcerikUstInnerMobile">
                                </ul>
                            </div>
                            <div class="col-lg-12 mb-15 mt-20">
                                <div class="input-title pl-30">S&#x131;k&#xE7;a Sorulan Sorular</div>
                                <a class="detay-link detay-link--negative" id="sssSayisiContainerUstinnerMobile" style="margin-top:-4px;" href="#"><span id="sssSayisiUstInnerMobile">0</span> Sonucu G&#xF6;r</a>
                                <ul class="mt-0 pl-20 float-left pr-20 w-100" id="sssIcerikUstInnerMobile">
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <ol>
                            <li>Sonu&#xE7; Bulunan Di&#x11F;er Kategoriler:</li>
                            <li><a id="yasalSayisiContainerUstinnerMobile"> Yasal Bildirimler (<span id="yasalSayisiUstInnerMobile">0</span>)</a></li>
                            <li><a id="medyaSayisiContainerUstinnerMobile"> Medya (<span id="medyaSayisiUstInnerMobile">0</span>)</a></li>
                            <li><a id="sayfaSayisiContainerUstinnerMobile"> Sayfalar (<span id="sayfaSayisiUstInnerMobile">0</span>)</a></li>
                        </ol>
                    </div>
                </div>


            </div>
        </div>
    </header>

    


<div class="container">




    <nav aria-label="breadcrumb">
        <ol class="breadcrumb mobile-hidden-600">
                <li class="breadcrumb-item"><a href="/">Ana Sayfa</a></li>
            <li class="breadcrumb-item active" aria-current="page">K&#x130;&#x15E;&#x130;SEL VER&#x130;LER&#x130;N &#x130;&#x15E;LENMES&#x130;NE &#x130;L&#x130;&#x15E;K&#x130;N AYDINLATMA METN&#x130;</li>
        </ol>
        <ol class="breadcrumb mobile-show-600">
            <li class="breadcrumb-item"></li>
            <li class="breadcrumb-item active" aria-current="page"><a href="/">Ana Sayfa</a></li>
        </ol>
    </nav>

    <h1 class="title mb-20"></h1>

        <div class="container-detail-block w-100 pl-0">
            <h2 class="sub-title mb-20">K&#x130;&#x15E;&#x130;SEL VER&#x130;LER&#x130;N &#x130;&#x15E;LENMES&#x130;NE &#x130;L&#x130;&#x15E;K&#x130;N AYDINLATMA METN&#x130;</h2>
            <div class="container-box">
<p>Veri sorumlusu sıfatıyla Başkent Elektrik Dağıtım A.Ş. olarak, işlenen kişisel verileriniz hakkında 6698 sayılı Kişisel Verilerin Korunması Kanunu&rsquo;nun 10&rsquo;uncu maddesi ile Aydınlatma Y&uuml;k&uuml;ml&uuml;l&uuml;ğ&uuml;n&uuml;n Yerine Getirilmesinde Uyulacak Usul ve Esaslar Hakkında Tebliğ uyarınca hazırladığımız işbu aydınlatma metni ile sizleri bilgilendirmek isteriz</p>

<h3 class="container-box--title">1. VERİ SORUMLUSUNUN KİMLİĞİ</h3>

<div class="mb-30">
<table class="table-default break-word">
	<tbody>
		<tr>
			<td>Veri Sorumlusu</td>
			<td>Başkent Elektrik Dağıtım A.Ş.</td>
		</tr>
		<tr>
			<td>Tebligat Adresi</td>
			<td>Kızılırmak Mahallesi Ufuk &Uuml;niversitesi Cad. No:1 Başkent Kule &Ccedil;ukurambar &Ccedil;ankaya/ANKARA</td>
		</tr>
		<tr>
			<td>KEP adresi</td>
			<td><a href="mailto:<EMAIL>"><EMAIL></a></td>
		</tr>
		<tr>
			<td>Telefon Numarası</td>
			<td>(0312) 573 50 00</td>
		</tr>
	</tbody>
</table>

<table class="table-default--mobile th-width-28 break-word">
	<tbody>
		<tr>
			<th>Veri Sorumlusu</th>
			<td><strong>Başkent Elektrik Dağıtım A.Ş.</strong></td>
		</tr>
		<tr>
			<th>Tebligat Adresi</th>
			<td>Kızılırmak Mahallesi Ufuk &Uuml;niversitesi Cad. No:1 Başkent Kule &Ccedil;ukurambar &Ccedil;ankaya/ANKARA</td>
		</tr>
		<tr>
			<th>KEP Adresi</th>
			<td><a href="mailto:<EMAIL>"><EMAIL></a></td>
		</tr>
		<tr>
			<th>Telefon Numarası</th>
			<td>(0312) 573 50 00</td>
		</tr>
	</tbody>
</table>
</div>

<p></p>

<h3 class="container-box--title">2. İŞLENEN KİŞİSEL VERİLER</h3>

<div class="float-left mb-30">
<table class="table-default break-word" style="display:table;">
	<thead>
		<tr>
			<th scope="col">
			<div class="t-border">KİŞİSEL VERİ KATEGORİSİ</div>
			</th>
			<th scope="col">A&Ccedil;IKLAMA</th>
		</tr>
	</thead>
	<tbody>
		<tr>
			<td><strong>Kimlik</strong></td>
			<td>Ad-soyad, uyruk, doğum tarihi, doğum yeri, medeni hali, cinsiyet, n&uuml;fus c&uuml;zdanı &ouml;n y&uuml;z&uuml;nde bulunan bilgiler, TC kimlik no ve seri numarası vergi numarası, ehliyet bilgileri, pasaport bilgileri, SGK numarası, EMO sicil no, imza, fotoğraf, akademik unvan</td>
		</tr>
		<tr>
			<td><strong>İletişim</strong></td>
			<td>E-posta adresi, iletişim adresi, kayıtlı elektronik posta adresi (KEP), telefon no, faks no</td>
		</tr>
		<tr>
			<td><strong>Lokasyon</strong></td>
			<td>Bulunduğu yerin konum bilgileri, tesisat koordinat bilgisi</td>
		</tr>
		<tr>
			<td><strong>&Ouml;zl&uuml;k</strong></td>
			<td>Bordro bilgileri, &ouml;zge&ccedil;miş bilgileri, performans değerlendirme raporları, disiplin kararları, SGK kayıt ve bilgileri, &ccedil;alışma belgesi, insan kaynaklarının planlanması ve y&ouml;netilmesine esas veriler, &ccedil;alışma ilişkileri s&uuml;re&ccedil;lerinin y&ouml;netilmesine esas veriler</td>
		</tr>
		<tr>
			<td><strong>Hukuki İşlem Bilgileri</strong></td>
			<td>Adli makamlarla yazışma bilgileri, dava dosyasındaki bilgiler, icra takip bilgileri</td>
		</tr>
		<tr>
			<td><strong>M&uuml;şteri İşlem</strong></td>
			<td>Elektrik dağıtım bağlantı ve sistem kullanım anlaşmasına ilişkin veriler, &ccedil;ağrı merkezi kayıtları, talep bilgisi, sipariş bilgisi, tanıtıcı numarası muhatap numarası, s&ouml;zleşme hesap numarası, tesisat numarası, muhatap no, muhatap t&uuml;r&uuml;, ka&ccedil;ak tutanak kodu, dağıtım bor&ccedil; bilgisi, s&ouml;zleşme hesabı, sayım noktası tanımı (ETSO), saya&ccedil; seri no, tesisat no, kWh (t&uuml;ketim bilgisi), endeks bilgileri, tarife tipi, tapu kaydı / bilgisi, yapı kullanım izin belgesi, yapı ruhsatı, tapu yer tahsis bilgisi, onaylı proje bilgisi, imar durumu bilgisi, elektrik i&ccedil; tesisat projesi, uzlaştırmaya esas veriş-&ccedil;ekiş miktarı, aplikasyon krokisi, &ouml;nceki başvuruların kaydı, m&uuml;şteri talep / şik&acirc;yet bilgisi, ka&ccedil;ak bor&ccedil; bilgisi, fatura, senet, &ccedil;ek bilgileri, gişe dekontlarındaki bilgiler, vek&acirc;letname, yetki s&ouml;zleşmesi</td>
		</tr>
		<tr>
			<td><strong>Fiziksel Mekan G&uuml;venliği</strong></td>
			<td>Giriş &ndash; &ccedil;ıkış kayıtları, kamera kayıtları</td>
		</tr>
		<tr>
			<td><strong>İşlem G&uuml;venliği</strong></td>
			<td>Kullanıcı adı, şifre ve parola bilgileri, denetim izleri, IP adresi bilgileri, internet sitesi erişim logları ile logları i&ccedil;eren siber g&uuml;venliğe ilişkin veriler, elektronik imza</td>
		</tr>
		<tr>
			<td><strong>Finans</strong></td>
			<td>IBAN, banka hesap numarası bilgileri, ticaret sicil gazetesi suretleri, imza sirk&uuml;leri, vergi levhası, ticaret ve sanayi odası kaydı, meslek odası kaydı</td>
		</tr>
		<tr>
			<td><strong>Mesleki Deneyim</strong></td>
			<td>&nbsp;Diploma bilgileri, gidilen kurs ve seminerler, meslek i&ccedil;i eğitim bilgileri, sertifikalar, yabancı dil bilgisi, iş tecr&uuml;beleri serbest m&uuml;şavirlik m&uuml;hendislik (SMM) belgesi, mesleki yeterlilik belgesi</td>
		</tr>
		<tr>
			<td><strong>Pazarlama</strong></td>
			<td>&nbsp;Anket ve &ccedil;erez kayıtları</td>
		</tr>
		<tr>
			<td><strong>G&ouml;rsel ve İşitsel Kayıtlar</strong></td>
			<td>&nbsp;Kamera g&ouml;r&uuml;nt&uuml;s&uuml;, fotoğraf, ses kaydı</td>
		</tr>
		<tr>
			<td><b>Felsefi İnan&ccedil;, Din, Mehzep ve Diğer İnan&ccedil;lar</b></td>
			<td>&nbsp;T.C. vatandaşları i&ccedil;in din hanesi yer alan eski n&uuml;fus c&uuml;zdanı suretinin bir hukuki ilişki &ccedil;er&ccedil;evesinde kullanıldığı s&uuml;re&ccedil;ler</td>
		</tr>
		<tr>
			<td><strong>Dernek &Uuml;yeliği</strong></td>
			<td>&nbsp;Dernek &uuml;yeliği</td>
		</tr>
		<tr>
			<td><strong>Sendika &Uuml;yeliği</strong></td>
			<td>&nbsp;Sendika &Uuml;yeliği</td>
		</tr>
		<tr>
			<td><strong>Sağlık Bilgileri</strong></td>
			<td>Elektrikle &ccedil;alışan diyaliz destek &uuml;nitesi, solunum cihazı ve benzeri mahiyette yaşam destek cihazlarına bağımlı olan t&uuml;keticilere ait destekleyici belgeler ile bu kapsamda ge&ccedil;erli sağlık raporu bilgileri, işe giriş muayene formları, sağlık raporu, engellilik durumuna ait bilgiler, kişisel sağlık bilgileri, kullanılan cihaz ve protezler, kan grubu bilgisi</td>
		</tr>
		<tr>
			<td><strong>Ceza Mahkumiyeti ve G&uuml;venlik Tedbirleri</strong></td>
			<td>&nbsp;Adli sicil ve arşiv araştırma kayıtları</td>
		</tr>
		<tr>
			<td><strong>Diğer</strong></td>
			<td>&nbsp;Enerji ve Tabi Kaynaklar Bakanlığı, Enerji Piyasası D&uuml;zenleme Kurumu ve T&uuml;rkiye Elektrik Dağıtım A.Ş kamu t&uuml;zelkişiliklerinin denetimine esas veriler ve bağımsız 3. kişi şirketler tarafından yapılan denetimlere esas veriler, unvan, ara&ccedil; bilgisi, kurumsal hafıza bilgisi, referans bilgisi, sigorta bilgisi (sağlık, hayat, bireysel emeklilik sigortası verileri), kurumsal iletişim bilgisi, beden &ouml;l&ccedil;&uuml;s&uuml;, ayakkabı numarası, &ccedil;alışan adayı bilgisi</td>
		</tr>
	</tbody>
</table>
</div>

<p></p>

<h3 class="container-box--title">3. KİŞİSEL VERİLERİNİZİN İŞLENME AMA&Ccedil;LARI</h3>

<p>Kişisel verilerinizi aşağıda belirtilen ama&ccedil;larla işlemekteyiz:</p>

<p></p>

<ul class="list-default">
	<li>Elektrik Piyasası Kanunu ve ikincil mevzuatı başta olmak &uuml;zere şirketimizin tabi olduğu mevzuattan doğan y&uuml;k&uuml;ml&uuml;l&uuml;kleri yerine getirerek faaliyetleri mevzuata uygun y&uuml;r&uuml;tmek,</li>
	<li>Elektrik dağıtım lisansımızda belirlenen b&ouml;lgede saya&ccedil;ları okumak,</li>
	<li>Saya&ccedil;ların bakımı ve işletilmesi hizmetleri yerine getirmek,</li>
	<li>Elektrik dağıtım lisansımızda belirtilen b&ouml;lgedeki dağıtım sistemini elektrik enerjisi &uuml;retimi ve satışında rekabet ortamına uygun şekilde işletmek,</li>
	<li>Dağıtım sistemi tesislerini yenilemek, kapasite ikame ve artırım yatırımlarını yapmak,</li>
	<li>Dağıtım sistemine bağlı ve/veya bağlanacak olan t&uuml;m dağıtım sistemi kullanıcılarına ilgili mevzuat h&uuml;k&uuml;mleri doğrultusunda eşit taraflar arasında ayrım g&ouml;zetmeksizin hizmet sunmak,</li>
	<li>Yan hizmetleri sağlamak,</li>
	<li>Dağıtım lisansımızda belirlenen b&ouml;lgelerde talep tahminlerini hazırlamak,</li>
	<li>Yatırım planlarını hazırlamak, gerekli iyileştirme ve kapasite artırımı yatırımlarını yapmak ve/veya yeni dağıtım tesisleri inşa etmek,</li>
	<li>Dağıtım gerilim seviyesinden bağlı t&uuml;ketici saya&ccedil;larının kurulumunu yapmak, saya&ccedil;ları işletmek ve bakımını yapmak ile mevcut saya&ccedil;ların bir program d&acirc;hilinde m&uuml;lkiyetini devralmak,</li>
	<li>Dağıtım b&ouml;lgemizde, genel aydınlatma ve buna ait gerekli &ouml;l&ccedil;&uuml;m sistemlerini tesis etmek ve işletmek,</li>
	<li>Dağıtım faaliyetinde bulunan lisans sahibi &ouml;zel hukuk t&uuml;zel kişilerinin lisansa konu faaliyetlerine ilişkin taşınmaz temini taleplerini y&uuml;r&uuml;tmek,</li>
	<li>Arıza, bakım ve onarım s&uuml;re&ccedil;lerini y&uuml;r&uuml;tmek, takip etmek, talepleri karşılamak,</li>
	<li>Ka&ccedil;ak ve usuls&uuml;z elektrik enerjisi t&uuml;ketimini tespit etmek ve buna ilişkin s&uuml;reci y&uuml;r&uuml;tmek,</li>
	<li>Elektrik piyasasında dağıtım ve &uuml;retim faaliyetlerine ilişkin taşınmaz temini işlemlerini y&uuml;r&uuml;tmek,</li>
	<li>Elektrik tesisleri proje onay, kabul ve tutanak onay işlemlerini y&uuml;r&uuml;tmek,</li>
	<li>Elektrik enerjisi &uuml;retmek, iletmek ve/veya dağıtmak &uuml;zere kurulacak tesislerin kabul işlemlerini y&uuml;r&uuml;tmek,</li>
	<li>İlgili mevzuat uyarınca kullanım yerinin elektriğini kesmek ve bağlamak,</li>
	<li>Sayacın arızalanması veya &ouml;l&ccedil;me hassasiyetinden ş&uuml;phe edilmesi halinde, sayacı s&ouml;kerek değiştirmek ve t&uuml;keticiyi bilgilendirmek,</li>
	<li>Elektrikle &ccedil;alışan diyaliz destek &uuml;nitesi, solunum cihazı ve benzeri mahiyette yaşam destek cihazlarına bağımlı olan ve destekleyici belgelere sahip t&uuml;keticileri kayıt altına almak</li>
	<li>Faaliyet konumuz ile ilgili olarak arıza bildirimi, ka&ccedil;ak ve usuls&uuml;z elektrik enerjisi kullanım ihbarları, itirazlar, şik&acirc;yetler ve benzeri konularda yapılan başvuruları almak ve cevaplandırılmak,</li>
	<li>İtirazlar, şik&acirc;yetler, taleplere ilişkin bilgilendirme ve y&ouml;nlendirme konularında kullanıcılara ve/veya t&uuml;keticilere yardımcı olmak &uuml;zere kurulan &ccedil;ağrı merkezi &uuml;zerinden hizmet vermek,</li>
	<li>T&uuml;keticilerin yeterli, g&uuml;venli, s&uuml;rekli ve kolay bir şekilde hizmet almalarını sağlamak ve gerekli bilgilendirmeleri yapmak,</li>
	<li>Elektrik Piyasası Kanunu ve ikincil mevzuatı kapsamında y&uuml;k&uuml;ml&uuml; olduğumuz bildirimleri yapmak,</li>
	<li>Teknik kaliteyi &ouml;l&ccedil;mek, teknik kalite &ouml;l&ccedil;&uuml;mlerini raporlamak ve teknik kalite &ouml;l&ccedil;&uuml;m sonu&ccedil;larını değerlendirmek,</li>
	<li>Elektrik Piyasası Kanunu ve ikincil mevzuatında belirlenen esaslar &ccedil;er&ccedil;evesinde tazminat &ouml;demelerini ve buna ilişkin bildirimleri yapmak,</li>
	<li>Elektrik dağıtım sisteminden kaynaklanan kalite sorunları nedeniyle te&ccedil;hizatta oluşan hasara ilişkin başvuru ve zararın tazmini ya da onarım s&uuml;recini y&uuml;r&uuml;tmek,</li>
	<li>M&uuml;şteri İlişkileri birimi tarafından kaydedilen başvuruları sonu&ccedil;landırarak başvuru sahibini bilgilendirmek,</li>
	<li>Elektrik dağıtım şirketlerinin dağıtım faaliyeti kapsamında yapacağı her t&uuml;r yapım işleri, mal ve hizmet alımı satın alma, satma ile ihale s&uuml;re&ccedil; ve işlemlerini ger&ccedil;ekleştirmek,</li>
	<li>Dağıtım sistemine bağlantı ve sistem kullanım taleplerini eşit taraflar arasında ayrım g&ouml;zetmeksizin karşılamak,</li>
	<li>Kullanıcı ile dağıtım bağlantı anlaşması ve sistem kullanım anlaşması imzalamak,</li>
	<li>Ge&ccedil;ici kabul yetkisinin dağıtım şirketinde olduğu durumlarda buna ilişkin iş ve işlemleri y&uuml;r&uuml;tmek,</li>
	<li>Dağıtım şebekesine bağlantı sağlanması i&ccedil;in bağlantı hattı tesis s&uuml;re&ccedil;lerini y&uuml;r&uuml;tmek,</li>
	<li>Başvuru sahibinin gerekli dağıtım şebekesinin yapımını &uuml;stlenmeyi tercih etmesi durumunda yapım s&ouml;zleşmesi imzalamak ve proje hazırlamak, yatırıma ait bedeli kullanıcıya &ouml;demek,</li>
	<li>&Ouml;nlisans ve lisans alma ile şirket kurma y&uuml;k&uuml;ml&uuml;l&uuml;ğ&uuml;nden muaf olarak kurulabilecek &uuml;retim tesislerine ilişkin işlemleri y&uuml;r&uuml;tmek,</li>
	<li>İlgili mevzuat kapsamında &uuml;retici ve t&uuml;ketici saya&ccedil;larını Otomatik Saya&ccedil; Okuma Sistemi (OSOS) kapsamına dahil etmek,</li>
	<li>OSOS vasıtasıyla elde edilen verileri aşağıdaki ilgili taraflarla g&uuml;venli ortamda ve internet tabanlı paylaşmak,</li>
	<li>Tedarik&ccedil;i, &uuml;retici veya t&uuml;ketici tarafından talep edilen OSOS verilerini paylaşmak,</li>
	<li>Dağıtım sistemine bağlı t&uuml;m kullanıcı tesislerinin coğrafi konumlarıyla eşleştirilmiş verileri Coğrafi Bilgi Sistemi&rsquo;ne (CBS) kaydetmek,</li>
	<li>Araştırma, geliştirme ve yenilik faaliyetlerini desteklemek ve teşvik etmek,</li>
	<li>Hukuki ve ticari g&uuml;venliği sağlamak,</li>
	<li>&Ccedil;alışma ilişkileri ve insan kaynakları politikalarını y&uuml;r&uuml;tmek,</li>
	<li>İşe alım s&uuml;re&ccedil;lerini planlamak ve y&uuml;r&uuml;tmek,</li>
	<li>Engelli &ccedil;alıştırma y&uuml;k&uuml;ml&uuml;l&uuml;ğ&uuml;n&uuml; yerine getirmek,</li>
	<li>&Ouml;d&uuml;l, yetenek y&ouml;netimi ve kariyer gelişim faaliyetlerini planlanmak ve y&uuml;r&uuml;tmek,</li>
	<li>İş s&ouml;zleşmesinin kurulması ve kanundan doğan iş s&ouml;zleşmesi edimlerini yerine getirmek,</li>
	<li>Sosyal sigortalar ile genel sağlık sigortası bakımından iş&ccedil;iyi g&uuml;vence altına almak; sosyal sigortalar ve genel sağlık sigortasının işleyişi ile ilgili y&uuml;k&uuml;ml&uuml;l&uuml;kleri yerine getirmek,</li>
	<li>İş&ccedil;i &ouml;zl&uuml;k dosyasının tutmak,</li>
	<li>Sendikal hakları sağlamak, sendikal işlemleri y&uuml;r&uuml;tmek ve toplu iş s&ouml;zleşmesinden kaynaklanan y&uuml;k&uuml;ml&uuml;l&uuml;kleri yerine getirmek,</li>
	<li>Sağlık ve hayat sigortası yaptırmak,</li>
	<li>Bireysel emeklilik işlemlerini y&uuml;r&uuml;tmek,</li>
	<li>İşyerlerinde iş sağlığı ve g&uuml;venliğini sağlamak ve mevcut sağlık ve g&uuml;venlik şartlarını iyileştirmek i&ccedil;in g&ouml;rev, yetki, sorumluluk, hak ve y&uuml;k&uuml;ml&uuml;l&uuml;kleri yerine getirmek,</li>
	<li>&Ccedil;alışan adayı / stajyer / &ouml;ğrenci se&ccedil;me ve yerleştirme s&uuml;re&ccedil;lerini y&uuml;r&uuml;tmek</li>
	<li>İmza sirk&uuml;leri ve vek&acirc;letname d&uuml;zenlenmek,</li>
	<li>Genel kurul ve y&ouml;netim kurulu karar /tutanak s&uuml;re&ccedil;lerini y&uuml;r&uuml;tmek,</li>
	<li>Ticari defter, finansal tablo, pay defterinde yer alan kayıtları oluşturmak,</li>
	<li>Şirketin taraf olduğu s&ouml;zleşmeleri imzalamak,</li>
	<li>Fatura, gider pusulası, makbuzları, imzalamak ve takip etmek,</li>
	<li>&Ccedil;alışanlar hakkındaki disiplin s&uuml;re&ccedil;lerini y&ouml;netmek,</li>
	<li>Piyasa Y&ouml;netim Sistemi&rsquo;ne veri aktarmak,</li>
	<li>Hukuk işlerini takip etmek ve y&uuml;r&uuml;tmek,</li>
	<li>Taksitlendirme ve icra işlemlerini y&uuml;r&uuml;tmek,</li>
	<li>Finans ve muhasebe işlerini y&uuml;r&uuml;tmek,</li>
	<li>Fiziksel mek&acirc;n g&uuml;venliğini temin etmek,</li>
	<li>Ziyaret&ccedil;i kayıtlarını oluşturmak,</li>
	<li>İş stratejilerini belirlemek,</li>
	<li>Anket ve araştırma yapmak,</li>
	<li>Sosyal medya ve kurumsal iletişim s&uuml;re&ccedil;leri kapsamındaki faaliyetleri planlamak ve y&uuml;r&uuml;tmek,</li>
	<li>Sosyal sorumluluk aktivitelerini y&uuml;r&uuml;tmek,</li>
	<li>Sponsorluk faaliyetlerini planlamak ve y&uuml;r&uuml;tmek,</li>
	<li>Lojistik faaliyetlerini y&uuml;r&uuml;tmek,</li>
	<li>Bilgi g&uuml;venliği s&uuml;re&ccedil;lerini planlamak, denetimi veya icrasını yerine getirmek,</li>
	<li>Bilgi teknolojileri alt yapısını oluşturmak ve y&ouml;netmek,</li>
	<li>Operasyonel risk s&uuml;re&ccedil;lerini planlamak ve y&uuml;r&uuml;tmek,</li>
	<li>Re&rsquo;sen veya şikayet &uuml;zerine i&ccedil; denetim/i&ccedil; kontrol/soruşturma/etik faaliyetlerini planlamak ve y&uuml;r&uuml;tmek,</li>
	<li>Finansal risk s&uuml;re&ccedil;lerini planlamak ve y&uuml;r&uuml;tmek,</li>
	<li>Şirketimizin sistem ve operasyonlarının g&uuml;venliğini temin etmek,</li>
	<li>Acil durum veya olay y&ouml;netimi s&uuml;re&ccedil;lerini planlamak ve y&uuml;r&uuml;tmek,</li>
	<li>Yetkili kişi, kurum ve kuruluşlara bilgi vermek,</li>
	<li>Enerjisa Grup Şirketleri1 ve Hacı &Ouml;mer Sabancı Holding Şirketlerinin2 sunduğu mal ve hizmetlere y&ouml;nelik indirim, hediye &ccedil;eki/kuponu, hediye puan, promosyon &uuml;r&uuml;n sağlamak,</li>
	<li>Saklama ve arşiv faaliyetlerini y&uuml;r&uuml;tmek,</li>
	<li>Organizasyon ve etkinlik y&ouml;netimini sağlamak,</li>
</ul>

<p></p>

<h3 class="container-box--title">4. KİŞİSEL VERİLERİNİZİN AKTARILABİLECEĞİ KİŞİ YA DA KURULUŞLAR VE AKTARILMA AMA&Ccedil;LARI</h3>

<p>Kişisel verilerinizi,</p>

<ul class="list-default">
	<li>Elektrik Piyasası Kanunu ve ikincil mevzuatı uyarınca d&uuml;zenleme ve denetim yetkileri kapsamında şeffaflığın sağlanması, ihtilafların &ccedil;&ouml;z&uuml;m&uuml;, doğabilecek uyuşmazlıklarda delil olarak kullanılabilmesi ama&ccedil;larıyla Enerji Piyasası D&uuml;zenleme Kurumu&rsquo;na (EPDK), Enerji ve Tabii Kaynaklar Bakanlığı&rsquo;na; denetim yetkisi kapsamında T&uuml;rkiye Elektrik Dağıtım A.Ş.&rsquo;ye,</li>
	<li>Kanunlarda a&ccedil;ık&ccedil;a &ouml;ng&ouml;r&uuml;lm&uuml;ş olması halinde kamu otoritelerine, &ouml;zel hukuk ve kamu hukuku t&uuml;zel kişilerine, ger&ccedil;ek kişilere, topluluk şirketlerine, doğrudan veya dolaylı pay sahiplerine,</li>
	<li>Bir s&ouml;zleşmenin kurulması veya ifasıyla doğrudan doğruya ilgili olması kaydıyla, banka ve finans kuruluşlarına, sigorta şirketlerine, dijitalleşme, bilgi ve teknoloji işlerini y&uuml;r&uuml;ten kişi ve kuruluşlara, yerel idareler ve alt yapı kuruluşları da d&acirc;hil olmak &uuml;zere ger&ccedil;ek kişilere ve &ouml;zel hukuk t&uuml;zel kişilerine, hissedarımıza, tedarik&ccedil;ilerimize, topluluk şirketlerine ve yetkili kamu kuruluşlarına,</li>
	<li>Hukuki y&uuml;k&uuml;ml&uuml;l&uuml;ğ&uuml;m&uuml;z&uuml;n yerine getirilmesi i&ccedil;in zorunlu olması nedeniyle tedarik&ccedil;ilerimize, iş ortaklarımıza, topluluk şirketlerine, doğrudan veya dolaylı pay sahiplerimize, yerel idareler, alt yapı kuruluşları da d&acirc;hil olmak &uuml;zere yetkili &ouml;zel hukuk ve kamu hukuku t&uuml;zel kişilerine ve ger&ccedil;ek kişilere,</li>
	<li>Bir hakkın tesisi, kullanılması veya korunması i&ccedil;in kamu otoritelerine, yetkili &ouml;zel hukuk ve kamu hukuku t&uuml;zel kişilerine ve ger&ccedil;ek kişilere,</li>
	<li>Veri sorumlusu şirketimizin meşru menfaati ve topluluk şirketi h&uuml;k&uuml;mlerinin d&uuml;zenleyen mevzuat kapsamında Enerjisa Grup şirketlerine<sup>1</sup> ve Hacı &Ouml;mer Sabancı Holding şirketlerine<sup>2</sup> ,&nbsp;</li>
</ul>

<p></p>

<p>aktarmaktayız.</p>

<p></p>

<h3 class="container-box--title">5. KİŞİSEL VERİLERİN TOPLANMA Y&Ouml;NTEMİ&nbsp;VE HUKUKİ SEBEP</h3>

<p>Kişisel verilerinizi;</p>

<ul class="list-default">
	<li>Elektrik Piyasası Kanunu, Elektrik Dağıtımı ve Perakende Satış Faaliyetlerine İlişkin Kalite Y&ouml;netmeliği ve Elektrik Piyasası T&uuml;ketici Hizmetleri Y&ouml;netmeliği ile Elektrik Piyasası Kanunu uyarınca yayımlanan ikincil mevzuat ile elektrik piyasasını d&uuml;zenleyen t&uuml;m mevzuat başta olmak &uuml;zere, veri sorumlusu olarak veri sorumlusunun hukuki y&uuml;k&uuml;ml&uuml;l&uuml;ğ&uuml;n&uuml; yerine getirebilmesi i&ccedil;in zorunlu olması ve kanunlarda a&ccedil;ık&ccedil;a &ouml;ng&ouml;r&uuml;lmesi i&ccedil;in zorunlu olması hukuki sebeplerine dayalı olarak;
	<ul>
		<li>Elektrik Piyasası Kanunu ve ikincil mevzuatı başta olmak &uuml;zere şirketimizin tabi olduğu mevzuattan doğan y&uuml;k&uuml;ml&uuml;l&uuml;kleri yerine getirerek faaliyetleri mevzuata uygun y&uuml;r&uuml;tmek,</li>
		<li>Elektrik dağıtım lisansımızda belirlenen b&ouml;lgede saya&ccedil;ları okumak,</li>
		<li>Saya&ccedil;ların bakımı ve işletilmesi hizmetlerini yerine getirmek,</li>
		<li>Elektrik dağıtım lisansımızda belirtilen b&ouml;lgedeki dağıtım sistemini elektrik enerjisi &uuml;retimi ve satışında rekabet ortamına uygun şekilde işletmek,</li>
		<li>Dağıtım sistemi tesislerini yenilemek, kapasite ikame ve artırım yatırımlarını yapmak,</li>
		<li>Dağıtım sistemine bağlı ve/veya bağlanacak olan t&uuml;m dağıtım sistemi kullanıcılarına ilgili mevzuat h&uuml;k&uuml;mleri doğrultusunda eşit taraflar arasında ayrım g&ouml;zetmeksizin hizmet sunmak,</li>
		<li>Yan hizmetleri sağlamak,</li>
		<li>Dağıtım lisansımızda belirlenen b&ouml;lgelerde talep tahminlerini hazırlamak,</li>
		<li>Yatırım planlarını hazırlamak, gerekli iyileştirme ve kapasite artırımı yatırımlarını yapmak ve/veya yeni dağıtım tesisleri inşa etmek,</li>
		<li>Dağıtım gerilim seviyesinden bağlı t&uuml;ketici saya&ccedil;larının kurulumunu yapmak, saya&ccedil;ları işletmek ve bakımını yapmak ile mevcut saya&ccedil;ların bir program d&acirc;hilinde m&uuml;lkiyetini devralmak,</li>
		<li>Dağıtım b&ouml;lgemizde, genel aydınlatma ve buna ait gerekli &ouml;l&ccedil;&uuml;m sistemlerini tesis etmek ve işletmek,</li>
		<li>Dağıtım faaliyetinde bulunan lisans sahibi &ouml;zel hukuk t&uuml;zel kişilerinin lisansa konu faaliyetlerine ilişkin taşınmaz temini taleplerini y&uuml;r&uuml;tmek,</li>
		<li>İş&ccedil;i &ouml;zl&uuml;k dosyasını tutmak,</li>
		<li>Sosyal sigortalar ile genel sağlık sigortası bakımından iş&ccedil;iyi g&uuml;vence altına almak; sosyal sigortalar ve genel sağlık sigortasının işleyişi ile ilgili y&uuml;k&uuml;ml&uuml;l&uuml;kleri yerine getirmek,</li>
		<li>Sendikal hakları sağlamak, sendikal işlemleri y&uuml;r&uuml;tmek ve toplu iş s&ouml;zleşmesinden kaynaklanan y&uuml;k&uuml;ml&uuml;l&uuml;kleri yerine getirmek,</li>
		<li>Sağlık ve hayat sigortası yaptırmak, &bull; Bireysel emeklilik işlemlerini y&uuml;r&uuml;tmek,</li>
		<li>İşyerlerinde iş sağlığı ve g&uuml;venliğini sağlamak ve mevcut sağlık ve g&uuml;venlik şartlarını iyileştirmek i&ccedil;in g&ouml;rev, yetki, sorumluluk, hak ve y&uuml;k&uuml;ml&uuml;l&uuml;kleri yerine getirmek,</li>
		<li>Engelli &ccedil;alıştırma y&uuml;k&uuml;ml&uuml;l&uuml;ğ&uuml;n&uuml; yerine getirmek,</li>
		<li>İmza sirk&uuml;leri ve vek&acirc;letname d&uuml;zenlenmek,</li>
		<li>Genel kurul ve y&ouml;netim kurulu karar /tutanak s&uuml;re&ccedil;lerini y&uuml;r&uuml;tmek,</li>
		<li>Ticari defter, finansal tablo, pay defterinde yer alan kayıtları oluşturmak,</li>
		<li>Şirketin taraf olduğu s&ouml;zleşmeleri imzalamak,</li>
		<li>Fatura, gider pusulası, makbuzları, imzalamak ve takip etmek,</li>
		<li>&Ccedil;alışanlar hakkındaki disiplin s&uuml;re&ccedil;lerini y&ouml;netmek,</li>
		<li>Saklama ve arşiv faaliyetlerini y&uuml;r&uuml;tmek,</li>
	</ul>
	</li>
	<li>Bir s&ouml;zleşmenin kurulması veya ifasıyla doğrudan doğruya ilgili olması kaydıyla, s&ouml;zleşmenin taraflarına ait kişisel verilerin işlenmesinin gerekli olması hukuki sebebine dayalı olarak;
	<ul>
		<li>Kullanıcı ile dağıtım bağlantı anlaşması ve sistem kullanım anlaşması imzalamak,</li>
		<li>Ge&ccedil;ici kabul yetkisinin dağıtım şirketinde olduğu durumlarda buna ilişkin iş ve işlemleri y&uuml;r&uuml;tmek,</li>
		<li>Dağıtım şebekesine bağlantı sağlanması i&ccedil;in bağlantı hattı tesis s&uuml;re&ccedil;lerini y&uuml;r&uuml;tmek,</li>
		<li>Başvuru sahibinin gerekli dağıtım şebekesinin yapımını &uuml;stlenmeyi tercih etmesi durumunda yapım s&ouml;zleşmesi imzalamak ve proje hazırlamak, yatırıma ait bedeli kullanıcıya &ouml;demek,</li>
		<li>&Ouml;nlisans ve lisans alma ile şirket kurma y&uuml;k&uuml;ml&uuml;l&uuml;ğ&uuml;nden muaf olarak kurulabilecek &uuml;retim tesislerine ilişkin işlemleri y&uuml;r&uuml;tmek,</li>
		<li>İlgili mevzuat kapsamında &uuml;retici ve t&uuml;ketici saya&ccedil;larını Otomatik Saya&ccedil; Okuma Sistemi (OSOS) kapsamına dahil etmek,</li>
		<li>OSOS vasıtasıyla elde edilen verileri aşağıdaki ilgili taraflarla g&uuml;venli ortamda ve internet tabanlı paylaşmak,</li>
		<li>Tedarik&ccedil;i, &uuml;retici veya t&uuml;ketici tarafından talep edilen OSOS verilerini paylaşmak,</li>
		<li>&Ccedil;alışma ilişkileri ve insan kaynakları politikalarını y&uuml;r&uuml;tmek,</li>
		<li>İşe alım s&uuml;re&ccedil;lerini planlamak ve y&uuml;r&uuml;tmek,</li>
		<li>&Ouml;d&uuml;l, yetenek y&ouml;netimi ve kariyer gelişim faaliyetlerini planlanmak ve y&uuml;r&uuml;tmek,</li>
		<li>İş s&ouml;zleşmesinin kurulması ve kanundan doğan iş s&ouml;zleşmesi edimlerini yerine getirmek,</li>
	</ul>
	</li>
	<li>Veri sorumlusunun hukuki y&uuml;k&uuml;ml&uuml;l&uuml;ğ&uuml;n&uuml; yerine getirebilmesi i&ccedil;in zorunlu olması hukuki sebebine dayalı olarak;
	<ul>
		<li>Elektrik tesisleri proje onay, kabul ve tutanak onay işlemlerini y&uuml;r&uuml;tmek,</li>
		<li>Elektrik enerjisi &uuml;retmek, iletmek ve/veya dağıtmak &uuml;zere kurulacak tesislerin kabul işlemlerini y&uuml;r&uuml;tmek,</li>
		<li>İlgili mevzuat uyarınca kullanım yerinin elektriğini kesmek ve bağlamak,</li>
		<li>Sayacın arızalanması veya &ouml;l&ccedil;me hassasiyetinden ş&uuml;phe edilmesi halinde, sayacı s&ouml;kerek değiştirmek ve t&uuml;keticiyi bilgilendirmek,</li>
		<li>Elektrikle &ccedil;alışan diyaliz destek &uuml;nitesi, solunum cihazı ve benzeri mahiyette yaşam destek cihazlarına bağımlı olan ve destekleyici belgelere sahip t&uuml;keticileri kayıt altına almak,</li>
		<li>Faaliyet konumuz ile ilgili olarak arıza bildirimi, ka&ccedil;ak ve usuls&uuml;z elektrik enerjisi kullanım ihbarları, itirazlar, şik&acirc;yetler ve benzeri konularda yapılan başvuruları almak ve cevaplandırmak,</li>
		<li>İtirazlar, şik&acirc;yetler, taleplere ilişkin bilgilendirme ve y&ouml;nlendirme konularında kullanıcılara ve/veya t&uuml;keticilere yardımcı olmak &uuml;zere kurulan &ccedil;ağrı merkezi &uuml;zerinden hizmet vermek,</li>
		<li>T&uuml;keticilerin yeterli, g&uuml;venli, s&uuml;rekli ve kolay bir şekilde hizmet almalarını sağlamak ve gerekli bilgilendirmeleri yapmak,</li>
		<li>Elektrik Piyasası Kanunu ve ikincil mevzuatı kapsamında y&uuml;k&uuml;ml&uuml; olduğu bildirimleri yapmak,</li>
		<li>Teknik kaliteyi &ouml;l&ccedil;mek, teknik kalite &ouml;l&ccedil;&uuml;mlerinin raporlanmasına ve teknik kalite &ouml;l&ccedil;&uuml;m sonu&ccedil;larının değerlendirilmesi,</li>
		<li>Elektrik Piyasası Kanunu ve ikincil mevzuatında belirlenen esaslar &ccedil;er&ccedil;evesinde tazminat &ouml;demelerini ve buna ilişkin bildirimleri yapmak,</li>
		<li>Elektrik dağıtım sisteminden kaynaklanan kalite sorunları nedeniyle te&ccedil;hizatta oluşan hasara ilişkin başvuru ve zararın tazmini ya da onarım s&uuml;recini y&uuml;r&uuml;tmek,</li>
		<li>M&uuml;şteri İlişkileri birimi tarafından kaydedilen başvuruları sonu&ccedil;landırarak başvuru sahibini bilgilendirmek,</li>
		<li>Elektrik dağıtım şirketlerinin dağıtım faaliyeti kapsamında yapacağı her t&uuml;r yapım işleri, mal ve hizmet alımı satın alma, satma ile ihale s&uuml;re&ccedil; ve işlemlerini ger&ccedil;ekleştirmek,</li>
		<li>Dağıtım sistemine bağlantı ve sistem kullanım taleplerini eşit taraflar arasında ayrım g&ouml;zetmeksizin karşılamak,</li>
		<li>Dağıtım sistemine bağlı t&uuml;m kullanıcı tesislerinin coğrafi konumlarıyla eşleştirilmiş verileri Coğrafi Bilgi Sistemi&rsquo;ne (CBS) kaydetmek,</li>
		<li>Araştırma, geliştirme ve yenilik faaliyetlerini desteklemek ve teşvik etmek,</li>
		<li>Hukuki ve ticari g&uuml;venliği sağlamak,</li>
		<li>İnsan kaynakları politikalarını y&uuml;r&uuml;tmek,</li>
		<li>Piyasa Y&ouml;netim Sistemi&rsquo;ne veri aktarmak,</li>
		<li>Finans ve muhasebe işlerini y&uuml;r&uuml;tmek,</li>
		<li>Bilgi g&uuml;venliği s&uuml;re&ccedil;lerini planlamak, denetimi veya icrasını yerine getirmek,</li>
		<li>Bilgi teknolojileri alt yapısını oluşturmak ve y&ouml;netmek,</li>
		<li>Re&rsquo;sen veya şik&acirc;yet &uuml;zerine i&ccedil; denetim/i&ccedil; kontrol/soruşturma/etik faaliyetlerini planlamak ve y&uuml;r&uuml;tmek,</li>
		<li>Acil durum veya olay y&ouml;netimi s&uuml;re&ccedil;lerini planlamak ve y&uuml;r&uuml;tmek</li>
	</ul>
	</li>
	<li>İlgili kişinin temel hak ve &ouml;zg&uuml;rl&uuml;klerine zarar vermemek kaydıyla, veri sorumlusunun meşru menfaatleri i&ccedil;in veri işlenmesinin zorunlu olması hukuki sebebine dayalı olarak;
	<ul>
		<li>İş stratejilerini belirlemek,</li>
		<li>Anket ve araştırma yapmak,</li>
		<li>Sosyal medya ve kurumsal iletişim s&uuml;re&ccedil;leri kapsamındaki faaliyetleri planlamak ve y&uuml;r&uuml;tmek,</li>
		<li>Fiziksel mek&acirc;n g&uuml;venliğini temin etmek,</li>
		<li>Ziyaret&ccedil;i kayıtlarını oluşturmak,</li>
		<li>Operasyonel risk s&uuml;re&ccedil;lerini planlamak ve y&uuml;r&uuml;tmek,</li>
		<li>Finansal risk s&uuml;re&ccedil;lerini planlamak ve y&uuml;r&uuml;tmek,</li>
		<li>Şirketimizin sistem ve operasyonlarının g&uuml;venliğini temin etmek,</li>
		<li>Sosyal sorumluluk aktivitelerini y&uuml;r&uuml;tmek,</li>
		<li>Sponsorluk faaliyetlerini planlamak ve y&uuml;r&uuml;tmek,</li>
		<li>Lojistik faaliyetlerini y&uuml;r&uuml;tmek,</li>
		<li>Yetkili kişi, kurum ve kuruluşlara bilgi vermek,</li>
		<li>Enerjisa Grup Şirketleri ve Hacı &Ouml;mer Sabancı Holding Şirketlerinin sunduğu mal ve hizmetlere y&ouml;nelik indirim, hediye &ccedil;eki/kuponu, hediye puan, promosyon &uuml;r&uuml;n sağlamak,</li>
		<li>&Ccedil;alışan adayı / stajyer / &ouml;ğrenci se&ccedil;me ve yerleştirme s&uuml;re&ccedil;lerini y&uuml;r&uuml;tmek,</li>
		<li>Organizasyon ve etkinlik y&ouml;netimini sağlamak,</li>
		<li>İş s&ouml;zleşmesinin kurulması kapsamında elde edilen kişisel verileri insan kaynakları ve &ccedil;alışma ilişkileri s&uuml;re&ccedil;leri ve projeleri, organizasyonel bağlılık, sosyal sorumluluk projeleri kapsamında Hacı &Ouml;mer Sabancı Holding A.Ş.&rsquo;ye aktarmak,</li>
	</ul>
	</li>
	<li>Bir hakkın tesisi, kullanılması veya korunması i&ccedil;in veri işlemenin zorunlu olması hukuki sebebine dayalı olarak;
	<ul>
		<li>M&uuml;şteri İlişkileri birimi tarafından kaydedilen başvuruları sonu&ccedil;landırarak başvuru sahibini bilgilendirmek,</li>
		<li>Hukuk işlerini takip etmek ve y&uuml;r&uuml;tmek,</li>
		<li>Taksitlendirme ve icra işlemlerini y&uuml;r&uuml;tmek,</li>
	</ul>
	</li>
	<li>A&ccedil;ık rıza vermeniz halinde;
	<ul>
		<li>Adli sicil ve arşiv kaydı almak ve personel &ouml;zl&uuml;k dosyasında tutmak,</li>
		<li>Dernek &uuml;yeliği hakkında bilgileri işlemek,</li>
		<li>Engelli verisini işlemek,</li>
		<li>Sağlık verilerini sağlık sigortası yaptırmak amacıyla işlemek ve yetkili sigorta şirketine aktarmak,</li>
		<li>İstihdam s&uuml;re&ccedil;leri, &uuml;cret y&ouml;netimi, bağlılık ve anket araştırması işlemlerini y&uuml;r&uuml;tmek &uuml;zere kişileri verileri yurtdışında yerleşik bulut sunucusu ve platformlarda hizmet altyapısını danışmanlık firmalarına ve bu yolla yurtdışına aktarmak,</li>
		<li>Sağlık verilerini işleyerek, Elektrik Piyasası T&uuml;ketici Hizmetleri Y&ouml;netmeliği&rsquo;nin 52. Maddesi kapsamında ve t&uuml;keticinin korunması ile y&uuml;k&uuml;ml&uuml;l&uuml;kleri yerine getirilebilmek ve elektrikle &ccedil;alışan diyaliz destek &uuml;nitesi, solunum cihazı ve benzeri mahiyette yaşam destek cihazlarına bağımlı olan ve destekleyici belgelere sahip t&uuml;keticileri Hasta Var Projesi&rsquo;ne d&acirc;hil edilebilmek,</li>
	</ul>
	</li>
</ul>

<p></p>

<p>ama&ccedil;larıyla toplamaktayız.</p>

<p>Kişisel verilerinizi, fiziki ve/veya elektronik olarak, şirketimizin bilgi ve evrak kayıt sistemi, internet sitesi, Online İşlem Merkezi, elektronik posta, kayıtlı elektronik posta adresleri, kamera ve telefon kayıtları, sosyal medya hesapları, web konferansı/video konferans, telefon konferansı, &ccedil;evrim i&ccedil;i toplantılar, fiziki ve online form, y&uuml;klenici iletişim portali, s&ouml;zleşmeler, iş başvurusu sırasında veya istihdam sağlama platformları, istihdama aracılık eden kamu otoriteleri ve &ouml;zel kuruluşlar, kamu tarafından sunulan işin ve iş&ccedil;inin y&ouml;netimine i&ccedil;in bilgileri i&ccedil;erir platformlar ile bilgi teknoloji alt yapıları, ara&ccedil;ların izlenme sistemleri (GPS), iş sağlığı ve g&uuml;venliğinin sağlanması i&ccedil;in kullanılan sistemler, iş yeri hekimi ve sağlık sunucular ile mobil uygulamalar &uuml;zerinden s&ouml;zl&uuml;, yazılı veya elektronik ortamda toplamaktayız.</p>

<h3 class="container-box--title">6. KİŞİSEL VERİLER İLE İLGİLİ HAKLARINIZ</h3>

<p>6698 sayılı Kişisel Verilerin Korunması Kanunu&rsquo;nun 11&rsquo; inci maddesi kapsamında, kişisel verilerinizin işlenip işlenmediğini &ouml;ğrenebilir, kişisel verileriniz işlenmişse buna ilişkin bilgi talep edebilir, kişisel verilerin işlenme amacını ve bunların amacına uygun kullanılıp kullanılmadığını &ouml;ğrenebilir, yurti&ccedil;inde veya yurtdışında kişisel verilerin aktarıldığı kişileri &ouml;ğrenebilir, kişisel verilerin eksik veya yanlış işlenmesi h&acirc;linde bunların d&uuml;zeltilmesini isteyebilirsiniz. Ayrıca 6698 sayılı Kişisel Verilerin Korunması Kanunu&rsquo;nun 7&rsquo;nci maddesi &ccedil;er&ccedil;evesinde kişisel verilerin silinmesini veya yok edilmesini isteyebilir, eksik veya yanlış işleme d&uuml;zeltilmişse ya da silme, yok etme veya anonim hale getirme işlemleri yapılmışsa bu durumun, verinin aktarıldığı &uuml;&ccedil;&uuml;nc&uuml; kişilere bildirilmesini isteyebilir ya da kişisel verilerin kanuna aykırı olarak işlenmesi sebebiyle zarara uğramanız halinde zararının giderilmesini isteyebilirsiniz.</p>

<p></p>

<h3 class="container-box--title">7. KİŞİSEL VERİLERE İLİŞKİN HAKLARIN KULLANILMASI</h3>

<p>6698 sayılı Kişisel Verilerin Korunması Kanunu&rsquo;nun 11&rsquo;inci maddesi kapsamındaki taleplerinizi &ldquo;Veri Sorumlusuna Başvuru Usul ve Esasları Hakkında Tebliğ&rdquo;de belirtildiği şekilde yazılı olarak veya kayıtlı elektronik posta (KEP) adresi, g&uuml;venli elektronik imza,&nbsp;mobil imza ya da şirketimize bildirdiğiniz ve şirketimizin sisteminde kayıtlı bulunan elektronik posta adresini kullanmak suretiyle iletebilirsiniz. Başvurunuzda Veri Sorumlusuna Başvuru Usul ve Esasları Hakkında Tebliğ&rsquo;in 5&rsquo; inci maddesinin 2&rsquo; nci fıkrasında belirtilen;</p>

<ul class="list-number">
	<li><span class="number">a</span><span> Ad,&nbsp;soy ad&nbsp;ve başvuru yazılı ise imza,</span></li>
	<li><span class="number">b</span><span> T&uuml;rkiye Cumhuriyeti vatandaşları i&ccedil;in T.C. kimlik numarası, yabancılar i&ccedil;in uyruğu, pasaport numarası veya varsa kimlik numarası,</span></li>
	<li><span class="number">c</span><span> Tebligata esas yerleşim yeri veya iş yeri adresi,</span></li>
	<li><span class="number">&ccedil;</span><span> Varsa bildirime esas elektronik posta adresi, telefon ve faks numarası ve</span></li>
	<li><span class="number">d</span><span> Talep konusunun,</span></li>
</ul>

<p>bulunması&nbsp;zorunludur.</p>

<p>İlgili Kişi Başvurunuzu, internet sitemizde KVKK sekmesi altında yer alan &ldquo;<a href="/site-dosya/dosya-on-izleme/9601" target="_blank">İlgili Kişi Başvuru Formu</a>&rdquo; &rsquo;nu doldurarak&nbsp;bize iletebileceğinizi belirtmek isteriz.</p>

<p>Başvurunuzu, şirketimizce yapılacak olan kimlik doğrulamasını takiben en kısa s&uuml;rede ve en ge&ccedil; otuz (30) g&uuml;n i&ccedil;inde &uuml;cretsiz olarak yanıtlayacağız.</p>

<p>Saygılarımızla.</p>

<p></p>

<hr />
<p>1 Enerjisa Grup Şirketleri: Başkent Elektrik Dağıtım A.Ş.,Toroslar Elektrik Dağıtım A.Ş., İstanbul Anadolu Yakası Elektrik Dağıtım A.Ş., Enerjisa Baskent Elektrik Perakende Satıs A.Ş., Enerjisa Toroslar Elektrik Perakende Satıs A.Ş., Enerjisa Istanbul Anadolu Yakası Elektrik Perakende Satıs A.Ş., Enerjisa M&uuml;şteri &Ccedil;&ouml;z&uuml;mleri A.Ş.&rsquo;yi ifade eder.</p>

<p>2 Hacı &Ouml;mer Sabancı Holding Şirketleri; sigorta, bankacılık ve perakende satış sekt&ouml;rlerinde faaliyet g&ouml;steren AgeSA, Aksigorta, TeknoSA, CarrefourSA, Akbank, SabancıDx, Akportf&ouml;y, Ak Yatırım unvanları altındaki şirketler ile Hacı &Ouml;mer Sabancı Holding&rsquo;in %20 ve &uuml;zerinde hisse sahibi olduğu t&uuml;m şirketleri ifade eder.</p>
</div>


        </div>
    

</div>

    <footer class="footer footer-desk">
        <div class="container">
            <div class="footer-top">
                



    <a href="https://wa.link/sm3s84" class="whatsapp" target="_blank" rel="noopener noreferrer">
        <img src="img/whatsapp.svg" alt="whatsapp" />
        <div class="whatsapp-text">
            Whatsapp Destek Hatt&#x131;
            <span>T&#x131;kla Mesaj&#x131;n&#x131; G&#xF6;nder</span>
        </div>
    </a>

                <ul>
                    <li>
                        <a href="/sayfa/kariyer">
                            Ba&#x15F;kent&#x2019;te Kariyer
                            <div class="menu-border"></div>
                        </a>
                    </li>
                    <li><a target="_blank" rel="noopener noreferrer" href="https://stportal.eedas.com.tr/">Serbest T&#xFC;keticiler<div class="menu-border"></div></a></li>
                    <li>
                        <a href="/yetkili-elektrikci">
                            Yetkili Elektrik&#xE7;i Listesi<div class="menu-border"></div>
                        </a>
                    </li>
                    <li><a href="/sayfa/cerez-politikasi">&#xC7;erez Politikas&#x131; &amp; Ayarlar&#x131;<div class="menu-border"></div></a></li>
                </ul>
            </div>
            <div class="footer-center">
                <div class="cagri-merkezi">
                    <a href="tel:186">
                        <img src="img/phone_f.svg" alt="telefon" />
                        <strong>186</strong>
                        &#xC7;a&#x11F;r&#x131; Merkezi
                    </a>
                </div>
                <div class="social">
                    <p>Bizi Takip Edin</p>
                    <a href="https://www.facebook.com/baskentedastr" target="_blank">
                        <img src="img/facebook_f.svg" alt="facebook" />
                    </a>
                    <a href="https://www.linkedin.com/company/baskentedas/" target="_blank">
                        <img src="img/linkedin_f.svg" alt="linkedin" />
                    </a>
                    <a href="https://twitter.com/baskentdestek" target="_blank">
                        <img src="img/twitter_f.svg" alt="twitter" />
                    </a>
                </div>
            </div>
            <div class="footer-bottom">
                <ul>
                    <li><a href="/site-haritasi">Site Haritas&#x131;</a></li>
                    <li><a target="_blank" rel="noopener noreferrer" href="https://e-sirket.mkk.com.tr/esir/Dashboard.jsp#/sirketbilgileri/11483">Bilgi Toplumu Hizmeti</a></li>
                </ul>

                <p>Yetkili Elektrik&#xE7;iler &#x130;&#xE7;in<a target="_blank" rel="noopener noreferrer" href="https://ybp.eedas.com.tr/">Yeni Ba&#x11F;lant&#x131; Portal&#x131;</a></p>
            </div>
        </div>
    </footer>

    <footer class="footer footer-mobile">
        <div class="container">
            <div class="social">
                <p>Bizi Takip Edin</p>
                <a href="https://www.facebook.com/baskentedastr" target="_blank">
                    <img src="img/facebook_f.svg" alt="facebook" />
                </a>
                <a href="https://www.linkedin.com/company/baskentedas/" target="_blank">
                    <img src="img/linkedin_f.svg" alt="linkedin" />
                </a>
                <a href="https://twitter.com/baskentdestek" target="_blank">
                    <img src="img/twitter_f.svg" alt="twitter" />
                </a>
            </div>
            <hr />
            <div class="footer-top">
                <ul>
                    <li>
                        <a href="/sayfa/kariyer">
                            Ba&#x15F;kent&#x2019;te Kariyer
                            <div class="menu-border"></div>
                        </a>
                    </li>
                    <li><a target="_blank" rel="noopener noreferrer" href="https://stportal.eedas.com.tr/">Serbest T&#xFC;keticiler<div class="menu-border"></div></a></li>
                    <li><a href="/yetkili-elektrikci">Yetkili Elektrik&#xE7;i Listesi <div class="menu-border"></div></a></li>
                    <li>
                        <a href="/sayfa/cerez-politikasi">
                            &#xC7;erez Politikas&#x131; &amp; Ayarlar&#x131;<div class="menu-border"></div>
                        </a>
                    </li>
                </ul>
            </div>
            <hr class="mb-0 mt-0" />
            <div class="footer-bottom">
                <ul>
                    <li><a href="/site-haritasi">Site Haritas&#x131;</a></li>
                    <li><a target="_blank" rel="noopener noreferrer" href="https://e-sirket.mkk.com.tr/esir/Dashboard.jsp#/sirketbilgileri/11483">Bilgi Toplumu Hizmeti</a></li>
                </ul>
                <hr />
                <p>
                    Yetkili Elektrik&#xE7;iler &#x130;&#xE7;in<a target="_blank" rel="noopener noreferrer" href="https://ybp.eedas.com.tr/">
                        Yeni Ba&#x11F;lant&#x131; Portal&#x131;
                    </a>
                </p>
            </div>
            <hr />
            <div Class="footer-top">
                



    <a href="https://wa.link/sm3s84" class="whatsapp" target="_blank" rel="noopener noreferrer">
        <img src="img/whatsapp.svg" alt="whatsapp" />
        <div class="whatsapp-text">
            Whatsapp Destek Hatt&#x131;
            <span>T&#x131;kla Mesaj&#x131;n&#x131; G&#xF6;nder</span>
        </div>
    </a>


                <div class="cagri-merkezi">
                    <a href="tel:186">
                        <img src="img/phone_f.svg" alt="telefon" />
                        <strong>186</strong>
                        &#xC7;a&#x11F;r&#x131; Merkezi
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <div class="modal fade modal-default" id="tesisat-no-nedir" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="modalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content pa-10">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalLabel">Tesisat No Nedir?</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <img src="img/close.svg" width="20" alt="kapat" />
                    </button>
                </div>
                <div class="modal-body pt-0">
                    <img src="img/Tesisat-No-Nedir.png" width="100%" alt="tesisat no nedir" />
                </div>
            </div>
        </div>
    </div>

    <script src="css/bootstrap.bundle.min.js"></script>
    <script src="css/bootstrap-select.min.js"></script>
    <script src="css/js.cookie.min.js"></script>


    <div class="loading-bg">
        <div class="center">
            <h3>Y&#xFC;kleniyor</h3>
            <div class="spinner-border" role="status"></div>
            <div class="loading-img"><img src="img/loading-img.svg" alt="yükleniyor" /> </div>
        </div>
    </div>

    <!-- Memnuniyet Anketi Modal -->

    

    <div id="voltiOpenId">
        



    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.2/css/all.min.css" rel="stylesheet" />

<div class="flex-col-c-sb size1 overlay1">
    <button class="chatbox-open chatbot chatbot-baskent"
            id="chatbox-open"
            onclick="setTimeout(down, 100);clickFunction();"
            style="display: none"
            type="button">
    </button>
    <section class="chatbox-popup" id="chatbox-popup" style="display: none;position:fixed;z-index: 25;">
        <header class="chatbox-popup__header">

            <aside style="flex: 9" aria-label="volti">
                <h3 id="volti" class="pl-3"
                    style="color: black; font-style: italic; cursor: pointer">
                    ChatVolt
                </h3>
            </aside>
            <aside style="flex: 1" aria-label="kapat">
                <button class="chatbox-maximize" id="chatbox-maximize" type="button">
                    <img src="img/close.svg" alt="kapat" />
                </button>
            </aside>
            <aside id="disconnect" style="flex: 1; display: none" aria-label="kapat">
                <div>
                    <button onclick="disconnect()" style="color: black" type="button">X</button>
                </div>
            </aside>
        </header>
        <main class="chatbox-popup__main"
              id="messageBody"
              style="
        overflow: auto;
        max-height: 450px;
        height: 450px;
        font-size: 12px;
      "></main>
        <img id="writing" style="
        margin-bottom: 0px;
        font-family: monospace;
        margin-left: 15px;
        width: 50px;
        margin-top: -30px;
        position: absolute;
        display: none;
        bottom: 75px;
      " src="img/tenor.gif" alt="yazıyor" />
        <footer class="chatbox-popup__footer">
            <aside style="flex: 10" aria-label="input">
                <input type="text"
                       id="userBox"
                       placeholder="Aa"
                       ng-trim="false"
                       autofocus=""
                       onkeydown="if (event.keyCode == 13) {myFunction()}"></input>
            </aside>
            <aside style="flex: 1; color: #888; text-align: center" aria-label="gönder"
                   id="sendbutton">
                <img src="img/button.webp" style="width: 100%; margin-left: 10px" alt="gönder" />
            </aside>
        </footer>
    </section>
</div>



<script src="css/socket.io.js" defer></script>
<script src="css/saferInnerHTML.js" defer></script>

<script>

    var zackaiRatingScore = 0;
    var numara = 0;
    function divcounterFunction() {
        window.numara = window.numara + 1;
        return window.numara;

    }

    function randomDivId() {
        return ([1e7] + -1e3 + -4e3 + -8e3 + -1e11).replace(/[018]/g, c =>
            (c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> c / 4).toString(16)
        );
    }
</script>
<script>
    function chatbotRequest(usertext) {

        var xhttp = new XMLHttpRequest();
        xhttp.onreadystatechange = function () {
            if (this.readyState == 4 && this.status == 200) {
                var responseJson = JSON.parse(this.responseText);
                var chatbotResponse = responseJson.text;

                if ("isAgent" in responseJson.context) {
                    if (responseJson.context.isAgent === true) {
                        window.isAgentConnect = true;
                        if (window.socketon == false) {
                            window.isAgentConnect = responseJson.context.isAgentConnect;
                            socketOn();
                        }
                    }
                }
                var newNode = document.createElement("div");
                newNode.className = "d-flex justify-content-start mb-4";
                var botOnlineTime = dateTime();
                var chatbotResponseAnswer = chatbotResponse.join(" ");
                chatbotResponseAnswer = filter(chatbotResponseAnswer);
                saferInnerHTML(newNode,
                    '<div class="msg_cotainer">' +
                    '<div class="msg_cotainer_pre">' +
                    chatbotResponseAnswer +
                    "</pre>" +
                    '<span class="msg_time">' +
                    botOnlineTime +
                    "</span>" +
                    "</div>");
                var writingNone = document.getElementById("writing");
                writingNone.style.display = "none";
                document.getElementById("messageBody").appendChild(newNode);
                var objDiv = document.getElementById("messageBody");
                historySpeech({
                    userId: "0",
                    text: chatbotResponseAnswer,
                    time: botOnlineTime,
                });
                if ("isSurvey" in responseJson.context) {
                    if (responseJson.context.isSurvey === true) {
                        var anketStatus = 0;

                        var sessionName = localStorage.getItem("chatbotSurveyDate");
                        if (sessionName === "" || sessionName === null) {
                            localStorage.setItem("chatbotSurveyDate", new Date());
                            anketStatus = 0;
                        } else {
                            const date1 = new Date();
                            const date2 = new Date(sessionName);
                            const diffTime = Math.abs(date2 - date1);
                            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                            if (diffDays > 7) {
                                localStorage.setItem("chatbotSurveyDate", new Date());
                                anketStatus = 0;
                            } else {
                                anketStatus = 1;
                            }
                        }
                        if (anketStatus === 0) {
                            setTimeout(() => survey(), 10000);
                        }
                    }
                }

                function survey() { // bu fonksiyonu copy paste ile değiştirmeniz yeterlidir
                    var ratingNode = document.createElement("div");
                    ratingNode.className = "feedback_x";
                    ratingNode.id = "ratings_x";
                    ratingNode.innerHTML =
                      '<div class="feedback" id="ratings">'+
                        '<p style="padding: 0px; margin: 0px;">ChatVolt üzerinden aldığınız hizmetten memnun kaldınız mı?</p>'+
                        '<div class="rating">'+
                          '<div class="emoji-wrapper">'+
                            '<div class="zackai-emoji">'+
                                '<ul class="zackai-survey">'+
                                    '<li>'+
                                        '<a title="1"><img data-img="img/survey1.svg" data-imghover="img/survey1-hover.svg" class="img-1" data-toggle="tooltip" data-placement="bottom" title="" src="img/survey1.svg" alt="çok kötü" data-original-title="Çok Kötü"></a>'+
                                    '</li>'+
                                    '<li>'+
                                        '<a title="2" class=""><img data-img="img/survey2.svg" data-imghover="img/survey2-hover.svg" class="img-2" data-toggle="tooltip" data-placement="bottom" title="" src="img/survey2.svg" alt="kötü" data-original-title="Kötü"></a>'+
                                    '</li>'+
                                    '<li>'+
                                        '<a title="3" class=""><img data-img="img/survey3.svg" data-imghover="img/survey3-hover.svg" class="img-3" data-toggle="tooltip" data-placement="bottom" title="" src="img/survey3.svg" alt="orta" data-original-title="Orta"></a>'+
                                    '</li>'+
                                    '<li>'+
                                        '<a title="4" class=""><img data-img="img/survey4.svg" data-imghover="img/survey4-hover.svg" class="img-4" data-toggle="tooltip" data-placement="bottom" title="" src="img/survey4.svg" alt="iyi" data-original-title="İyi"></a>'+
                                    '</li>'+
                                    '<li>'+
                                        '<a title="5" class=""><img data-img="img/survey5.svg" data-imghover="img/survey5-hover.svg" class="img-5" data-toggle="tooltip" data-placement="bottom" title="" src="img/survey5.svg" alt="çok iyi" data-original-title="Çok İyi"></a>'+
                                    '</li>'+
                                '</ul>'+
                            '</div>'+
                          '</div>'+
                        '</div>'+
                        '<hr width="100%" style="background-color:black;" size="4">'+
                        '<p style="text-align: center;"> Gelecek işlemlerinizde ChatVolt üzerinden tekrar hizmet almayı düşünür müsünüz? </p>'+
                        '<form class="buttons">'+
                          '<div class="rd1">'+
                            '<input id="radio-1" class="radio-custom" name="radio-group" type="radio" checked>'+
                            '<label for="radio-1" class="radio-custom-label">Evet</label>'+
                          '</div>'+
                          '<div class="rd2">'+
                            '<input id="radio-2" class="radio-custom" name="radio-group" type="radio">'+
                            '<label for="radio-2" class="radio-custom-label">Hayır</label>'+
                          '</div>'+
                        '</form>'+
                        '<button type="button" id="sendrating" class="survey-btn">Anketi Gönder</button>'+
                      '</div>';

                    document.getElementById("messageBody").appendChild(ratingNode);
                    var objDiv = document.getElementById("messageBody");
                    objDiv.scrollTop = objDiv.scrollHeight;
                    document.getElementById("sendrating").addEventListener("click", function (e) { starControl(); });
                    $('.zackai-survey li a').click(function(){
                        $(this).closest('.zackai-survey').find('a').removeClass('active');

                         $(this).closest('.zackai-survey').find('a img').map(function() {
                            $(this).attr('src', $(this).data('img'));
                        })
                        $(this).addClass('active');
                         $(this).find('img').attr('src', $(this).find('img').data('imghover'));
                        $(this).closest('.item').find('.next-button').removeClass('disabled');

                        zackaiRatingScore = this.getAttribute("title")
                    });
                }
                objDiv.scrollTop = objDiv.scrollHeight;

            }
            if (this.readyState == 4 && this.status != 200) {
                var botOfflineTime = dateTime();
                var newNode = document.createElement("div");
                newNode.className = "d-flex justify-content-start mb-4";
                var offline = "Şu anda hizmet veremiyorum.";
                saferInnerHTML(newNode,
                    '<div class="msg_cotainer">' +
                    '<div class="msg_cotainer_pre">' +
                    offline +
                    "</pre>" +
                    '<span class="msg_time">' +
                    botOfflineTime +
                    "</span>" +
                    "</div>");
                var writingNone = document.getElementById("writing");
                writingNone.style.display = "none";
                document.getElementById("messageBody").appendChild(newNode);
                var objDiv = document.getElementById("messageBody");
                objDiv.scrollTop = objDiv.scrollHeight;
                historySpeech({ userId: "0", text: offline, time: botOfflineTime });

            }
        };
        xhttp.open("POST", "https://chatbot-service.eedas.com.tr/", true);
        xhttp.setRequestHeader(
            "Content-Type",
            "application/json; charset=UTF-8"
        );
        var data = {
            text: usertext,
            chatId: window.chatId,
            region: "baskent",
        };
        xhttp.send(JSON.stringify(data));
    }
    function myFunction() {
        var divcounter = divcounterFunction()
        var id = "div_id" + divcounter;
        var textinbox = document.getElementById("userBox").value;
        textinbox = filter(textinbox);
        var writing = document.getElementById("writing");


        if (textinbox != "") {
        }
        if (textinbox.trim() == "") {

        } else {
            writing.style.display = "";
            document.getElementById("userBox").value = "";
            textinbox = textinbox.trim();
            var userMessageBaloon = document.createElement("div");
            userMessageBaloon.className = "d-flex justify-content-end mb-4";
            var userTime = dateTime();
            saferInnerHTML(userMessageBaloon,
                '<div class="msg_cotainer_send">' +
                '<div class="costumer">' +
                textinbox +
                "</pre>" +
                '<span class="msg_time_send">' +
                userTime +
                "</span>" +
                "</div>");

            document.getElementById("messageBody").appendChild(userMessageBaloon);

            var objDiv = document.getElementById("messageBody");
            divcounter = divcounter + 1;
            var objDiv = document.getElementById("messageBody");

            objDiv.scrollTop = objDiv.scrollHeight;
            document.getElementById("userBox").value = "";
            console.log(window.socketConnectOn)
            if (window.socketConnectOn == "off") {
                var message = chatbotRequest(textinbox);
                historySpeech({ userId: "1", text: textinbox, time: userTime });
            }
            else {
                if (window.socketon == false) {
                    socketOn();
                }

                historySpeech({ userId: "1", text: textinbox, time: userTime });

                socket.emit("send_message", {
                    username: "Customer",
                    room: window.roomId,
                    message: textinbox,
                    type: "customer",
                    user_id: window.chatId,
                });
            }
        }
    }
</script>
<script>
    {
        var btnSend = document.getElementById("sendbutton");
        btnSend.onclick = function () {
            myFunction();
        };
    }
</script>
<script>
    function hideRating() {
        const myNode = document.getElementById("ratings_x");
        while (myNode.firstChild) {
            myNode.removeChild(myNode.lastChild);
        }
        var ratings_x = document.getElementById("ratings_x");
        ratings_x.remove();

    }
    function starControl() {
        if (zackaiRatingScore > 0) {
            var ratingScore = zackaiRatingScore;
        }
        else {
            alert("lütfen bir seçim yapınız");
            return
        }

        if (document.getElementById("radio-1").checked) {
            var survey2 = "Evet"
        } else { var survey2 = "Hayır" }
        hideRating();
        var d = new Date();
        year = d.getFullYear();
        month = d.getMonth() + 1;
        day = d.getDate();
        hour = d.getHours();
        min = d.getMinutes();
        sec = d.getSeconds();
        var date = day + "-" + month + "-" + year;
        var hourAndMinute = dateTime();
        hourAndMinute = hourAndMinute + ":" + sec;
        var fulltime = date + " " + hourAndMinute;
        var anketData = {
            datetime: fulltime,
            rating: ratingScore,
            region: "baskent",
            chatId: window.chatId,
            rating2: survey2
        };
        var xhttp1 = new XMLHttpRequest();
        xhttp1.onreadystatechange = function () {
            if (this.readyState == 4 && this.status == 200) {
                var anketTime = hour + ":" + min;

                var ratingBaloon = document.createElement("div");
                ratingBaloon.className = "d-flex justify-content-start mb-4";
                saferInnerHTML(ratingBaloon,
                    '<div class="msg_cotainer">' +
                    '<div class="msg_cotainer_pre">' +
                    "Değerli müşterimiz, görüşlerinizi bizimle paylaştığınız için teşekkür ederiz." +
                    "</pre>" +
                    '<span class="msg_time">' +
                    anketTime +
                    "</span>" +
                    "</div>");
                document.getElementById("messageBody").appendChild(ratingBaloon);
                var objDiv = document.getElementById("messageBody");
                historySpeech({
                    userId: "0",
                    text:
                        "Değerli müşterimiz, görüşlerinizi bizimle paylaştığınız için teşekkür ederiz.",
                    time: anketTime,
                });
                objDiv.scrollTop = objDiv.scrollHeight;
            }
        };
        xhttp1.open("POST", "https://chatbot-service.eedas.com.tr/" + "survey", true);
        xhttp1.setRequestHeader(
            "Content-Type",
            "application/json; charset=UTF-8"
        );
        xhttp1.send(JSON.stringify(anketData));
    }
</script>
<script>
    function showOrHide() {
        var melement = document.getElementById("chatbox-popup");

        if (melement.style.display == "none") {
            //const chatbox = jQuery.noConflict();
            $(".chatbox-open");
            $(".chatbox-popup, .chatbox-close").css("display", "flex").hide().fadeIn();
        } else {
            //const chatbox = jQuery.noConflict();
            $(".chatbox-maximize");
            $(".chatbox-popup, .chatbox-close").fadeOut();
        }
    }
</script>
<script type="text/javascript">
    document
        .getElementById("chatbox-open")
        .addEventListener("click", function (e) {
            showOrHide();
        });
    document
        .getElementById("chatbox-maximize")
        .addEventListener("click", function (e) {
            showOrHide();
        });
    document
        .getElementById("volti")
        .addEventListener("click", function (e) {
            showOrHide();
        });
</script>
<script>
    function msieversion() {
        var ua = window.navigator.userAgent;
        var msie = ua.indexOf("MSIE ");
        if (msie > 0 || !!navigator.userAgent.match(/Trident.*rv\*rv\:11\./)) {
            var button = document.getElementById("chatbox-open");
            button.style.display = "none";
            return true;
        } else {
        }
        return false;
    }
    var eversion = msieversion();
    if (eversion == false) {
        var chatbox = document.getElementById("chatbox-open");
        chatbox.style.display = "";
    }
</script>
<script>
    function historySpeech(data) {
        var chatHistory = [];
        var sessionControl = JSON.parse(localStorage.getItem("chatSpeech"));

        if (sessionControl === null) {
        } else {
            for (var i = 0; i < sessionControl.chatSpeechList.length; i++) {
                var stepHistory = {
                    userId: sessionControl.chatSpeechList[i].userId,
                    text: sessionControl.chatSpeechList[i].text,
                    time: sessionControl.chatSpeechList[i].time,
                };
                chatHistory.push(stepHistory);
            }
        }
        chatHistory.push(data);
        localStorage.setItem(
            "chatSpeech",
            JSON.stringify({ chatSpeechList: chatHistory })
        );
    }
</script>

<script>
    function createUUID() {
        return ([1e7] + -1e3 + -4e3 + -8e3 + -1e11).replace(/[018]/g, c =>
            (c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> c / 4).toString(16)
        );
    }

    window.onload = function () {
        var detect = msieversion();

        var historyBot = document.createElement("div");
        var defaultTime = dateTime();
        var defaultText = "Merhaba, ben ChatVolt! Size yardımcı olmaya başlamadan önce Kişisel Verilerin Korunması Kanunu uyarınca sizi bilgilendirmem gerekiyor. Chatbot uygulamasına ilişkin aydınlatma metnine aşağıdaki linkten erişebilirsiniz. Unutmayın! Aydınlatma metni içerisinde yer alan iletişim kanalları, yalnızca kişisel verinizle ilgili haklarınızı kullanabilmeniz içindir! " +
            "<a href = 'https://www.baskentedas.com.tr/sayfa/chatbot-uygulamasina-iliskin-aydinlatma-metni' >" + 'https://www.baskentedas.com.tr/sayfa/chatbot-uygulamasina-iliskin-aydinlatma-metni' + "</a>" + " Diğer tüm şikayet, başvuru ve önerileriniz  için " + "<a href = 'https://www.baskentedas.com.tr' >" + 'https://www.baskentedas.com.tr' + "</a>" + " internet sitemizde de yer alan iletişim kanalları üzerinden bizimle irtibata geçebilirsiniz.";

        historyBot.className = "d-flex justify-content-start mb-4";
        saferInnerHTML(historyBot,
            '<div class="msg_cotainer">' +
            '<div class="msg_cotainer_pre">' +
            defaultText +
            "</pre>" +
            '<span class="msg_time">' +
            defaultTime +
            "</span>" +
            "</div>");
        document.getElementById("messageBody").appendChild(historyBot);

        var objDiv1 = document.getElementById("messageBody");
        objDiv1.scrollTop = objDiv1.scrollHeight;

        var chatSpeechHistoryDateTime = localStorage.getItem(
            "chatSpeechHistoryDateTime"
        );
        if (chatSpeechHistoryDateTime === null) {
            var sessionName = localStorage.setItem(
                "chatSpeechHistoryDateTime",
                new Date()
            );
            localStorage.removeItem("chatSpeech");
        } else {
            const date1 = new Date();
            const date2 = new Date(chatSpeechHistoryDateTime);
            const diffTime = Math.abs(date2 - date1);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            if (diffTime > 600000) {
                localStorage.setItem("chatSpeechHistoryDateTime", new Date());
                localStorage.removeItem("chatSpeech");
                localStorage.removeItem("chatIdLocalStorage");
            }
        }

        var chatIdLocalStorage = localStorage.getItem("chatIdLocalStorage");

        if (chatIdLocalStorage === null) {
            var chatId = createUUID();
            window.chatId = chatId;
            localStorage.setItem("chatIdLocalStorage", window.chatId);
        } else {
            window.chatId = chatIdLocalStorage;
        }
        var sessionControl = JSON.parse(localStorage.getItem("chatSpeech"));

        if (sessionControl === null) {

        } else {
            for (var i = 0; i < sessionControl.chatSpeechList.length; i++) {
                if (sessionControl.chatSpeechList[i].userId === "0") {
                    var historyBot = document.createElement("div");
                    historyBot.className = "d-flex justify-content-start mb-4";
                    saferInnerHTML(historyBot,
                        '<div class="msg_cotainer">' +
                        '<div class="msg_cotainer_pre">' +
                        sessionControl.chatSpeechList[i].text +
                        "</pre>" +
                        '<span class="msg_time">' +
                        sessionControl.chatSpeechList[i].time +
                        "</span>" +
                        "</div>");
                    document.getElementById("messageBody").appendChild(historyBot);
                    var objDiv1 = document.getElementById("messageBody");
                    objDiv1.scrollTop = objDiv1.scrollHeight;
                } else if (sessionControl.chatSpeechList[i].userId === "1") {
                    var id1 = "divnum" + randomDivId();
                    var historyUser = document.createElement("div");
                    historyUser.className = "d-flex justify-content-end mb-4";
                    saferInnerHTML(historyUser,
                        '<div class="msg_cotainer_send">' +
                        '<div class="costumer">' +
                        sessionControl.chatSpeechList[i].text +
                        "</pre>" +
                        '<span class="msg_time_send">' +
                        sessionControl.chatSpeechList[i].time +
                        "</span>" +
                        "</div>");
                    document.getElementById("messageBody").appendChild(historyUser);
                    var objDiv2 = document.getElementById("messageBody");
                    objDiv2.scrollTop = objDiv2.scrollHeight;
                }
            }
        }
        down();
        var eversion = msieversion();
        if (eversion == false) {
            var chatbox = document.getElementById("chatbox-open");
            chatbox.style.display = "";
        }
    };
</script>
<script>
    function down() {
        var objDiv2 = document.getElementById("messageBody");
        objDiv2.scrollTop = objDiv2.scrollHeight;
    }
</script>
<script>
    function dateTime() {
        var botDate = new Date();
        hour = botDate.getHours();
        min = botDate.getMinutes();
        var r = parseInt(hour);
        var n = parseInt(min);
        if (n < 10) {
            min = "0" + min;
        }
        if (r < 10) {
            hour = "0" + hour;
        }
        return hour + ":" + min;
    }
</script>
<script>
    window.socketConnectOn = "off";
    window.socketon = false;
    window.leaveon = false;
</script>

<script>


    function socketOn() {

        //bir oda oluşturulup bağlanma
        window.socketConnectOn = "on";
        if (window.socketon == false) {
            window.socket = io.connect("https://chatbot-service.eedas.com.tr/");

            console.log(Array(io))
            window.socketon = true;
            window.leaveon = true;
        }

        if (window.socketConnectOn == "on") {
            if (window.isAgentConnect) {
                window.roomId = randomRoomId();

                var chatSpeech = localStorage.getItem("chatSpeech");

                var sessionControl = sessionStorage.getItem('LiveChat');
                if (sessionControl !== null) {
                    sessionControl = JSON.parse(sessionControl);
                    window.roomId = sessionControl['room'];
                }
                else {
                    var sessionData = { "room": window.roomId, "chatId": window.chatId }
                    sessionStorage.setItem("LiveChat", JSON.stringify(sessionData));
                }

                window.socket.on("connect", function () {
                    window.socket.emit("join_room", {
                        username: "Customer",
                        room: window.roomId,
                        type: "customer",
                        user_id: window.chatId,
                        region: "baskent",
                        history: chatSpeech,
                    });
                });


                window.socket.on("receive_message", function (data) {
                    if ("type" in data) {
                        if (data.type == "agent" && data.room == window.roomId) {
                            const newNode = document.createElement("div");
                            var agentTime = dateTime();
                            newNode.className = "d-flex justify-content-start mb-4";
                            newNode.innerHTML =
                                '<div class="msg_cotainer">' +
                                '<div class="msg_cotainer_pre">' +
                                data.message +
                                "</pre>" +
                                '<span class="msg_time">' +
                                agentTime +
                                "</span>" +
                                "</div>";

                            historySpeech({ userId: "0", text: data.message, time: agentTime });
                            var writingNone = document.getElementById("writing");
                            writingNone.style.display = "none";
                            document.getElementById("messageBody").appendChild(newNode);
                            var objDiv = document.getElementById("messageBody");
                            objDiv.scrollTop = objDiv.scrollHeight;
                        }
                    }
                });

                window.socket.on("join_room_announcement", function (data) {
                    window.socketId = window.socket.id

                    if ("type" in data) {
                        if (data.type != "customer" && data.room === window.roomId) {
                            const newNode = document.createElement("div");
                            var agentTime = dateTime();
                            var agentJoin =
                                "<b>" + data.username + "</b>" + " konuşmaya katıldı.";
                            newNode.className = "d-flex justify-content-start mb-4";
                            newNode.innerHTML =
                                '<div class="msg_cotainer">' +
                                '<div class="msg_cotainer_pre">' +
                                agentJoin +
                                "</pre>" +
                                '<span class="msg_time">' +
                                agentTime +
                                "</span>" +
                                "</div>";
                            var writingNone = document.getElementById("writing");
                            writingNone.style.display = "none";
                            document.getElementById("messageBody").appendChild(newNode);
                            var objDiv = document.getElementById("messageBody");
                            objDiv.scrollTop = objDiv.scrollHeight;
                        }
                    }
                });

                window.isAgentConnect = false;
                //Eğer bağlandıysa disconnect buttonunu görünür yap
                var disconnectButton = document.getElementById("disconnect");
                disconnectButton.style.display = "";
            }
            var before_user = { type: "agent" };

            window.socket.on("leave_room_announcement", function (data) {
                window.sessionStorage.removeItem('LiveChat');
                if ("type" in data) {
                    if (data.type === "agent" && window.isDisClick === false && data.room === window.roomId) {
                        const newNode = document.createElement("div");
                        var agentTime = dateTime();
                        var exit = "Müşteri hizmetleri konuşmayı sonlandırmıştır.";
                        console.log(exit);
                        newNode.className = "d-flex justify-content-start mb-4";
                        var offline = "Şu anda hizmet veremiyorum.";
                        newNode.innerHTML =
                            '<div class="msg_cotainer">' +
                            '<div class="msg_cotainer_pre">' +
                            exit +
                            "</pre>" +
                            '<span class="msg_time">' +
                            agentTime +
                            "</span>" +
                            "</div>";
                        var writingNone = document.getElementById("writing");
                        writingNone.style.display = "none";

                        if (true) {
                            document.getElementById("messageBody").appendChild(newNode);
                            var objDiv = document.getElementById("messageBody");
                            objDiv.scrollTop = objDiv.scrollHeight;
                            var disconnectButton = document.getElementById("disconnect");
                            window.leaveon = false;
                        }

                        disconnectButton.style.display = "none";

                        /*window.socket.emit("leave_room", {
                          username: "System",
                          room: window.roomId,
                          type: "system",
                          user_id: window.chatId,
                        });*/

                        window.socket.disconnect();
                        window.socketon = false;
                        before_user.type = "customer";
                        window.socketConnectOn = "off";
                    }
                }
            });
        }
        window.onbeforeunload = function () {
            /* window.socket.emit("leave_room", {
               username: "customer",
               room: window.roomId,
               type: "customer",
               user_id: window.chatId,});
               window.socket.emit('client_disconnecting',{
                   username: "Customer",
                   room: window.roomId,
                   type: "customer",
                   user_id: window.chatId,
                   region: "ayedas"}); */
            if (window.socketon) {
                var xhttp = new XMLHttpRequest();
                xhttp.open("POST", "https://chatbot-service.eedas.com.tr/" + "remove_room", true);
                xhttp.setRequestHeader("Content-Type", "application/json; charset=UTF-8");
                var data = { room_id: window.roomId };
                xhttp.send(JSON.stringify(data));
                //socket.disconnect();
            }



        };

    }
</script>
<script>
    function randomRoomId() {
        var mathRandom = Math.floor(Math.random() * 72345);
        var result = window.chatId;
        return result + mathRandom;
    }
</script>
<script>
    window.isDisClick = false;

    function disconnect() {
        console.log("disconnecte girdi")
        window.leaveon = false;
        window.socket.removeAllListeners();
        sessionStorage.removeItem('LiveChat');
        var disconnectButton = document.getElementById("disconnect");
        disconnectButton.style.display = "none";
        //var socket = io.connect("https://chatbot-service-test.enerjisa.com.tr/",{secure:true});
        window.socket.emit("leave_room", {
            username: "customer",
            room: window.roomId,
            type: "customer",
            user_id: window.chatId
        });
        window.socket.disconnect();
        window.socketon = false;
        window.isDisClick = true;


        const newNode = document.createElement("div");
        var agentTime = dateTime();
        var exit = "Müşteri temsilcisiyle yapılan görüşme sonlandırılmıştır. Sizi ChatVolt'a yönlendiriyorum.";
        newNode.className = "d-flex justify-content-start mb-4";
        newNode.innerHTML =
            '<div class="msg_cotainer">' +
            '<div class="msg_cotainer_pre">' +
            exit +
            "</pre>" +
            '<span class="msg_time">' +
            agentTime +
            "</span>" +
            "</div>";
        var writingNone = document.getElementById("writing");
        writingNone.style.display = "none";
        document.getElementById("messageBody").appendChild(newNode);
        var objDiv = document.getElementById("messageBody");
        objDiv.scrollTop = objDiv.scrollHeight;
        var xhttp = new XMLHttpRequest();
        xhttp.open("POST", "https://chatbot-service.eedas.com.tr/" + "remove-room", true);
        xhttp.setRequestHeader(
            "Content-Type",
            "application/json; charset=UTF-8"
        );
        var data = {

            room_id: window.roomId

        };
        xhttp.send(JSON.stringify(data));

        window.socketConnectOn = "off";
    }
</script>
<script>
    var clickcounter = false;

    function clickFunction() {
        if (clickcounter == false) {
            var xhttp = new XMLHttpRequest();
            xhttp.onreadystatechange = function () {
                if (this.readyState == 4 && this.status == 200) {
                    var responseJson = JSON.parse(this.responseText);
                    window.clickcounter = true;
                }
            };
            xhttp.open(
                "POST",
                "https://chatbot-service.eedas.com.tr/" + "popup-click",
                true
            );
            xhttp.setRequestHeader("Content-Type", "application/json; charset=UTF-8");
            var data = { "region": "baskent" };
            xhttp.send(JSON.stringify(data));
        }

    }
</script>
<script>
    function filter(text) {
        var newtext = text;
        newtext = newtext.replace(/</g, '');
        newtext = newtext.replace(/>/g, '');
        newtext = newtext.replace(/‘/g, '');
        newtext = newtext.replace(/”/g, '');
        newtext = newtext.replace(/script/g, '');
        newtext = newtext.replace(/SCRIPT/g, '');

        return newtext;

    }


    function sessionControl() {
        var ses = sessionStorage.getItem('LiveChat');
        var ctid = localStorage.getItem("chatIdLocalStorage");
        if (ctid !== null) {
            window.chatId = ctid;
        }

        if (ses !== null) {
            window.socketConnectOn = "on";
            window.isAgentConnect = true;
            document.getElementById('chatbox-open').click();
            socketOn();
        }
    }
    sessionControl();
</script>
    </div>


    




<script>
    var anketSorgulaFormCaptchaWidget;
    var CaptchaCallback = function () {
        anketSorgulaFormCaptchaWidget = grecaptcha.render('captcha_memnuniyetAnketForm');
    };

</script>

<button type="button" id="anketButonId" class="d-none" data-toggle="modal" data-target="#modal-memnuniyet-anketi">
    Memnuniyet Anketi
</button>
<div class="modal fade" id="modal-memnuniyet-anketi" tabindex="-1" aria-labelledby="modalLabel" aria-hidden="true" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalLabel">Memnuniyet Anketi</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <img src="img/close.svg" width="20" alt="kapat" />
                </button>
            </div>
            <div class="modal-body">
                <ul class="step-bar">
                    <li class="active"><a data-step="1"></a></li>
                    <li><a data-step="2"></a></li>
                    <li><a data-step="3"></a></li>
                    <li><a data-step="4"></a></li>
                </ul>
                <div class="step-content">
                    <div class="item first" style="display:block">

                        <div class="row">
                            <div class="col-lg-12">
                                <h4>Web sitemizi genel olarak nas&#x131;l de&#x11F;erlendirirsiniz?</h4>
                                <ul class="survey">
                                    <li><a title="1"><img data-img="img/survey1.svg" data-imghover="img/survey1-hover.svg" class="img-1" data-toggle="tooltip" data-placement="bottom" title="Çok Kötü" src="img/survey1.svg" alt="çok kötü" /></a></li>
                                    <li><a title="2"><img data-img="img/survey2.svg" data-imghover="img/survey2-hover.svg" class="img-2" data-toggle="tooltip" data-placement="bottom" title="Kötü" src="img/survey2.svg" alt="kötü" /></a></li>
                                    <li><a title="3"><img data-img="img/survey3.svg" data-imghover="img/survey3-hover.svg" class="img-3" data-toggle="tooltip" data-placement="bottom" title="Orta" src="img/survey3.svg" alt="orta" /></a></li>
                                    <li><a title="4"><img data-img="img/survey4.svg" data-imghover="img/survey4-hover.svg" class="img-4" data-toggle="tooltip" data-placement="bottom" title="İyi" src="img/survey4.svg" alt="iyi" /></a></li>
                                    <li><a title="5"><img data-img="img/survey5.svg" data-imghover="img/survey5-hover.svg" class="img-5" data-toggle="tooltip" data-placement="bottom" title="Çok İyi" src="img/survey5.svg" alt="çok iyi" /></a></li>
                                </ul>
                            </div>
                        </div>


                        <div class="row">
                            <div class="col-lg-12" style="justify-content: center;display: flex;">
                                <a class="next-button button--default disabled" data-step="1">&#x130;leri</a>
                            </div>
                        </div>
                    </div>
                    <div class="item second">
                        <div class="row">
                            <div class="col-lg-12">
                                <h4>Arad&#x131;&#x11F;&#x131;n&#x131;z bilgiye ula&#x15F;abildiniz mi?</h4>
                                <ul class="survey" id="yeterliBilgi">
                                    <li>
                                        <a title="false">
                                            <img data-img="img/survey2.svg" data-imghover="img/survey2-hover.svg" class="img-2" src="img/survey2.svg" alt="hayır" />
                                            <span>
                                                Hay&#x131;r
                                            </span>
                                        </a>
                                    </li>
                                    <li>
                                        <a title="true">
                                            <img data-img="img/survey4.svg" data-imghover="img/survey4-hover.svg" class="img-4" src="img/survey4.svg" alt="evet" />
                                            <span>
                                                Evet
                                            </span>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-lg-12" style="justify-content: center;display: flex;">
                                <a class="next-button button--default disabled" data-step="2">&#x130;leri</a>
                            </div>
                        </div>
                    </div>
                    <div class="item third">
                        <h4 class="mb-20">Web sitemizi hangi i&#x15F;lemler i&#xE7;in kulland&#x131;n&#x131;z?</h4>
                        <div class="form-row">
                            <div class="col-lg-6">
                                <label class="input-checkbox">
                                    Kesinti Bilgisi
                                    <input type="checkbox" name="Kesinti" id="kesinti">
                                    <span class="checkmark"></span>
                                </label>
                            </div>
                            <div class="col-lg-6">
                                <label class="input-checkbox">
                                    Yasal Bildirimler
                                    <input type="checkbox" name="YasalBildirimler" id="yasalbildirimler">
                                    <span class="checkmark"></span>
                                </label>
                            </div>
                            <div class="col-lg-6">
                                <label class="input-checkbox">
                                    Talep B&#x131;rakma
                                    <input type="checkbox" name="TalepBirakma" id="talepbirakma">
                                    <span class="checkmark"></span>
                                </label>
                            </div>
                            <div class="col-lg-6">
                                <label class="input-checkbox">
                                    Duyurular
                                    <input type="checkbox" name="Duyurular" id="duyurular">
                                    <span class="checkmark"></span>
                                </label>
                            </div>
                            <div class="col-lg-6">
                                <label class="input-checkbox">
                                    Yetkili Elektrik&#xE7;i
                                    <input type="checkbox" name="YetkiliElektrikci" id="yetkilielektrikci">
                                    <span class="checkmark"></span>
                                </label>
                            </div>
                            <div class="col-lg-6">
                                <label class="input-checkbox">
                                    Di&#x11F;er
                                    <input type="checkbox" name="Diger" id="checkbox-diger">
                                    <span class="checkmark"></span>
                                </label>
                            </div>
                            <div class="col-lg-11 textarea-diger" style="display:none">
                                <div class="form-row-textarea w-100 ml-0">
                                    <span class="charCount char_count_js">0/150</span>
                                    <textarea class="form-control input-textarea" id="digerAciklama" name="digerAciklama" placeholder="Di&#x11F;er" maxlength="150"></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-12 mt-20" style="justify-content: center;display: flex;">
                                <a class="next-button button--default disabled" data-step="3">&#x130;leri</a>
                            </div>
                        </div>
                    </div>
                    <div class="item fourth">
                        <h4 class="mb-20">
                            G&#xF6;r&#xFC;&#x15F; ve &#xF6;nerilerinizi bizimle payla&#x15F;&#x131;r m&#x131;s&#x131;n&#x131;z?<span style="font-weight: 500">
                                (&#x130;ste&#x11F;e ba&#x11F;l&#x131;)
                            </span>
                        </h4>
                        <div class="form-row-textarea">
                            <span class="charCount char_count_js">0/500</span>
                            <textarea class="form-control input-textarea" name="Aciklama" id="Aciklama" placeholder="A&#xE7;&#x131;klama Giriniz" maxlength="500"></textarea>
                        </div>
                        <div class="row">
                            <div class="col-lg-12 mt-20" style="justify-content: center;display: flex;">
                                <div class="memnuniyetAnketForm" id="captcha_memnuniyetAnketForm" data-sitekey="6LcCIEYUAAAAAMkgU8fDrYCro2bZ84M2U_dKzUIi"></div>

                                <a class="button--default send_survey" id="submit">G&#xF6;nder</a>
                            </div>
                            <div id="captchaErrorMesajDiv"  style="display:none"  class="field-validation-error text-danger mb-10 mt-10 col-lg-12">

                            </div>
                        </div>
                        <div class="row">
                            
                        </div>
                    </div>
                </div>
                <div class="survey-success">
                    <div class="image">
                        <img src="img/survey_success.svg" alt="başarılı" />
                    </div>
                    <h4>De&#x11F;erli m&#xFC;&#x15F;terimiz, g&#xF6;r&#xFC;&#x15F;lerinizi bizimle payla&#x15F;t&#x131;&#x11F;&#x131;n&#x131;z i&#xE7;in te&#x15F;ekk&#xFC;r ederiz!</h4>
                </div>
                <div class="survey-info">
                    <div class="image">
                        <img src="img/survey_info.svg" alt="anket bilgi simgesi" />
                    </div>
                    <h4>
                        Daha &#xF6;nce anketi cevaplad&#x131;&#x11F;&#x131;n&#x131;z i&#xE7;in tekrar kat&#x131;l&#x131;m&#x131;n&#x131;z&#x131; ger&#xE7;ekle&#x15F;tiremiyoruz.
                    </h4>
                </div>
            </div>
        </div>
    </div>
</div>

<script>

    $('.send_survey').click(function () {

    });

    $(document).on("click", ".daha_once_ankete_katildiniz", function () {
        $('#modal-memnuniyet-anketi .step-content, #modal-memnuniyet-anketi .step-bar, #modal-memnuniyet-anketi .modal-header h5').hide();
        $('#modal-memnuniyet-anketi .survey-info').show();
    });

    $('#anketButonId').click(function () {
        var head = document.getElementsByTagName('head')[0];
        var script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = 'https://www.google.com/recaptcha/api.js?hl=tr&onload=CaptchaCallback&render=explicit'
        head.appendChild(script);
    });

    $(document).ready(function () {
        var anket = Cookies.get("anket" + '1000' , { domain: '.baskentedas.com.tr' });
        if (anket) {
            document.getElementById("anketButonId").className = "btn-memnuniyet-anketi daha_once_ankete_katildiniz";
        } else {
            document.getElementById("anketButonId").className = "btn-memnuniyet-anketi";
        }
    });

    $('#checkbox-diger').change(function () {
        if ($(this).is(':checked')) {
            $('.textarea-diger').show();
        } else {
            $('.textarea-diger').hide();
        }
    });

    $("#submit").click(function () {

        var nes = {
            Bilgi: $("#yeterliBilgi>li>a.active").attr("title") == "true",
            Memnuniyet: parseInt($(".survey>li>a.active").attr("title")),
            Aciklama: $("#Aciklama").val(),
            Kesinti: $("#kesinti:checked").val() === "on",
            TalepBirakma: $("#talepbirakma:checked").val() === "on",
            Duyurular: $("#duyurular:checked").val() === "on",
            YetkiliElektrikci: $("#yetkilielektrikci:checked").val() === "on",
            YasalBildirimler: $("#yasalbildirimler:checked").val() === "on",
            Diger: $("#checkbox-diger:checked").val() === "on",
            DigerAciklama: $("#digerAciklama").val(),
            Captcha: grecaptcha.getResponse(anketSorgulaFormCaptchaWidget)
        }
        $.ajax({
            method: "post",
            url: "/anket-doldur",
            contentType: "application/json",
            data: JSON.stringify(nes),
            success: function (res) {
                if (res.state == 3) {
                    $("#captchaErrorMesajDiv").show().html(res.message);
                }
                else {
                    if (res.data === true) {
                        $('#modal-memnuniyet-anketi .step-content, #modal-memnuniyet-anketi .step-bar, #modal-memnuniyet-anketi .modal-header h5').hide();
                        $('#modal-memnuniyet-anketi .survey-success').show();
                        $("#memnuniyet-anketi").modal('hide');
                        $("#anket").remove();

                            Cookies.set('anket' + '1000', '1', { expires: 1, domain: '.baskentedas.com.tr' });
                            
                    }
                }

            },
            error: function (err) {
            }
        });
        return true;
    });
</script>

    <script src="css/main.js"></script>

    <script>
    function makeLayoutSearch() {
        var val = $("#searchLayoutId").val().toLocaleLowerCase('tr-TR');

        var items = localStorage.getItem("SearchingItems");
        if (items === "" || items == null) {
            items = val + ":";
        } else {
            items += val + ":";
        }
        localStorage.setItem("SearchingItems", items);

        window.location.href = "/arama?k=" + encodeURIComponent(escape(val));
    }

    function makeLayoutMobileSearch() {
        var val = $("#searchLayoutIdMobile").val().toLocaleLowerCase('tr-TR');

        var items = localStorage.getItem("SearchingItems");
        if (items === "" || items == null) {
            items = val + ":";
        } else {
            items += val + ":";
        }
        localStorage.setItem("SearchingItems", items);

        window.location.href = "/arama?k=" + encodeURIComponent(escape(val));
    }

        $(function () {
            new Promise(function (resolve, reject) {
                resolve();
            }).then(function () {

                $("#searchLayoutId").keyup(function () {
                    if ($("#searchLayoutId").val().length >= 3) {
                        var searchingKey = $("#searchLayoutId").val().toLocaleLowerCase('tr-TR');
                        const resultStaticList = fuseStaticlist.search(searchingKey);
                        const resultSssList = fuseSssList.search(searchingKey);
                        const resultSayfaList = fuseSayfaList.search(searchingKey);
                        const resultMedyaList = fuseMedyaList.search(searchingKey);
                        const resultYasalList = fuseYasalList.search(searchingKey);

                        var sssIcerik = "";
                        var islemIcerik = "";
                        for (var i = 0; i < resultStaticList.length; i++) {
                            if (i <= 2) {
                                $("#islemSayisiContainerUstinner").show();
                                islemIcerik += "<li class=\"ml-10\"><a class=\"pl-10\" href=\"" + resultStaticList[i].item.url + "\">" + resultStaticList[i].item.name + "</a></li>";

                            }
                        }
                        if (resultStaticList.length == 0) {
                            islemIcerik += "<li class=\"ml-10\"><a class=\"pl-10\" href=\"#\">Sonu&#xE7; Bulunamad&#x131;...</a></li>";
                            $("#islemSayisiContainerUstinner").hide();
                        }
                        $("#islemIcerikUstInner").html(islemIcerik);
                        $("#islemSayisiUstInner").html(resultStaticList.length);

                        for (var i = 0; i < resultSssList.length; i++) {
                            if (i <= 2) {
                                $("#sssSayisiContainerUstinner").show();
                                sssIcerik += "<li class=\"ml-10\"><a class=\"pl-10\" href=\"/sss/" + resultSssList[i].item.seoBaslik + "\">" + resultSssList[i].item.soru + "</a></li>";
                            }
                        }
                        if (resultSssList.length == 0) {
                            sssIcerik += "<li class=\"ml-10\"><a class=\"pl-10\" href=\"#\">S&#x131;k&#xE7;a Sorulan Sorularda Sonu&#xE7; Bulunamad&#x131;</a></li>";
                            $("#sssSayisiContainerUstinner").hide();
                        }
                        $("#sssIcerikUstInner").html(sssIcerik);
                        $("#sssSayisiUstInner").html(resultSssList.length);

                        $("#sayfaSayisiUstInner").html(resultSayfaList.length);
                        $("#medyaSayisiUstInner").html(resultMedyaList.length);
                        $("#yasalSayisiUstInner").html(resultYasalList.length);

                        textBold($(this).val(), '.s-body ul li a');
                    }
                });

                $("#searchLayoutIdMobile").keyup(function () {
                    if ($("#searchLayoutIdMobile").val().length >= 3) {
                        var searchingKey = $("#searchLayoutIdMobile").val().toLocaleLowerCase('tr-TR');
                        const resultStaticList = fuseStaticlist.search(searchingKey);
                        const resultSssList = fuseSssList.search(searchingKey);
                        const resultSayfaList = fuseSayfaList.search(searchingKey);
                        const resultMedyaList = fuseMedyaList.search(searchingKey);
                        const resultYasalList = fuseYasalList.search(searchingKey);

                        var sssIcerik = "";
                        var islemIcerik = "";
                        for (var i = 0; i < resultStaticList.length; i++) {
                            if (i <= 2) {
                                $("#islemSayisiContainerUstinnerMobile").show();
                                islemIcerik += "<li class=\"ml-10\"><a class=\"pl-10\" href=\"" + resultStaticList[i].item.url + "\">" + resultStaticList[i].item.name + "</a></li>";

                            }
                        }
                        if (resultStaticList.length == 0) {
                            islemIcerik += "<li class=\"ml-10\"><a class=\"pl-10\" href=\"#\">Sonu&#xE7; Bulunamad&#x131;...</a></li>"
                            $("#islemSayisiContainerUstinnerMobile").hide();
                        }
                        $("#islemIcerikUstInnerMobile").html(islemIcerik);
                        $("#islemSayisiUstInnerMobile").html(resultStaticList.length);

                        for (var i = 0; i < resultSssList.length; i++) {
                            if (i <= 2) {
                                $("#sssSayisiContainerUstinnerMobile").show();
                                sssIcerik += "<li class=\"ml-10\"><a class=\"pl-10\" href=\"/sss/" + resultSssList[i].item.seoBaslik + "\">" + resultSssList[i].item.soru + "</a></li>"
                            }
                        }
                        if (resultSssList.length == 0) {
                            sssIcerik += "<li class=\"ml-10\"><a class=\"pl-10\" href=\"#\">S&#x131;k&#xE7;a Sorulan Sorularda Sonu&#xE7; Bulunamad&#x131;</a></li>"
                        }
                        $("#sssIcerikUstInnerMobile").html(sssIcerik);
                        $("#sssSayisiUstInnerMobile").html(resultSssList.length);

                        $("#sayfaSayisiUstInnerMobile").html(resultSayfaList.length);
                        $("#medyaSayisiUstInnerMobile").html(resultMedyaList.length);
                        $("#yasalSayisiUstInnerMobile").html(resultYasalList.length);

                        textBold($(this).val(), '.s-body ul li a');
                    }
                });
            });

            $("#islemSayisiContainerUstinner, #sssSayisiContainerUstinner, #sayfaSayisiContainerUstinner, #medyaSayisiContainerUstinner, #yasalSayisiContainerUstinner").click(function () {
                makeLayoutSearch();
            });
            $("#islemSayisiContainerUstinnerMobile, #sssSayisiContainerUstinnerMobile, #sayfaSayisiContainerUstinnerMobile, #medyaSayisiContainerUstinnerMobile, #yasalSayisiContainerUstinnerMobile").click(function () {
                makeLayoutMobileSearch();
            });

            $('#searchLayoutId').keypress(function (event) {
                var keycode = (event.keyCode ? event.keyCode : event.which);
                if (keycode == '13' || keycode == '9') {
                    makeLayoutSearch();
                }
            });

            $('#searchLayoutIdMobile').keypress(function (event) {
                var keycode = (event.keyCode ? event.keyCode : event.which);
                if (keycode == '13' || keycode == '9') {
                    makeLayoutMobileSearch();
                }
            });
        });
    </script>
    <script>
        function Loading() {
            $('.loading-bg').show();
            $('body').addClass('body-loading');
            return false;
        }
        function RemoveLoading() {
            $('.loading-bg').hide();
            $('body').removeClass('body-loading');
            return false;
        }
    </script>
</body>
</html>