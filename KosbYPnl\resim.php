<?php
include "../db/baglanti.php";
if (!isset($_SESSION['kullanici_adi']) and !isset($_SESSION['isim']))
{
header("Location:giris.php");
}
$id=$_SESSION['kullanici_id'];
$kullaciSor=$db->prepare("select * from yonetim where kullanici_id=?");
$kullaciSor->execute(array($id));
$kullaniciCek=$kullaciSor->fetch(PDO::FETCH_ASSOC);
$ayarsor=$db->prepare("select * from ayar where ayar_id=:ayar_id");
$ayarsor->execute(array(
        "ayar_id"=>1
));
$ayarcek=$ayarsor->fetch(PDO::FETCH_ASSOC);


include('fonksiyonlar.php');
if (isset($_FILES['resim'])) {


    $sayfatipi = $_POST['st'];
    $uid = $_POST['uid'];
    $yonlenurl = $_POST['url'];
    $resimboyut = $ayarcek['resimboyut'];


    $st_id = $_POST['st'];

    $query = $db->prepare("SELECT yetki FROM sayfatipleri WHERE id = :st_id");
    $query->bindParam(':st_id', $st_id, PDO::PARAM_INT);
    $query->execute();
    $result = $query->fetch(PDO::FETCH_ASSOC);

    if ($result) {
        $klasor = $result['yetki'];
    } else {
        $klasor = "ortak";
    }


    if ($_POST['yaziiciresim'] == "on") {
        $yaziiciresim=1;
    }else {
        $yaziiciresim=0;
    }

 

    $id = $_POST['menu_id'];
    $yol = "images/".$klasor. "/";
    $kalite = $ayarcek['kalite'];
    $dikeylimit = $ayarcek['dikeylimit'];
    $yataylimit = $ayarcek['yataylimit'];
    $kucultmeorani = $ayarcek['kucultmeorani'];
    $resimkucultme = $ayarcek['resimkucultme'];
    $maxcozunurluluk = $ayarcek['max_resim_cozunurluluk'];
    $resimboyut = $ayarcek['resimboyut'];

    $dilSorgusu = $db->query("SELECT kisaltma FROM dil");
    $diller = $dilSorgusu->fetchAll(PDO::FETCH_COLUMN);
    
    $dilSutunlar = implode(', ', array_map(function ($dil) {
        return "baslik_$dil";
    }, $diller));
    
    $dilPlaceholders = implode(', ', array_fill(0, count($diller), '?'));

    
    include 'resimupload.php';


    } else {

        extract($_POST);
        if($_POST){ 
    
            $dilSorgusu = $db->query("SELECT * FROM dil");
            $diller = $dilSorgusu->fetchAll(PDO::FETCH_ASSOC);

            $dilDizisi = [];
            foreach ($diller as $dil) {
                $kisaltma = $dil['kisaltma'];
                $dilDizisi[$kisaltma] = $_POST["resimbaslik_$kisaltma"];
            }

            $sql = "UPDATE resimler SET ";
            $setValues = [];

            foreach ($dilDizisi as $kisaltma => $baslik) {
                $setValues[] = "baslik_$kisaltma = :baslik_$kisaltma";
            }

            $sql .= implode(', ', $setValues);
            $sql .= " WHERE id = :id";

            $medyaKaydet = $db->prepare($sql);
            $params = [
                "id" => $id,
            ];

            foreach ($dilDizisi as $kisaltma => $baslik) {
                $params["baslik_$kisaltma"] = $baslik;
            }

            $medyaKaydet->execute($params);

            echo "OK";

        }
    
    
    
    extract($_GET);
    if($_GET['islem'] == 1){ 
    
        $medyaKaydet=$db->prepare("update resimler set sil=1 where id=:id");
        $medyaKaydet->execute(array(
            "id"=>$id
        ));
        echo "OK";
        }
        elseif ($_GET['islem'] == 2){
            
        $medyaKaydet=$db->prepare("update resimler set sil=0 where id=:id");
        $medyaKaydet->execute(array(
            "id"=>$id
        ));
        echo "OK";
        }
        
    
        if($_GET['islem'] == 11){ 
    
            $resimsor=$db->prepare("select * from resimler where id=:id");
            $resimsor->execute(array("id"=>$id));
            $resimcek=$resimsor->fetch(PDO::FETCH_ASSOC);
    
            $resimlink = $resimcek['link'];
            unlink("../" . $resimlink . "");
    
            $kalicisil=$db->prepare("delete from resimler where id=:id");
            $kalicisil->execute(array(
                "id"=>$id
            ));
            echo "OK";
        }

    }

?>