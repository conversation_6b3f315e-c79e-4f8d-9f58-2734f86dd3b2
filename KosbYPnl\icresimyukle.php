<?php

if ($yenimenu == 0) {

    $resimsor = $db->prepare("select * from $tablo where id=:id");
    $resimsor->execute(array("id" => $id));
    $resimcek = $resimsor->fetch(PDO::FETCH_ASSOC);


    if (isset($_FILES["resim"]['size'])) {
        $resim = $resimcek['resim'];
    }

    if (isset($_FILES["resim1"]['size'])) {
        $resim1 = $resimcek['resim1'];
    }

    if (isset($_FILES["resim2"]['size'])) {
        $resim2 = $resimcek['resim2'];
    }

    if (isset($_FILES["resim3"]['size'])) {
        $resim3 = $resimcek['resim3'];
    }

    if (isset($_FILES["video"]['size'])) {
        $resim4 = $resimcek['video'];
    }

    if (isset($_FILES["dosya"]['size'])) {
        $dosya = $resimcek['dosya'];
    }

    if (isset($_FILES["dosya2"]['size'])) {
        $dosya2 = $resimcek['dosya2'];
    }

    if (isset($_FILES["video"]['size'])) {
        $video = $resimcek['video'];
    }
}

$klasorsor = $db->prepare("SELECT * FROM sayfatipleri WHERE id=:id");
$klasorsor->execute(array("id" => $sayfatipi));
$klasorcek = $klasorsor->fetch(PDO::FETCH_ASSOC);

$kalite = $ayarcek['kalite'];
$dikeylimit = $ayarcek['dikeylimit'];
$yataylimit = $ayarcek['yataylimit'];
$kucultmeorani = $ayarcek['kucultmeorani'];
$resimkucultme = $ayarcek['resimkucultme'];
$maxcozunurluluk = $ayarcek['max_resim_cozunurluluk'];
$resimboyut = $ayarcek['resimboyut'];
include 'icresimupload.php';
