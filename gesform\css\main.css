body {
    font-family: Hind, sans-serif;

  background: url("../img/ges5.jpg") no-repeat center center fixed;
    -webkit-background-size: cover;
    -moz-background-size: cover;
    -o-background-size: cover;
    background-size: cover;

}
.header{
    margin: 0 0 10px;
    box-shadow: 0 2px 15px 2px rgba(52, 52, 52, 0.6);
	background: url("../img/zemin2.png") repeat-x;
    float: left;
    width: 100%;
}
/* header scrol kısmı 2236. satırda -> .header.header-scroll */
/* header mobile kısmı 2285. satırda -> .header-mobile */
/* header mobile scrol kısmı 2248. satırda -> .header-mobile.header-scroll */


footer{
    margin: 0 0 0;
    padding: 0 0 0;
    box-shadow: 0 5px 20px 5px rgba(52, 52, 52, 0.1);
    background-color: #0a191f;
    float: left;
    width: 100%
}
a:hover{
    text-decoration:none;
}
.success{
    color: #fff;
	text-shadow: 1px 1px 2px black;
	font-size: 45px;
	font-weight: 500;
}
hr.hr-success {
  border: 3px solid white;
  border-radius: 3px;
  width: 70%;
  opacity: 0.7;
}
.belge_baslik{
    color: #fff;
	text-shadow: 1px 1px 2px black;
}
.belge_info{
	color: #ffecb6;
	text-shadow: 1px 1px 2px black;
}
.breadcrumb{
    background: none;
    margin:0;
    padding-left:0;
}
.breadcrumb-item, .breadcrumb-item a{
    font-family: Hind, sans-serif;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.36;
    letter-spacing: normal;
    color:#a7afb4;
}
.breadcrumb-item.active, .breadcrumb-item.active a{
    color: #ffa300;
    font-weight: bold;
}
.breadcrumb-item+.breadcrumb-item::before{
    background: url("../img/arrow-gray.png") no-repeat;
    width: 20px;
    height: 20px;
    margin: 1px 3px 2px;
    padding: 7px 6px;
    content: '';
}
.breadcrumb-item+.breadcrumb-item.active::before{
    background: url("../img/arrow-yellow.png") no-repeat !important;
}
.title{
    font-family: Hind, sans-serif;
    font-size: 24px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    color: #000;
	text-shadow: 2px 2px 2px #fff;
}
.container-box{
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 5px 20px 5px rgba(52, 52, 52, 0.1);
    background-color: #fff;
    float: left;
    width:76.5%;
}
.container-box--title{
    font-family: Hind, sans-serif;
    font-size: 16px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    color: #fdf8e4;
}
.title--yellow{
    font-family: Hind, sans-serif;
    font-size: 14px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    color: #ffa300;
}
p{
    font-family: Hind, sans-serif;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.36;
    letter-spacing: normal;
    color: #747e86;
}
.list-default,
.list-default ul{
    padding: 0;
    margin: 0;
    list-style-type: none;
}
.list-default li{
    font-family: Hind, sans-serif;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.36;
    letter-spacing: normal;
    color: #747e86;
    background: url('../img/icons-general-mark.png') no-repeat;
    background-size: 14px;
    background-position:0 2px ;
    padding-left:20px ;
    margin-top: 10px;
}
.list-default li ul li{
    background: url('../img/list-marker.svg') no-repeat;
    background-position:0 3px ;
}
.list-default-negative li{
     background: url('../img/icons-general-mark-yellow.svg') no-repeat;
     background-size: 14px;
       background-position:0 2px ;
     color:#fff !important;
}
.list-number{
    padding: 0;
    margin: 0;
    list-style-type: none;
    position: relative;
    overflow: hidden;
}
.list-number li{
    font-family: Hind, sans-serif;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.36;
    letter-spacing: normal;
    color: #747e86;
    margin-bottom: 30px;
    overflow:Hidden;
}
.list-number li .number{
    width: 30px;
    height: 30px;
    padding:3px;
    border-radius: 7px;
    background-color: #fff;
    border:3px solid #173a47;
    font-family: Hind, sans-serif;
    font-size: 16px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.25;
    letter-spacing: normal;
    text-align: center;
    color: #ff9500;
    float: left;
    margin-right: 10px;
    position: relative;
    z-index: 20;
}
.list-number li::before{
background: #173a47;
    width: 3px;
    height: 100%;
    content: '';
    position: absolute;
    left:14px;
    z-index: 1;
}
.list-number li:last-child:before { 
background: #fff !important;
}
.list-number-basvuru-takibi li::before{
 left:18px;
}
.list-number-basvuru-takibi li.completed:before{
    background: #173a47;
}
.list-number-basvuru-takibi li.process::before{
    background: #a7afb4;
}
.list-number-basvuru-takibi li.pending::before{
    background: #a7afb4;
}
.list-number-basvuru-takibi li.completed .number{
    background:#173a47 url(../img/ok.svg) no-repeat center center;
    border-radius:50%;
}
.list-number-basvuru-takibi li.process .number{
    background:#fff url(../img/icons-general-basvuru-detay.svg) no-repeat center center;
    border-radius:50%;
    border:3px solid #ffa300;
     width: 40px;
    height: 40px;
    margin-left:-5px;
}
.list-number-basvuru-takibi li{
    padding-left:5px;
}
.list-number-basvuru-takibi li.pending .number{
    background:#a7afb4;
    border-radius:50%;
    border-color:#a7afb4;
}
.list-number-basvuru-takibi .list-number-content h5{
font-family: Hind, sans-serif;
  font-size: 14px;
  font-weight: bold;
  color: #173a47;
} 
.list-number-basvuru-takibi .pending .list-number-content h5{
    margin-top:8px;
}
.list-number-basvuru-takibi .process .list-number-content h5{
    margin-top:13px;
}



hr{
    background: #a7afb4;
    opacity: 0.2;
}
.button--default{
    font-family: Hind, sans-serif;
    font-size: 16px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #ffffff;
    padding: 10px 10px 0;
    border-radius: 16px;
    background-image: linear-gradient(99deg, #ffc522, #ff8000 99%);
    cursor: pointer;
    width: 280px;
    height: 46px;
    display: block;
}
button.button--default{
    border:none;
    padding: 0;
}
.button--default-padding{
    width:initial;
    padding: 10px 37px;
}
.button--default:hover{
    color:#fff;
    text-decoration: none;
    background-image: linear-gradient(105deg, #f7cf5f, #ffa040 99%);
}
.button--default:disabled, .button--default.disabled{
    color:#fff;
     background-image: linear-gradient(to bottom, rgba(229, 235, 240, 0.7), rgba(229, 235, 240, 0.7)), linear-gradient(105deg, #ffc522, #ff8000 99%);
}
/*
.button--default:before{
    height:36px;
    width:280px;
    content:'';
     opacity: 0.5;
  border-radius: 14px;
  -webkit-filter: blur(10px);
  filter: blur(10px);
  background-image: linear-gradient(98deg,#ffc522, #ff8000 99%);
   position: absolute;
            z-index: -1;
}
*/
.spec-box{
    border-right: 1px solid #eff1f1;
}
.spec-box p{
    float: left;
    line-height: 40px;
    padding-left: 10px;
    font-family: Hind, sans-serif;
    font-size: 14px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    letter-spacing: normal;
    color: #747e86;
    margin-bottom:0;
}
.spec-box .rounded-circle{
    width: 40px;
    height: 40px;
    padding-top:10px ;
    background-color: #173a47;
    float: left;
}
.spec-box img{
    margin-left: auto;
    margin-right: auto;
    display: block;
}
.spec-box:last-child{
    border:none;
}
.p_text{
  font-family: Hind, sans-serif;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.36;
  letter-spacing: normal;
  color: #747e86;
}

.link-box{
  width:100%;
  padding: 39px 30px 39px 19px;
  border-radius: 16px;
  box-shadow: 0 5px 20px 5px rgba(52, 52, 52, 0.1);
  background-color: #ffffff !important;
  font-family: Hind, sans-serif;
  font-size: 14px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #486072;
  padding-left:16px;
  display: flex;
  align-items: center;
  height:100px;
  
 }
.link-box:hover{
     color: #486072;
     text-decoration:none;
     box-shadow: 0 5px 20px 5px rgba(52, 52, 52, 0.18);
}
.link-box::after {
    background: url(../img/arrow-yellow-thin.svg) no-repeat;
    transition:.2s;
    content:'';
    width: 11px;
    height: 20px;
    margin-right: 4px;
    position: absolute;
    right: 30px;
}
.link-box--kurumsal{
   background: url("../img/np-business-2092805.svg") no-repeat;
   background-position: -26px;
}
.link-box--hizmetler{
   background: url("../img/electric-station.svg") no-repeat;
   background-position: -26px;
}
.link-box--enerjisa{
   background: url("../img/np-report-794733-000000.svg") no-repeat;
   background-position: -26px;
}
.link-box--medya{
   background: url("../img/illust-rapor.svg") no-repeat;
   background-position: -26px;
}
.link-box--bilgilendirme{
   background: url("../img/illust-bilgilendirme.svg") no-repeat;
   background-position: -45px;
}



.menu-left{
  width: 285px;
  padding: 32px 0 32px 30px;
  border-radius: 24px 0px 0px 24px;
  background-color: #0a191f;
  float:left;
}

.menu-left ul{
    margin:0;
    padding-left:0;
    list-style-type: none;
}

.menu-left .menu-li{
    position:relative;

}

.menu-left .menu-li .menu-link{
  font-family: Hind, sans-serif;
  font-size: 16px;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #ffffff;
  display:block;
   padding: 12px 30px 12px 30px;
   cursor:pointer;
   
}
.menu-left .menu-li .menu-link:hover, .menu-left .menu-li .menu-link[aria-expanded="true"], .menu-left .menu-li .menu-link.active {
  text-decoration:none;    
  border-radius: 38px 0 0 38px;
  background-color: #f5f7f9;
  font-weight: bold;
  color: #231f20;
  z-index: 2;
  position: inherit;
}

.menu-left .menu-li .menu-link:hover:before,.menu-left .menu-li .menu-link.active:before, .menu-left .menu-li .menu-link[aria-expanded="true"]:before{
  width: 49px;
  height: 40px;
  -webkit-filter: blur(10px);
  filter: blur(10px);
  background-color: rgba(255, 213, 34, 0.24);
  content:'';
  left:0;
  top:0;
  position:absolute;
}
.menu-left .menu-li .menu-link::after {
    background: url(../img/menu-arrow-right.svg) no-repeat;
    content:'';
    width: 11px;
    height: 20px;
    float:right;
    transition:.2s;
    position: absolute;
    right: 20px;
    top: 50%;
    margin-top: -10px;
}

.menu-left .menu-li .menu-link:hover:after, .menu-left .menu-li .menu-link[aria-expanded="true"]:after,.menu-left .menu-li .menu-link.active:after {
    background: url(../img/arrow-yellow-thin.svg) no-repeat;
    content:'';
    width: 11px;
    height: 20px;
    float:right;
}
.menu-left .sub_menu{
  border-radius: 0 0 0 24px;
  background-color: #223035;
  width:100%;
  padding-top:10px;
  padding-bottom:10px;
}
.menu-left .sub_menu:before{
    background:#223035;
    width:100%;
    height:20px;
    content:'';
    position:absolute;
    margin-top:-20px;
    z-index:1;
}

.menu-left .sub_menu .sub_menu-link{
  font-family: Hind, sans-serif;
  font-size: 14px;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #ffffff;
  display:block;
  padding: 12px 45px 12px 30px;   
  position:relative;
}
.menu-left .sub_menu .sub_menu-link:hover, .menu-left .sub_menu .sub_menu-link.active{
  text-decoration:none;  
  color: #ffa300;
}
.menu-left .sub_menu-li .sub_menu-link::after {
    background: url(../img/sub_menu_arrow.svg) no-repeat;    
    content:'';
    width: 8px;
    height: 14px;
    margin-top: 4px;
    position: absolute;
    right: 25px;
    top: calc(50% - 10px);
}
.menu-left .sub_menu-li .sub_menu-link:hover:after, .menu-left .sub_menu-li .sub_menu-link.active:after {
    background: url(../img/sub_menu_arrow-hover.svg) no-repeat; 
}
.menu-left .menu-li .menu-link[aria-expanded="false"]:after{
  transform: rotate(90deg);
  transition:.2s;
}
.menu-left .menu-li .menu-link[aria-expanded="true"]:after{
  transform: rotate(-90deg);
  transition:.2s;
}

.container-detail-block{
  padding-left:20px;
  width: calc(100% - 285px);
  float:left;
	border: 1px solid #666;
	border-radius: 0px 20px 20px 0;
}
.yetkili-elektrikci-container {
    width: calc(100% - 305px);
}

.sub-title{
  font-family: Hind, sans-serif;
  font-size: 20px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: normal;
  color: #173a47
}
.container-detail---shadow-box{
  padding: 20px;
  border-radius: 0 20px 20px 0;
  box-shadow: 0 5px 20px 5px rgba(52, 52, 52, 0.2);
  background: rgba(0, 0, 0, 0.3);
  width:100%;
  float:left;
}


.container-detail---shadow-box h3{
  font-family: Hind, sans-serif;
  font-size: 16px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.25;
  letter-spacing: normal;
  color: #fcfaf2;
  text-shadow: 3px 3px 2px black;
}

ul.table-list,
.ul-search-filter{
    padding:0;
    margin:0;
    list-style-type: none;
}

ul.table-list li{
    float:left;
    width:100%;
    border-bottom: 1px solid rgb(167 175 180 / 20%);
    padding: 14px 0;
}
ul.table-list li:last-child{
    border-bottom:none !important;
}
ul.table-list li .p_bold
{   
    width: 40%;
}
ul.table-list li p{
    display:block;
    float:left;
    margin-bottom:0;

}
.p_bold{
    font-weight: 600;
}
.menu-left--mobile{display:none;}
.menu-left--mobile{
    width:100%;
    margin-top:-5px;
    margin-bottom:30px;
}

.menu-left--mobile .btn-group{
    width:100%;
}
.menu-left--mobile button{
  border-radius: 8px;
  box-shadow: 0 0 15px 0 rgba(116, 126, 134, 0.2);
  background-color: #0a191f;
  width:100%;
  text-align:left;
  font-family: Hind, sans-serif;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
   line-height: normal;
  letter-spacing: normal;
  border:none;
  padding: 4px 15px 7px 15px;
}

.menu-left--mobile button:before{
    background: url(../img/arrow-yellow-thin.svg) no-repeat;
    content: '';
    width: 11px;
    height: 20px;
    transform: rotate(90deg);
    transition: .2s;
    position: absolute;
    right: 18px;
    top: 18px;
}
.menu-left--mobile button[aria-expanded="true"]:before{
    transform: rotate(-90deg);
    transition: .2s;
}

.menu-left--mobile span{  
  font-size: 10px; 
  color: #ffffff;
}
.menu-left--mobile p{
  font-size: 14px;
  color: #ffc522;
  margin-bottom:0;
}
.menu-left--mobile .dropdown-menu{
  padding: 0 0 19px;
  border-radius:0 0 8px 8px;
  box-shadow: 0 0 15px 0 rgba(116, 126, 134, 0.2);
  background-color: #0a191f;
  width:100%;
  margin: -3px 0 0 0;
  border-top: 1px solid #ffa300;
}
.menu-left--mobile .dropdown-menu ul{
    padding:0;
    margin:0;
    list-style-type: none;
    border: none;
    text-align: left;
}

.menu-left--mobile .dropdown-menu ul a{
  font-family: Hind, sans-serif;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.36;
  letter-spacing: normal;
  color: #ffffff;
  border-bottom:1px solid rgb(167 175 180 / 20%);
  display:block;
  padding: 17px 14px;
}
.menu-left--mobile .dropdown-menu ul a:hover, .menu-left--mobile .dropdown-menu ul a.active{
    background:#223035;
}

.bg-yellow{
    background: rgba(255, 197, 36 ,0.5);
}
.text-color-yellow{
    color:#ffa300 !important;
}
.text-color-blau{
    color:#00888a !important;
}


.ayedas-genel-mudurluk .iframe{
    margin-top: -20px;margin-bottom: -26px; margin-right: -20px;
}

.ayedas-genel-mudurluk .rounded-circle{
    width: 40px;
    height: 40px;
    padding-top:10px ;
    background-color: #173a47;
    float: left;
    display:Block;
    margin-right:20px;
}
.ayedas-genel-mudurluk img{
    margin-left: auto;
    margin-right: auto;
    display: block;
}

.ayedas-genel-mudurluk p {
    width:calc(100% - 80px);
     border-left: 1px solid #eff1f1;
     padding-left:20px;
     display:flex;
     align-items:center;
     min-height:40px;
     float:left;
}
 
.ayedas-genel-mudurluk .a-tum_operasyon_merkezleri{
font-family: Hind, sans-serif;
  font-size: 14px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: right;
  color: #486072;
  float:right;
  text-decoration:none;

}
.ayedas-genel-mudurluk .a-tum_operasyon_merkezleri::after {
    background: url(../img/sub_menu_arrow-hover.svg) no-repeat;
    content: '';
    width: 8px;
    height: 14px;
    margin-top: 3px;
    margin-left:10px;
    float:right;    
}

.isbirliklerimiz .item-isbirliklerimiz{
    width:20%;
}
.isbirliklerimiz .item-isbirliklerimiz .img{
  width: 123px;
  height: 123px;
  padding: 33px 11px 32px 12px;
  box-shadow: 0 5px 20px 5px rgba(52, 52, 52, 0.1);
  background-color: #ffffff;
  border-radius:50%;
  margin:0 auto;
}
.isbirliklerimiz .item-isbirliklerimiz .img img{
    width:100%;
}
.isbirliklerimiz .item-isbirliklerimiz .text{
  font-family: Hind, sans-serif;
  font-size: 14px;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.36;
  letter-spacing: normal;
  text-align: center;
  color: #0a191f;
  padding: 15px 27px 0
}

.link-border-box{
  width:100%;  
  height:100px;
  border-radius: 16px;
  background: linear-gradient(110deg, rgb(255 197 34 / 50%), rgb(255 128 0 / 50%) 99%);
  padding:10px;
}

.link-border-box a{
  width:100%;  
  border-radius: 12px;
  box-shadow: 0 5px 20px 5px rgba(52, 52, 52, 0.05);
  background-color: #ffffff;
  display:Block;
  height:80px;
  font-family: Hind, sans-serif;
  font-size: 16px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.25;
  letter-spacing: normal;
  color: #173a47;
  padding:30px 10px;
}
.link-border-box a:hover{
     color: #486072;
     text-decoration:none;
    box-shadow: 0 5px 20px 5px rgba(52, 52, 52, 0.18);
}
.link-border-box::before {
    background: url(../img/arrow-right-gray.svg) no-repeat;
    transition:.2s;
    content:'';
    width: 11px;
    height: 20px;
    margin-right: 10px;
    float: right;
    margin-top: 30px;
}

.kurumsal_kariyer{
    background: url(../img/kurumsal-kariyer-bg.jpg) no-repeat;
    padding: 10px;
    object-fit: contain;
    border-radius: 8px;
    width:100%;
    float:left;
    min-height:201px;
    background-size:cover;
}
.kurumsal_kariyer--border{
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: solid 1px #ffffff;
  background-color: rgb(10 25 31 / 50%);
  width:100%;
  min-height:180px;
}
.kurumsal_kariyer .bg{
    padding:10px 20px;
    border-right:1px solid rgb(255 255 255 / 20%);
    margin-top:28px;
}

.kurumsal_kariyer .bg div{
  font-family: Hind, sans-serif;
  font-size: 50px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #ffffff;
  width: 100%;
  white-space: nowrap;
  overflow: hidden !important;
  text-overflow: ellipsis;
}
.kurumsal_kariyer .bg span{
  font-family: Hind, sans-serif;
  font-size: 20px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: normal;
  color: #ffc522;
}
.border-box--shadow{
  border-radius: 8px;
  box-shadow: 0 5px 20px 5px rgba(52, 52, 52, 0.1);
  background-color: #ffffff;
  min-height: 175px;
}

.kariyer-box{
    overflow:hidden;
    cursor:pointer;
}
.kariyer-box .date,
.bulten-box .date{
  font-family: Hind, sans-serif;
  font-size: 12px;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  width:100%;
  text-align:right;
  display:Block;
  color:#a7afb4;
}
.kariyer-box h4{
  font-family: Hind, sans-serif;
  font-size: 14px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  width:100%;
  display:Block;
  color: #ffa300;
  margin:0;
}

.kariyer-box h3{
  font-family: Hind, sans-serif;
  font-size: 18px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  width:100%;
  display:Block;
  color: #0a191f;
  margin-bottom:0;
  min-height:55px;
}
.kariyer-box p{
    height:38px;
    overflow:hidden;
}
.kariyer-box:hover .detay-link:after {
    background: url(../img/sub_menu_arrow-gray.svg) no-repeat;
}
.detay-link{
font-family: Hind, sans-serif;
  font-size: 14px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: right;
  color: #486072;
  float:right;
  text-decoration:none;
  margin-top:10px;
  margin-bottom:10px;

}
.detay-link::after {
    background: url(../img/sub_menu_arrow-hover.svg) no-repeat;
    content: '';
    width: 8px;
    height: 14px;
    margin-top: 3px;
    margin-left:10px;
    float:right;    
}

.detay-link:hover {
    color: #486072;
    text-decoration:none;
}
.detay-link:hover:after {
    background: url(../img/sub_menu_arrow-gray.svg) no-repeat;
}
.detay-link.detay-link--negative:after{
 background: url(../img/sub_menu_arrow-gray.svg) no-repeat;
}
.detay-link.detay-link--negative:hover:after{
 background: url(../img/sub_menu_arrow-hover.svg) no-repeat;
}

.kariyer-box .spec-box{
    border:none;
}
.kariyer-box .spec-box p{
    width: calc(100% - 42px);
}

.select-box--full{  
  width:100%;
}

.select-box--full button{
  border-radius: 8px;
  box-shadow: 0 0 15px 0 rgba(116, 126, 134, 0.2);
  background-color: #ffffff;
  width:100%;
  border:none;
  text-align:left;
  padding:0 12px;
  height:46px;
}

.select-box--full button span{
  font-family: Hind, sans-serif;
  font-size: 10px;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #486072;
  display:block;
}
.select-box--full button p{
  font-family: Hind, sans-serif;
  font-size: 14px;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.36;
  letter-spacing: normal;
  color: #0a191f;
  margin:0;
  white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis;
    width: 100%;
    padding-right: 20px;
}

.select-box--full button:before{
    background: url(../img/arrow-right-gray.svg) no-repeat;
    content: '';
    width: 11px;
    height: 20px;
    transform: rotate(90deg);
    transition: .2s;
    position: absolute;
    right: 34px;
    top: 14px;
}
.select-box--full button[aria-expanded="true"]{
 box-shadow: 0 0 15px 0 rgba(116, 126, 134, 0.2);
  border: solid 1px #ffc522;
}
.select-box--full button[aria-expanded="true"]:before{
    transform: rotate(-90deg);
}
.select-box--full .dropdown-menu{
  padding: 20px 15px;
  border-radius: 8px;
  box-shadow: 0 5px 20px 5px rgba(52, 52, 52, 0.1);
  border:none;
  max-height:350px;
  overflow:hidden;  
  width: calc(100% - 30px);
}

.select-box--full ul{
     margin: 0;
    list-style-type: none;
    padding:0;
    overflow-y:auto;
    height:255px;
    float:left;
    width:100%;
}
.select-box--full ul li a{
  margin: 0;
  list-style-type: none;
  font-family: Hind, sans-serif;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.36;
  letter-spacing: normal;
  color: #747e86;
  border-bottom: 1px solid rgb(167 175 180 / 20%);
  display: block;
  padding: 14px 10px 14px 0;
}

.select-box--full ul li a:hover{
    text-decoration:none;
}
.scroll-tesisat-secimi{
    overflow-x:hidden;
    overflow-y:auto;
    max-height:200px;
}

.select-box--full ul::-webkit-scrollbar,
.scroll-tesisat-secimi::-webkit-scrollbar {
  width: 6px;
}
.select-box--full ul::-webkit-scrollbar-track,
.scroll-tesisat-secimi::-webkit-scrollbar-track {
  background: #e5ebf0;
  border-radius: 3px;
}
.select-box--full ul::-webkit-scrollbar-thumb,
.scroll-tesisat-secimi::-webkit-scrollbar-thumb {
  background: #a7afb4;
  border-radius: 3px;
}
.select-box--full ul::-webkit-scrollbar-thumb:hover,
.scroll-tesisat-secimi::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.select-box--full__search-input{
  padding: 0 15px 0 0;
  border-radius: 30px;
  box-shadow: 0 5px 20px 5px rgba(52, 52, 52, 0.1);
  background-color: #ffffff;
  height: 40px;
  margin-bottom:16px;
}

.select-box--full__search-input input{
  border:none;
  float: left;
  height: 40px;
  font-family: Hind, sans-serif;
  font-size: 14px;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #173a47;
  background:none;  
  padding-left:70px;
  width: calc(100% - 46px);
}
.select-box--full__search-input .clear{
    font-family: Hind, sans-serif;
    font-size: 12px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 10px;
    letter-spacing: normal;
    text-align: right;
    border-bottom: 2px solid #486072;
    float: right;
    margin-top: 14px;
    cursor:pointer;
}
.select-box--full__search-input:before{
    content: '';
    width: 60px;
    height: 40px;
    position: absolute;
    background: url(../img/search-input-icon-yellow-16.svg) no-repeat;
    border-radius: 20px;
    background-position: 22px 12px;
    box-shadow: 0 5px 20px 5px rgba(52, 52, 52, 0.1);
    left:15px;
}

.overflow-hidden{
    overflow:hidden;
}
.box-egitim--bg:after{
    position:absolute;
    content:'';
    width:84px;
    height:92px;
    background: url(../img/illust-egitim.svg) no-repeat;    
    left:3px;
    bottom:-12px;
}

.search-input{
    width:100%;
}
.search-input input{
  width:100%;
  padding: 10px 10px 10px 60px;
  border-radius: 8px;
  box-shadow: 0 0 15px 0 rgba(116, 126, 134, 0.2);
  background-color: #ffffff;
  border: solid 1px #fff;
  height:46px;
  font-family: Hind, sans-serif;
  font-size: 16px;
  font-weight: 300;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  color:#a7afb4; 
}
.search-input input::placeholder{
    color:#a7afb4;  
}
.search-input input[type="text"]:focus{
 box-shadow: 0 0 15px 0 rgba(116, 126, 134, 0.2) !important;
 border: solid 1px #ffc522 !important;
}
.search-input.shadow-clear input[type="text"]:focus{
 box-shadow: 0 0 15px 0 rgba(116, 126, 134, 0.2) !important;
 border: solid 1px #fff !important;
}
 input[type="text"]:focus{
 outline: 0 none;
 }
.search-input:before{
    content: '';
    width: 33px;
    height: 20px;
    position: absolute;
    background: url(../img/search-input-icon.svg) no-repeat;
    margin-top: 13px;
    margin-left: 13px;
    border-right: 1px solid rgb(179 194 212 / 50%);
}
.btn-clear-li{
    font-family: Hind, sans-serif;
    font-size: 14px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 17px;
    letter-spacing: normal;
    text-align: right;
    border-bottom: 2px solid #486072;
    color: #486072;
    cursor: pointer;
    float: right;
    margin-right: 18px;
    margin-top: 2px;
}

.img-content--banner .text{
  font-family: Hind, sans-serif;
  font-size: 50px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.06;
  letter-spacing: normal;
  color: #ffffff;
  position:absolute;
  z-index:2;
  top: calc(50% - 26px);
  padding-left: 40px;
}
.img-content--banner a{
  padding: 10px 92px;
  border-radius: 16px;
  background-image: linear-gradient(99deg, #173a47, #0c1f27 99%);
  font-family: Hind, sans-serif;
  font-size: 16px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  color: #ffffff;
  position: absolute;
  bottom: 40px;
  right: 65px
}
.img-content--banner a:hover{
    text-decoration:none;
    opacity:.8
}

.accordion-default .card{
  
  border-radius: 8px !important;
  box-shadow: 0 5px 20px 5px rgba(52, 52, 52, 0.1);
  background-color: #ffffff;
  border:none;
  margin-bottom:20px;
}
.accordion-default .card .card-header{
    border:none;
    background:#fff;
    margin: 0 20px;
    padding: 14px 0;
}
.accordion-default .card .card-header button{
     font-family: Hind, sans-serif;
  font-size: 16px;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.19;
  letter-spacing: normal;
  color: #231f20;
  border:none;
  outline:none;
  background:none;
  padding-left: 0;
}
.accordion-default .card .card-header button[aria-expanded="true"]{
  color: #ffa300;
}
.accordion-default .card .card-header button:hover, .accordion-default .card .card-header button:focus{
 text-decoration:none;
  border:none;
  outline:none;
  box-shadow:none;
}
.accordion-default .card .card-header button:before{
    background: url(../img/arrow-right-gray.svg) no-repeat;
    transition: .2s;
    content: '';
    width: 11px;
    height: 20px;
    margin-right: 10px;
    float: right;
     transform: rotate(90deg);
}

.accordion-default .card .card-header button[aria-expanded="true"]:before{
    transform: rotate(-90deg);
    transition:.2s;
}
.accordion-default .card .card-body{
    border:none;
    background:#fff;
    border-top:1px solid rgb(167 175 180 / 20%);
    margin: 0 20px;
    padding: 14px 0 20px;
  font-family: Hind, sans-serif;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.36;
  letter-spacing: normal;
  color: #747e86;
  min-height:60px;
    position: relative;
    z-index: 2;
    float:left;
    width:calc(100% - 40px)
}
.accordion-default .card .card-body:after{
    position: absolute;
    content: '';
    width: 90px;
    height: 87px;
    background: url(../img/shape-sss.svg) no-repeat;
    left: -20px;
    bottom: 0;
    z-index: -1;
}
.accordion-open .card{
  border-radius: 8px !important;
  box-shadow: 0 5px 20px 5px rgba(52, 52, 52, 0.1);
  background-color: #ffffff;
  border:none;
  margin-bottom:20px;
}
.accordion-open .card .card-header{
  border:none;
  padding: 18px 20px;
  border-radius: 8px 8px 0px 0px;
  box-shadow: 0 5px 20px 5px rgba(52, 52, 52, 0.1);
  background-color: #ffffff;
  font-family: Hind, sans-serif;
  font-size: 14px;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #173a47;  
}
.accordion-open .card .card-body{
  font-family: Hind, sans-serif;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.36;
  letter-spacing: normal;
  color: #747e86;
}
.bulten-box--list {
    position: relative;
}

.bulten-box--list:after {
    background: url(../img/arrow-right-gray.svg) no-repeat;
    content: '';
    width: 11px;
    height: 20px;
    transition: .2s;
    right: 20px;
    margin-top: -10px;
    top: 50%;
    position: absolute;
}
.bulten-box--list-img:before{
    margin-top: 80px;
    margin-right: 20px;
}
.bulten-box--list:hover,
.border-box--shadow:hover,
.kariyer-box:hover{
    box-shadow: 0 5px 20px 5px rgba(52, 52, 52, 0.18);
}
.bulten-box--list .img{
    width:33%;
    float:left;
    margin-right:40px;
    border-radius: 6px;
    min-height:180px;
}
.bulten-box--list .img img{
   width: 100%;
    object-fit: cover;
    height: 180px;
    border-radius: 8px;
}
.bulten-box--list .img:after{
    content: '';
    object-fit: contain;
    opacity: 0.7;
    border-radius: 6px;
    box-shadow: 0 9px 29px 9px rgb(52 52 52 / 10%);
    background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0), #173a47);
    height: 180px;
    left: 15px;
    bottom: 0px;
    z-index: 2;
    background-size: contain;
    float: right;
    width: 100%;
    margin-top: -180px;
}

.video--banner{
    cursor:pointer;
    z-index:88;
    position:absolute;
    height:400px;
    width:100%;
}
.video--banner:after{
    content: '';
    object-fit: contain;
    opacity: 0.7;
    border-radius: 6px;
    box-shadow: 0 9px 29px 9px rgb(52 52 52 / 0%);
    background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0), #173a47);
    /*background:url(../img/filtre.svg) no-repeat;*/
    width: calc(100% - 30px);
    position: absolute;
    height: 250px;
    left: 0;
    bottom:0px;
    z-index: 2;
    background-size: contain;
}

.video--banner:before{
    content: '';
    object-fit: contain;
    width: 14%;
    position: absolute;
    height: 98px;
    left: calc(50% - 50px);
    top: 40%;
    z-index: 4;
    background: url(../img/np-play-906231-000000.svg) no-repeat;
    background-size: contain;
}


.carousel-item .video--banner:after {
    width: 100%;
}
.carousel-item video {
    z-index:23;
    position:relative;
}



.carouselMedia{
  border-radius: 6px;
  box-shadow: 0 9px 29px 9px rgba(52, 52, 52, 0.1);
  background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0), #173a47);
}
.carouselMedia:after{
  object-fit: contain;
  border-radius: 6px;
  box-shadow: 0 9px 29px 9px rgba(52, 52, 52, 0.1);
  background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0), #173a47);
  content:'';
  width:100%;
  z-index:22;
  bottom:0;
  position:absolute;
  height:280px;  
}

.carouselMedia .carousel-control-next,
.homepage_slider .carousel-control-next {
    width: 40px;
    height: 80px;
    padding: 30px 0px 30px 14px;
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
    background-color: rgba(255, 255, 255, 0.5);
    border-top-left-radius: 40px;
    border-bottom-left-radius: 40px;
    position: absolute;
    top: 40%;
    z-index: 23;
    opacity: 1 !important;
}

.carouselMedia .carousel-control-next .carousel-control-next-icon,
.homepage_slider .carousel-control-next .carousel-control-next-icon{
    background: url(../img/menu-arrow-right.svg) no-repeat; 
}
.carouselMedia .carousel-control-prev .carousel-control-prev-icon,
.homepage_slider .carousel-control-prev .carousel-control-prev-icon{
    background: url(../img/menu-arrow-right.svg) no-repeat;   
    margin-right: 7px;
   transform: rotate(-180deg);
}
.carouselMedia .carousel-control-next:hover,
.homepage_slider .carousel-control-next:hover{
    background: linear-gradient(180deg, #ffc522 1%, #ff8000 100%);
}


.carouselMedia .carousel-control-prev,
.homepage_slider .carousel-control-prev{
  width: 40px;
  height: 80px;
  padding: 30px 6px 30px 0px;
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
  background-color: rgba(255, 255, 255, 0.5);
  border-top-right-radius: 40px;
  border-bottom-right-radius: 40px;
  position: absolute;
  top: 40%;
  z-index:23;
  opacity:1 !important;
}
.carouselMedia .carousel-control-prev:hover,
.homepage_slider .carousel-control-prev:hover{
    background: linear-gradient(180deg, #ffc522 1%, #ff8000 100%);
}

.carouselMedia .carousel-indicators{
    bottom:-34px !important;
}
.carouselMedia .carousel-indicators li{
 width: 4px;
  height: 4px;
  margin: 2px 2px 2px 2px;
 opacity: 0.6;
  border-radius: 4px;
  box-shadow: 0 2px 64px 0 #ffd522;
  background-color: #a7afb4;
  border:none;
}

.carouselMedia .carousel-indicators li.active{
 width: 22px;
  height: 8px;
  margin: 0 8px;
  opacity: 0.7;
  border-radius: 4px;
  box-shadow: 0 2px 8px 0 #ffd522;
  background-color: #ffd522;
}

.table-default{
    width:100%;
     border-radius: 8px;
}
.table-default thead th:first-child {
    border-radius: 8px 0 0 0;
    -moz-border-radius: 8px 0 0 0;
    -webkit-border-radius: 8px 0 0 0;
}
.table-default thead th:last-child {
    border-radius: 0 8px 0 0;
    -moz-border-radius: 0 8px 0 0;
    -webkit-border-radius: 0 8px 0 0;
}


.table-default thead{  
  background-color: #173a47; 
  padding:10px;
}
.table-default thead th{ 
      font-family: Hind, sans-serif;
  font-size: 16px;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #ffffff;
  padding: 9px .75rem; 
  margin:10px;
}
.table-default thead th .t-border{
     padding:6px 0;
     border-right:1px solid  #fafafa;
} 
.table-default tbody .t-border{
     padding:6px 0;
     border-right:1px solid  #a7afb4;
} 
.table-default tbody td:last-child .t-border{
     padding:6px 0;
     border-right:none;
} 
.table-default tbody td{
  vertical-align: top;
  padding: 9px .5rem;
}
    .table-default tbody td, .table-default tbody td div {
        font-family: Hind, sans-serif;
        font-size: 14px;
        font-weight: 600;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.36;
        letter-spacing: normal;
        color: #231f20;
        word-break: break-all;
    }
        .break-word tbody td, .break-word tbody td div {
            word-break: break-word;
        }
.table-default .table-detay-link{
  cursor:pointer;
  display:Block;
  float:right;
}
.table-default tbody tr:nth-of-type(odd) {
    background-color:#f5f5f5;

}
.table-default { height: 1px; }
.table-default tr { height: 100%; }
.table-default td { height: 100%; }
.table-default td > div { height: 100%; }

.pagination-default{
    justify-content: center;    
}

.pagination-default .page-link{
  background:none;
  border: none;
  font-family: Hind, sans-serif;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.33;
  color: #747e86;
  padding: 5px 9px 2px 9px ;
  border-radius: 6.7px;
  height:25px;
  text-align:Center;
}
.pagination-default .page-link:hover, .pagination-default .page-link.active{
  background-color: #173a47;
  font-size: 14px;
  font-weight: bold;
  color: #ffa300;
}

.pagination-default .page-item:focus, .pagination-default .page-link:focus{
    outline:none;
    box-shadow:none;
}

.page-link.previous{
    background: url(../img/sub_menu_arrow-hover.svg) no-repeat;    
        background-position: center center !important;
    transform: rotate(-180deg);
    margin-right:5px;
}
.page-link.next{
    background: url(../img/sub_menu_arrow-hover.svg) no-repeat;
    background-position: center center !important;
}

.page-link.previous:hover, .page-link.next:hover{
      background: url(../img/sub_menu_arrow-hover.svg) no-repeat;
}
.page-link.previous.disabled, .page-link.next.disabled{
    background: url(../img/arrow-gray.png) no-repeat center center !important;
    opacity:.5;
}
.pagination-default--info{
  text-align:Center;
  font-family: Hind, sans-serif;
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.33;
  letter-spacing: normal;
  color: #747e86;
}
.pagination-default--info strong{
  font-weight: 600;
  color: #0a191f;
}
 .table-default--mobile{
   display:none;
   width:100%;
   float:left;
 }
 .table-default--mobile th{
  padding: 15px 0 15px 10px;
  background-color: #173a47;
  font-family: Hind, sans-serif;
  font-size: 14px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #ffffff;
  width:40%;
  vertical-align: top;
 }
 
 .table-default--mobile td{
 padding: 15px 0 15px 10px;
  font-family: Hind, sans-serif;
  font-size: 14px;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.36;
  letter-spacing: normal;
  color: #231f20;
  vertical-align: top;
 }
 .table-default--mobile tbody:nth-of-type(odd){
    background-color: #f5f5f5;
}

.box-musteri-hizmetleri{
width: 285px;
float:left;
}
.box-musteri-hizmetleri h3 {
    font-family: Hind, sans-serif;
    font-size: 16px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.25;
    letter-spacing: normal;
    color: #173a47;
}

.box-musteri-hizmetleri .social a{
  width:100%;
  display:block;
  margin-top:20px;
  font-family: Hind, sans-serif;
  font-size: 14px;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.36;
  letter-spacing: normal;
  color: #747e86;
}
.box-musteri-hizmetleri .social a img{
    margin-right:10px;
}

.box-musteri-hizmetleri .phone-number a img{
  width: 40px;
  height: 40px;
  padding: 8px;
  background-color: #173a47;
  margin-right: 10px;
  float: left;
}
.box-musteri-hizmetleri .phone-number a{
  width:100%;
  display:block;
  margin-top:20px;
  font-family: Hind, sans-serif;
  font-size: 14px;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.36;
  letter-spacing: normal;
  color: #747e86;
}
.box-musteri-hizmetleri .phone-number a:hover{
    opacity:.7;
}
.box-musteri-hizmetleri .phone-number a strong{
    display:block;
}

.search-input--autocomplete .dropdown-menu,
.search-input--autocompleteArama .dropdown-menu,
.search-input--default .dropdown-menu {
    padding: 20px 0;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 5px 20px 5px rgb(52 52 52 / 10%);
    border: none;
    overflow: hidden;
    width: calc(100% - 30px);
    margin-top: 0;
    border-top: 1px solid rgb(167 175 180 / 20%);
    transform: translate3d(15px, 40px, 0px) !important;
    z-index: 9999 !important;
}

.search-input--autocomplete .button-box,
.search-input--default .button-box,
.search-input--autocompleteArama .button-box{
    position: absolute;
    right: 35px;
    margin-top: -34px;
    -webkit-transition: width .35s ease-in-out;
    transition: .2s
}

    .search-input--autocomplete .button-box .close,
    .search-input--default .button-box .close,
    .search-input--autocompleteArama .button-box .close{
        float: left;
        cursor: pointer;
        opacity: 1;
        display: none;
    }
    .search-input--autocomplete .button-box .clear,
    .search-input--autocompleteArama .button-box .clear{
        font-family: Hind, sans-serif;
        font-size: 14px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: 17px;
        letter-spacing: normal;
        text-align: right;
        border-bottom: 2px solid #486072;
        color: #486072;
        cursor: pointer;
        float: left;
        margin-right: 18px;
        margin-top: 2px;
        display: none;
    }

        .search-input--autocomplete .button-box .clear:hover,
        .search-input--autocomplete .button-box .close:hover,
        .search-input--autocompleteArama .button-box .close:hover,
        .search-input--autocompleteArama .button-box .clear:hover,
        .search-input--default .button-box .close:hover {
            opacity: .7;
        }
.search-input--autocomplete .dropdown-menu ul,
.search-input--default .dropdown-menu ul,
.search-input--autocompleteArama .dropdown-menu ul {
    list-style-type: none;
    padding: 0;
}
    .search-input--autocomplete .dropdown-menu ul li a,
    .search-input--default .dropdown-menu ul li a,
    .search-input--autocompleteArama .dropdown-menu ul li a{
        font-family: Hind, sans-serif;
        font-size: 16px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.19;
        letter-spacing: normal;
        color: #747e86;
        display: block;
        border-bottom: 1px solid rgb(167 175 180 / 20%);
        padding: 14px 20px;
        cursor: pointer;
    }
        .search-input--autocomplete .dropdown-menu ul li a:hover,
        .search-input--autocompleteArama .dropdown-menu ul li a:hover,
        .search-input--default .dropdown-menu ul li a:hover {
            box-shadow: 0 5px 20px 5px rgba(52, 52, 52, 0.1);
            background-color: #ffffff;
        }
.search-input--autocomplete .dropdown-menu .input-text,
.search-input--autocompleteArama .dropdown-menu .input-text,
.search-input--default .dropdown-menu .input-text,
.search-input .dropdown-menu .input-title {
    float: left;
    font-family: Hind, sans-serif;
    font-size: 20px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: normal;
    color: #173a47;
    padding-left: 20px;
}
.search-input--autocomplete .dropdown-menu .detay-link,
.search-input--autocompleteArama .dropdown-menu .detay-link {
    padding-right: 20px;
}
.search-input--autocomplete input[data-value]:not([data-value=""]) ~ .button-box .clear,
.search-input--autocompleteArama input[data-value]:not([data-value=""]) ~ .button-box .clear,
.search-input--default input[data-value]:not([data-value=""]) ~ .button-box .clear {
    display: block;
}

.search-input--autocomplete.show .button-box .clear,
.search-input--autocompleteArama.show .button-box .clear,
.search-input--default.show .button-box .clear {
    display: block;
}
.search-input--autocomplete.show .button-box .close,
.search-input--autocompleteArama.show .button-box .close,
.search-input--default.show .button-box .close {
    display: block;
}
.search-input--autocomplete.show:before,
.search-input--autocompleteArama.show:before {
    background: url(../img/search-input-icon-yellow-20.svg) no-repeat;
    z-index: 104;
}
.search-input--autocomplete input[type="text"]:focus,
.search-input--autocompleteArama input[type="text"]:focus {
    box-shadow: none !important;
    border: solid 1px #f5f7f9 !important;
}
.search-input--autocomplete.show input,
.search-input--autocompleteArama.show input {
    border-radius: 8px 8px 0 0;
}

.homepage_slider{
    height:670px;
    overflow:hidden;
}
.homepage_slider .carousel,
.homepage_slider .carousel-inner
{
    height:670px;
}
.slider-mask{
    width:100%;
    background: url(../img/slider-mask.svg) no-repeat;
    height:670px;
    position:absolute;
    top:0;
    background-size: cover;
}
.homepage_slider .carousel-caption{
    top:280px;
    max-width:1200px;
    text-align:left;
    left: calc(50% - 590px);
    z-index:1;
}
.homepage_slider .carousel-caption h5{
  font-family: Hind, sans-serif;
  font-size: 50px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.06;
  letter-spacing: normal;
  color: #ffffff;
  width:60%;
  margin-bottom:20px;
}
.homepage_slider .carousel-caption p{
  font-family: Hind, sans-serif;
  font-size: 20px;
  font-weight: 500;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #ffffff;
  width:60%;
  margin-bottom:20px;
}
.homepage_slider .carousel-caption a{
  font-family: Hind, sans-serif;
  font-size: 16px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  color: #ffffff;
  padding: 10px 71px;
  border-radius: 16px;
  background-image: linear-gradient(99deg, #173a47, #0c1f27 99%);
}
.dekupe{
    float:right;
    width:480px;
    margin-top:140px;
}
.dusuk_kalite{
    float:right;
    width:725px;
    margin-top:190px;
}

.homepage_slider .carousel-caption a:hover{
    opacity:.8;
}

.homepage_slider .carousel-control-next{
  top: 50%;
  }
.homepage_slider .carousel-control-prev{
  top: 50%;
}

.homepage_slider .carousel-indicators{
    bottom:90px;
}
.homepage_slider .carousel-indicators li{
 width: 4px;
  height: 4px;
  opacity: 0.6;
  border-radius: 1px;
  box-shadow: 0 2px 64px 0 #ffd522;
  background-color: #ffffff;
  border:none;
  margin: 3px ;
}
.homepage_slider .carousel-indicators li.active{
 width: 22px;
  height: 8px;
  margin: 0 3px;
  opacity: 0.7;
  border-radius: 4px;
  box-shadow: 0 2px 8px 0 #ffd522;
  background-color: #ffd522;
}
.homepage_slider .carousel-indicators li:first-child{
    margin-left:19px;
}

.homepage-header{
  position:absolute;
  top:0;
  height: 124px;
  background-image: linear-gradient(to bottom, rgb(35 31 32 / 75%), rgba(23, 58, 71, 0) 100%);
  width:100%;
  z-index:10;
}
.homepage-header.hover {
    z-index: 100;
}
.homepage-header .header-top,
.header .header-top{
    float:left;
    height:35px;
    width:100%;
}
.header .header-top{
     border-bottom: 1px solid rgb(167 175 180 / 20%);
}
.homepage-header .header-top ul,
.header .header-top ul{
    margin: 0;
    list-style-type: none;
    display: block;
    float: left;
    padding:10px 0 0 0;
}
.homepage-header .header-top li,
.header .header-top li{float:left;}
.homepage-header .header-top a,
.header .header-top a{
  font-size: 12px;
  font-weight: 300;
  font-stretch: normal;
  font-style: normal;  
  padding: 0 30px;
  display:block;
}
.homepage-header .header-top a{
 color: #f5f5f5;
 border-right:1px solid rgb(250 250 250 / 20%);
}
.header .header-top a{
 color: #a7afb4;
 border-right:1px solid rgb(167 175 180 / 20%);
}
.homepage-header .header-top .lang,
.header .header-top .lang {
    margin-left:60px;
}
.homepage-header .header-top .lang a,
.header .header-top .lang a{
    padding: 0 10px;
}
.homepage-header .header-top li:last-child a,.header .header-top li:last-child a{border:none;}
.homepage-header .header-top li a.active,.header .header-top li a.active{font-weight:600; }
.homepage-header .header-top li a:hover, .header .header-top li a:hover{opacity:.7 }
.homepage-header .navbar-brand {
    background: #fff;
    padding: 20px 20px 18px 43%;
    position: absolute;
    left: -43%!important;
    border-top-right-radius: 30px;
    border-bottom-right-radius: 30px;
    top:0px;
}

.homepage-header .navbar,
.header .navbar{
    padding:0 !important;
    width:100%;

}
.homepage-header .menu{
    padding-left:140px;

}
.homepage-header .menu ul li,
.header .menu ul li{
    margin-left:20px;
    padding:24px 30px 18px 30px;
    cursor:pointer;
}
.homepage-header .menu ul li a,
.header .menu ul li a{
  font-family: Hind, sans-serif;
  font-size: 14px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #ffffff;
   position:relative;
   padding:3px !important;
}
.header .menu ul li a{
    color:#173a47;
}

.menu-border{
    position: absolute;
    display:block;
    left: 0;
    top:90%;
    margin:0 auto;
    height: 2px;
    background-color: #fff;
    width: 10px;
    transition: width 1s;
}
.sub_menu-border {
    position: absolute;
    display:block;
    left: 0;
    top:90%;
    margin:0 auto;
    height: 2px;
    background-color: #a7afb4;
    width: 10px;
    transition: width 1s;
}
.homepage-header .menu ul li:hover,
.header .menu ul li:hover {
  border-radius: 8px 8px 0 0;
  box-shadow: 0 5px 20px 5px rgba(52, 52, 52, 0.1);
  background-color: #0a191f;
}
.homepage-header .menu ul li:hover a .menu-border,
.header .menu ul li:hover a .menu-border {
    width: 100%;
}

.homepage-header .menu .sub_menu-box,
.header .menu .sub_menu-box{
  border-radius: 8px;
  box-shadow: 0 5px 20px 5px rgba(52, 52, 52, 0.1);
  background-color: #0a191f;
  width:calc(100% - 340px);
  position:absolute;
  display:none;
  left:160px;
  margin-top:18px;
  z-index:99;
}
.header .menu .sub_menu-box{
left:162px;
}
.homepage-header .menu .sub_menu-box ul,
.header .menu .sub_menu-box ul{
    padding:0;
    list-style-type:none;
    float: left;
    width: 100%;
    margin: 20px 0 30px 0;
}
.homepage-header .menu .sub_menu-box li,
.header .menu .sub_menu-box li{
    width: calc(33% - 60px);
    float: left;
    margin: 0;
    border-bottom: 1px solid rgb(167 175 180 / 20%);
    margin-right: 60px;
}
.homepage-header .menu .sub_menu-box li a,
.header .menu .sub_menu-box li a{
    position: relative;
    padding: 3px !important;
    float:left;
    font-size: 12px;
    font-weight: 600;
    color: #a7afb4;
}
.homepage-header .menu .sub_menu-box li:hover a,
.header .menu .sub_menu-box li:hover a{
    color: #fff;
}
.homepage-header .sub_menu-box ul li:hover a> .sub_menu-border, 
.header .sub_menu-box ul li:hover a> .sub_menu-border {
    width: 100%;
    background:#fff;
}
.homepage-header .menu li:first-child .sub_menu-box,
.header .menu li:first-child .sub_menu-box{
border-radius:0 8px 8px 8px;
}
.homepage-header .menu ul li:hover .sub_menu-box,
.header .menu ul li:hover .sub_menu-box {
    display:Block;
}
.homepage-header.header-scroll,
.header.header-scroll{
	background: url("../img/zemin2.png") repeat-x;
    padding: 0;
    position: fixed;
    top: 0;
    z-index: 99;
    box-shadow: 0px 1px 10px rgb(0 0 0 / 40%);
    transition-duration: 0.1s;
    height:inherit;
}
.header-mobile.header-scroll,
.header-fixed{
    background: url("../img/zemin2.png") repeat-x;
    position: fixed;
    top: 0;
    z-index: 99;
    box-shadow: 0px 1px 10px rgb(0 0 0 / 40%);
    transition-duration: 0.1s;
    height:inherit;
}
.homepage-header.header-scroll .header-top,
.header.header-scroll .header-top{
    display:none;
}
.homepage-header.header-scroll .menu ul li,
.header.header-scroll .menu ul li{ 
    padding: 21px 30px;
}
.homepage-header.header-scroll .menu ul li a,
.header.header-scroll .menu ul li a{
 color: #173a47;
}
.homepage-header.header-scroll .menu ul li:hover a,
.header.header-scroll .menu ul li:hover a,
.header .menu ul li:hover a.nav-link{
 color: #fff;
}
.homepage-header.header-scroll .menu-border,
.header .menu-border,
.header.header-scroll .menu-border {
    background:#173a47;
}
.homepage-header.header-scroll li:hover .menu-border,
.header li:hover .menu-border,
.header.header-scroll li:hover .menu-border {
    background:#fff;
}

.header-mobile{
  display:none;
  padding: 15px;
  box-shadow: 0 5px 20px 5px rgba(52, 52, 52, 0.1);
  background: url("../img/zemin2.png") repeat-x;
  width:100%;
  height:70px;
}
.header-mobile .dropdown{
    float:left;
}
.header-mobile .logo{
   display:block;
   float: left;
    margin: 0px 15px;
}
.header-mobile .logo img{
    height:40px;
}
.header-mobile .btn-menu-open{
  background: url(../img/list.svg) no-repeat;
    width: 24px;
    height: 22px;
    border: none;
    margin-top: 8px;
    padding: 0;
}
.header-mobile .btn-menu-close {
    background: url(../img/close-black.svg) no-repeat;
    width: 24px;
    height: 22px;
    border: none;
    margin-top: 13px;
    padding: 0;
}
.header-mobile .button-online-islemler{
    padding: 10px 20px;
    height: 40px;
}

.header-mobile .button-online-islemler-xs{
    padding: 5px 2px 0px 2px;
    height: 40px;
    font-size: 12px;
    width:60px;
    line-height:15px;
      border-radius: 8px;
}
.header-mobile .dropdown .dropdown-menu {
    border: none;
    padding: 0 0 28px;
    box-shadow: 0 5px 20px 50px rgb(52 52 52 / 90%);
    background-color: #0a191f;
    width: 90%;
    height: 100%;
    margin-top: 0;
    border-radius: 0;
    position: fixed !important;
    transform: translate3d(0, 0, 0) !important;
    z-index:9999 !important;
    overflow: scroll;
}
.header-mobile .dropdown .dropdown-menu:after {
    content: '';
    width: 100%;
    position: absolute;
    background-color: rgba(35, 31, 32, 0.75);
    height: 100%;
    top: 0;
}

.header-mobile .dropdown .dropdown-menu .menu-header {
    height: 70px;
    margin: 0 15px 0 0;
    padding: 15px;
    box-shadow: 0 5px 20px 5px rgba(52, 52, 52, 0.1);
    background-color: #ffffff;
    width: 100%;
    float: left;
    margin-bottom: 26px;
}

    .header-mobile .dropdown .dropdown-menu .menu-header a.button--default {
        width: initial !important;
        float: right;
        padding: 8px 16px;
        height: 40px;
    }

.header-mobile .dropdown .dropdown-menu .menu-top ul,
.header-mobile .dropdown .dropdown-menu .menu-bottom ul {
    margin: 0;
    list-style-type: none;
    padding: 10px 18px 10px 18px;
}
.header-mobile .dropdown .dropdown-menu .menu-top ul li,
.header-mobile .dropdown .dropdown-menu .menu-bottom ul li{
    padding:10px 0;
}

.header-mobile .dropdown .dropdown-menu .menu-top ul li a,
.header-mobile .dropdown .dropdown-menu .menu-bottom ul li a{
  font-family: Hind, sans-serif;
  font-size: 16px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #ffffff;
  position:relative;
}

.header-mobile .sub_menu-border{
    background-color:#fff;
}


.header-mobile .dropdown-menu .menu-bottom{
    float: left;
    width: 100%;
}
.header-mobile .dropdown-menu .menu-bottom ul:first-child{
        background-color: #223035;
}

.header-mobile .dropdown-menu .menu-bottom ul{
    margin:0;
    list-style-type:none;
    padding:20px;
    margin-top:10px;
    float: left;
    width: 100%;
   
}

.header-mobile .dropdown-menu ul li.active .sub_menu-border {
    width: 100%;
}
.header-mobile .dropdown-menu .menu-bottom .lang li{
    width:initial;
    float:left;
}

.header-mobile .dropdown-menu .menu-bottom .lang li a{
  width:initial;
  float:left;
  font-family: Hind, sans-serif;
  font-size: 14px !important;
  font-weight: 300;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #ffffff;
  padding-right:10px;
  margin-right:10px;
}
.header-mobile .dropdown-menu .menu-bottom .lang li:first-child a{
    border-right: 1px solid rgb(167 175 180 / 20%);
}
.header-mobile .dropdown-menu .menu-bottom .lang li a.active{
    font-weight:bold;    
}
.header-mobile .dropdown-menu ul li>div:first-child a::before {
    background: url(../img/sub_menu_arrow.svg) no-repeat;
    content: '';
    width: 8px;
    height: 14px;
    margin-top: 4px;
    right: 25px;
    float:right;
}
.header-mobile .dropdown-menu ul.lang li::before{
    background:none !important;
    content:initial;
}
.header-mobile .dropdown-menu ul .sub_menu-icon>div:first-child a:before {
    transform: rotate(90deg);
    transition: 0.4s;
}

    .header-mobile .dropdown-menu ul .sub_menu-icon.active > div:first-child a:before {
        transform: rotate(-90deg);
    }
.header-mobile .dropdown-menu .sub_menu-box{
    display:none;
}

.header-mobile .dropdown-menu ul li.active .sub_menu-box ul li {
    border-bottom: 1px solid rgba(167, 175, 180, 0.2);
}
.sub_menu-box ul li::before {
    content: none !important;
}
.footer-top,
.footer-center,
.footer-bottom{
    float:left;
    width:100%;
}

.footer-top .whatsapp{
    float:left;
    display:block;
  }
.footer-top .whatsapp img{
    float:left;
}
.footer-top .whatsapp-text{
  font-family: Hind, sans-serif;
  font-size: 12px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 16px;
  letter-spacing: normal;
  text-align: center;
  color: #ffffff;
  float:left;
  margin-top:6px;
  border-radius:0 4px 4px 0;
  padding:4px 11px;
}
.footer-top .whatsapp-text span{
    font-weight: 500;
    display:block;
}

.footer-top ul{
    list-style-type:none;
    padding:0;
    margin:0;
    float:left;
    padding-left:80px;
}

.footer-top ul li{
    float:left;
    padding:10px 24px;
}
.footer-top ul li a{
  float:left;
  font-family: Hind, sans-serif;
  font-size: 14px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: right;
  color: #ffffff;
  position:relative;
}

.footer-top ul li:hover a .menu-border {
    width: 100%;
}

.footer-center{
    margin-top:20px;
}
.footer .cagri-merkezi {
  float:left;
  font-family: Hind, sans-serif;
  font-size: 12px;
  font-weight: 300;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #f5f5f5;
}
.footer .cagri-merkezi a {
    font-family: Hind, sans-serif;
    font-size: 12px;
    font-weight: 300;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    color: #f5f5f5;
}
.footer .cagri-merkezi strong{
    display:Block;
    font-weight: bold;
    color: #ffd522;
    float:left;
    border-bottom:1px solid #ffd522;
    margin:4px 3px 0;
    line-height:13px;
}
.footer .cagri-merkezi img{
    float:left;
    margin-right:5px;
}
.footer .social{
    float:right;
}

.footer .social a{
    padding:0 7px;
}
.social a:hover{
    opacity:.7;
}

.footer .social p{
    padding:0 7px;
    font-family: Hind, sans-serif;
  font-size: 12px;
  font-weight: 300;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #f5f5f5;
  float:left;
  margin-top:3px;
}

.footer-bottom{
     border-top: 1px solid rgb(167 175 180 / 20%);
     height:68px;
}
.footer-bottom ul{
    float:left;
    margin:0;
    padding:0;
    list-style-type: none;
}
.footer-bottom ul li{
    float:left;
    padding:18px 0 0; 
}
.footer-bottom ul li a{
  font-family: Hind, sans-serif;
  font-size: 12px;
  font-weight: 300;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #f5f5f5;
  display:block;
  padding-right:30px;
  border-right:1px solid rgb(167 175 180 / 20%);
  margin-right:30px;
}
.footer-bottom ul li a:hover{
opacity:.7;}

.footer-bottom ul li a:last-child{
    border:None;
}

.footer-bottom p{
  float:right;
  font-family: Hind, sans-serif;
  font-size: 12px;
  font-weight: 300;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #f5f5f5;
  margin-top:20px;
}

.footer-bottom p a{
  font-weight: bold;
  color: #ffd522;
  border-bottom:1px solid #ffd522;
  margin-left:5px;
}
.footer-mobile{
    display:none;
}

.homepage-search-box{
    position:absolute;
    z-index:43;
    width:100%;
    top:122px;
}

.search-input-radius input{
    border-radius: 30px;
    height:50px;
    padding: 10px 10px 10px 100px;
}
.search-input-radius:before{
    border-radius: 30px;
    box-shadow: 0 5px 20px 5px rgb(52 52 52 / 5%);
    background-color: #ffffff;
    width: 80px;
    height: 50px;
    background-position: center;
    margin:0;
}
.search-input-radius.show:before {
    background: url(../img/search-input-icon-yellow-20.svg) no-repeat;
    background-position: center;
}
.search-input-radius.show input{
border-radius: 30px 30px 0 0;
}
.search-input-radius .dropdown-menu{
    border-top:none;
}

.btn-hashtag{
  display:block;
  font-family: Hind, sans-serif;
  font-size: 14px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  float:left;
  color: #486072;
  padding: 10px 27px 7px 27px;
  border-radius: 20px;
  box-shadow: 0 5px 20px 5px rgba(52, 52, 52, 0.05);
  background-color: #ffffff;
  margin-right:15px;
}
.btn-hashtag:hover{
    color: #486072;
    opacity:.8;
}

.btn-hashtag img{
    float:left;
    margin-right:8px;
    margin-top: 2px;
}

.homepage-link-box{
    margin-top:60px;
    margin-bottom:30px;
    float:left;
    width:100%;
}
.homepage-link-box a{
  padding: 15px 40px 8px;
  border-radius: 12px;
  box-shadow: 0 5px 20px 5px rgba(52, 52, 52, 0.05);
  display:block;
  font-family: Hind, sans-serif;
  font-size: 16px;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
      line-height: 260px;
  letter-spacing: normal;
  text-align: center;
  color: #486072;
  height:168px;
  margin-bottom:15px;  
}
.homepage-link-box a:hover{
     box-shadow: 0 5px 20px 5px rgba(52, 52, 52, 0.18)
}
.homepage-link-box a.yasal_bildirimler{
     background: #ffffff url(../img/yasal_bildirimler.svg) no-repeat;
     background-position: center 15px;
}
.homepage-link-box a.basin_aciklamasÄ±{
     background: #ffffff url(../img/np-press-release.svg) no-repeat;
     background-position: center 26px;
}
.homepage-link-box a.ihaleler{
     background: #ffffff url(../img/ihaleler.svg) no-repeat;
     background-position: center 20px;
}
.homepage-link-box a.yeni_baglanti{
     background: #ffffff url(../img/yeni_baglanti.svg) no-repeat;
     background-position: center 20px;
}

.homepage-box-group{
    float: left;
    width: 100%;
    margin-top: -83px;
}
.homepage-box-group .homepage-box-item{
  padding: 20px 20px;
  border-radius: 12px;
  box-shadow: 0 5px 20px 5px rgba(52, 52, 52, 0.05);
  background-color: #ffffff;
  min-height:290px;
  margin-bottom:20px;
  position: relative;
  z-index: 2;
}
.homepage-box-group .col-lg-6:nth-child(1),
.homepage-box-group .col-lg-6:nth-child(3),
.homepage-bilgilendirme .col-lg-6:nth-child(1) {
    padding-right:0;
}

.homepage-box-item.bg-elektrik-Kesintisi:after{
   background: url(../img/home-elektrik-kesintisi-sorgulama.svg) no-repeat;
   position: absolute;
   content: '';
   width: 120px;
   height: 130px;
   left: 0;
   bottom: 0;
   z-index:-1;
}
.homepage-box-item.bg-online-islemler:after{
   background: url(../img/home-online-islemler.svg) no-repeat;
   position: absolute;
   content: '';
   width: 137px;
   height: 149px;
   left: 0;
   bottom: 0;
    z-index:-1;
}
.homepage-box-item.bg-basvuru-takibi:after{
   background: url(../img/home-basvuru-takibi.svg) no-repeat;
   position: absolute;
   content: '';
  width: 133px;
   height: 125px;
   left: 0;
   bottom: 0;
    z-index:-1;
}
.homepage-box-item.bg-sss:after{
   background: url(../img/home-sss.svg) no-repeat;
   position: absolute;
   content: '';
   width: 113px;
   height: 154px;
   left: 0;
   bottom: 0;
    z-index:-1;
}


.homepage-bilgilendirme{
    float: left;
    width: 100%;
    margin-top:85px;
}
.homepage-bilgilendirme:before{
     width: 45%;
  height: 510px;
  margin: 0 0 19px 84px;
  padding: 0 0 64px 48px;
  border-radius: 243px 0 0;
  content:'';
      background-image: linear-gradient(to bottom, rgb(244 236 194 / 95%), rgba(23, 58, 71, 0) 100%);

  position: absolute;
    right: 0;
    margin-top: -25px;
}
.homepage-bilgilendirme .homepage-bilgilendirme-item{
  padding: 20px 20px;
  border-radius: 12px;
  box-shadow: 0 5px 20px 5px rgba(52, 52, 52, 0.05);
  background-color: #ffffff;
  min-height:280px;
  margin-bottom:20px;
}
.homepage-bilgilendirme-item .container-detail---shadow-box{
    padding:15px;    
}
.homepage-bilgilendirme-item .bulten-box--list:before{
    background:none;
}

.homepage-box-item .title,
.homepage-bilgilendirme-item .title,
.homepage-bizden-haberler .title{
    position:relative;
    z-index:1;
    padding-left:10px;
    float:left;
    color:#173a47;
}
.homepage-box-item .title:before,
.homepage-bilgilendirme-item .title:before{
  width: 24px;
  height: 24px;
  content:'';
  opacity: 0.5;
  border-radius: 2px;
  box-shadow: 0 2px 64px 0 #ffd522;
  background-color: #ffd522;
  position:absolute;
  z-index: -1;
  margin-left: -10px;
  margin-top: -1px;
}

.homepage-seo {
    float: left;
    width: 100%;
    margin-top: 30px;
}
.homepage-seo .homepage-bilgilendirme-item {
    padding: 20px 20px;
    border-radius: 12px;
    box-shadow: 0 5px 20px 5px rgba(52, 52, 52, 0.05);
    background-color: #ffffff;
    margin-bottom: 20px;
}
    .homepage-seo .homepage-bilgilendirme-item details[open] > summary {
        display: none;
    }
        .homepage-seo .homepage-bilgilendirme-item details[open] > div {
            animation-name: slideDown;
            animation-duration: 0.3s;
            animation-fill-mode: forwards;
            display: flex;
            flex-wrap: wrap;
            max-height: 550px;
            overflow: auto;
            margin-bottom: 20px;
        }
    .homepage-seo .homepage-bilgilendirme-item .detay-link {
        margin-bottom: 0px;
    }
    .homepage-seo .homepage-bilgilendirme-item .detay-link:after {
        transform: rotate(90deg);
    }
        .homepage-seo .homepage-bilgilendirme-item .detay-link.reverse:after {
            transform: rotate(-90deg);
        }
    .homepage-seo .homepage-bilgilendirme-item details[open] > div::-webkit-scrollbar {
        width: 6px;
    }

    .homepage-seo .homepage-bilgilendirme-item details[open] > div::-webkit-scrollbar-track {
        background: #e5ebf0;
        border-radius: 3px;
    }

    .homepage-seo .homepage-bilgilendirme-item details[open] > div::-webkit-scrollbar-thumb {
        background: #a7afb4;
        border-radius: 3px;
    }

        .homepage-seo .homepage-bilgilendirme-item details[open] > div::-webkit-scrollbar-thumb:hover{
            background: #555;
        }
@keyframes slideDown {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;

    }
}
.homepage-bizden-haberler .title:before {
    width: 33px;
    height: 33px;
    content: '';
    opacity: 0.5;
    border-radius: 2px;
    box-shadow: 0 2px 64px 0 #ffd522;
    background-color: #ffd522;
    position: absolute;
    z-index: -1;
    margin-left: -10px;
    margin-top: -5px;
}

.homepage-box-item .detay-link,
.homepage-bilgilendirme-item .detay-link {
    z-index: 2;
    position: relative;
}
.homepage-bilgilendirme-item .col-lg-6, .d-flex-center{
     display: flex; 
     align-items: center;
}
.homepage-bilgilendirme-item .col-lg-6 p{
    margin-bottom:0;
}
.homepage-box-item--body{
    padding:0 10px 10px 10px;
    margin-top:10px;
}
.btn-black{
  border-radius: 16px;
  background-color: #89979e;
  font-family: Hind, sans-serif;
  font-size: 16px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  letter-spacing: normal;
  text-align: center;
  color: #c4cbce;
  width:100%;
  display:Block;
  line-height:46px;
  height:46px;
}
.btn-black:hover{
    color: #c4cbce;
 }
.btn-black.active{
 opacity: 1;
 color:#fff;
 background:#173a47;
}

.homepage-box-item--body .item{
  padding:15px 0 0 13px;
  border-radius: 4px;
  box-shadow: 0 5px 20px 5px rgba(52, 52, 52, 0.05);
  background-color: #ffffff;
  min-height:141px;
  display:Block;
}
.homepage-box-item--body .item:hover{
      box-shadow: 0 5px 20px 5px rgba(52, 52, 52, 0.18);
}
.homepage-box-item--body .item h5{
  font-family: Hind, sans-serif;
  font-size: 14px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #486072;
  margin-top: 3px;
    margin-bottom: 0;
}
.homepage-box-item--body .item small{
 font-family: Hind, sans-serif;
  font-size: 10px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.3;
  letter-spacing: normal;
  color: #94bc4f;
}
.homepage-box-item--body .item p{
    margin-bottom:0;
}
.homepage-box-item--body .item .lock{
  width: 28px;
  height: 38px;
  padding: 7px 4px 11px;
  border-radius: 0  0 50% 50%;
  background-color: #94bc4f;
  position:absolute;
  right:18px;
  top:0;
}
.homepage-box-item--body .row{
    margin-left:-6px;
    margin-right:-6px;
}
.homepage-box-item--body .col-lg-4{
    padding-left:6px;
    padding-right:6px;
}


.form-group.floating>label {
    bottom: 34px;
    left: 8px;
    position: relative;
    padding: 0px 5px 0 5px;
    font-size: 14px;
    transition: 0.1s;
    pointer-events: none;
    transform-origin: bottom left;
}
.form-control.floating:focus ~ label {
    transform: translate(1px,-45%) scale(.80);
    color: #486072;
    font-weight: bold;
    font-size: 10px;
}

.form-control.floating[data-value]:not([data-value=""]) ~ label {
    transform-origin: bottom left;
    transform: translate(1px,-45%) scale(.80);
    color: #486072;
    font-weight: bold;
    font-size: 10px;
}
input:disabled+label {
    color: #a7afb4    ;
     bottom: 43px !important;
    font-size: 10px !important;
    font-weight: 600;
  }
  input:disabled{
    color: #a7afb4 !important;
    border-color:#eff3f6!important;
    background-color:#eff3f6!important;
    box-shadow: none !important;
}

.text-input-full input{
    height: 46px;
  border-radius: 8px;
  box-shadow: 0 0 15px 0 rgba(116, 126, 134, 0.2);
  background-color: #ffffff;
  border:1px solid #fff;
  font-family: Hind, sans-serif;
  font-size: 14px;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
  letter-spacing: normal;
   color: #173a47;
   padding-top:20px;
}

.text-input-full label{
  font-family: Hind, sans-serif;
  font-size: 14px;
  font-weight: 300;
  font-stretch: normal;
  font-style: normal;
  letter-spacing: normal;
  color:#a7afb4;
}

.text-input-full input[type="text"]:focus,
.text-input input[type="text"]:focus{
 box-shadow: 0 0 15px 0 rgba(116, 126, 134, 0.2) !important;
 border: solid 1px #ffc522 !important;
}
.text-input-full .warning{
    position: absolute;
    right: 30px;
    top: 13px;
}

.text-input input{
  height: 46px;
  border-radius: 8px;
  box-shadow: 0 0 15px 0 rgba(116, 126, 134, 0.2);
  background-color: #ffffff;
  border:1px solid #fff;
  font-family: Hind, sans-serif;
  font-size: 14px;
  font-weight: 300;
  font-stretch: normal;
  font-style: normal;
  letter-spacing: normal;
   color: #a7afb4;
}
.text-input input::placeholder{
    color:#a7afb4;  
    font-size:15px;
}
.homepage-box-item .mr-10{
    margin-right:10px;
}
.homepage-box-item .mr-20{
    margin-right:20px;
}

.homepage-bizden-haberler{
    margin-top:50px;
}
.homepage-bizden-haberler,
.homepage-bizden-haberler-bg{
    height:510px;
    width:100%;
    float:left;
}
    .homepage-bizden-haberler-bg:before {
        width: 40%;
        height: 510px;
        content: '';
        background-image: linear-gradient(to right, rgb(255 240 159) 0%, rgba(23, 58, 71, 0) 100%);
        position: absolute;
        left: 0;
        z-index: -1;
    }
    .homepage-bizden-haberler:before {
        opacity: 0.3;
        background-image: radial-gradient(circle, #607d8b 0%, rgb(245 247 249) 100%);
        border-radius: 0 243px 243px 0;
        width: 70%;
        height: 510px;
        z-index: 3;
        content: '';
        position: absolute;
    }

.homepage-bizden-haberler .title{
    color:#173a47;
    margin-top:34px;
    z-index:12;
}
.carousel-bizdenhaberler{position:absolute;z-index:5 !important; }
    .carousel-bizdenhaberler.owl-drag .owl-item {
        padding: 20px 0px 20px 20px;
        border-radius: 8.9px;
        width: 300px;
        background: #fff;
        min-height: 250px
    }
.carousel-bizdenhaberler .owl-stage .owl-item{margin-top:100px !important;}
.carousel-bizdenhaberler .owl-stage .owl-item.img-large{margin-top:20px !important; bottom:-20px;}
.owl-stage{display:flex !important;}
.carousel-bizdenhaberler .text {
    position: absolute;
    font-family: Hind, sans-serif;
    padding: 60px 70px 33px 33px;
    font-stretch: normal;
    font-style: normal;
    letter-spacing: normal;
    color: #00888a;
}
    .carousel-bizdenhaberler .text h3 {
        font-size: 20px;
        font-weight: bold;
        min-height: 32px;
    }
    .carousel-bizdenhaberler .text p {
        font-size: 14px;
        font-weight: normal;
        color: #747e86;
        margin-bottom: 10px;
    }
.carousel-bizdenhaberler .owl-stage .owl-item.img-large .text {
    padding-left: 16%;
    padding-top: 100px;
    width:100%;
    height: 100%;
}

    .carousel-bizdenhaberler .owl-stage .owl-item.img-large .text h3 {
        font-size: 24px;
    }

    .carousel-bizdenhaberler .owl-stage .owl-item.img-large .text p {
        font-size: 18px;
    }

    .carousel-bizdenhaberler .owl-stage .owl-item.img-large .text .detay-link {
        font-size: 18px;
    }

    .carousel-bizdenhaberler .owl-stage .owl-item.img-large .text .detay-link:before {
        margin-top: 6px;
    }

.owl-nav {overflow-x:hidden; margin-top:-450px;width:100%;}
.owl-nav button{margin-top:260px;z-index:2}
    .owl-nav .owl-prev {
        width: 40px;
        height: 80px;
        padding: 30px 6px 30px 0px;
        -webkit-backdrop-filter: blur(4px);
        backdrop-filter: blur(4px);
        background-color: rgba(0, 0, 0, 0.1) !important;
        border-top-right-radius: 40px;
        border-bottom-right-radius: 40px;
        opacity: 1 !important;
        z-index: 23;
        left: 0;
    }

    .owl-nav .owl-next {
        width: 40px;
        height: 80px;
        padding: 30px 0px 30px 14px;
        -webkit-backdrop-filter: blur(4px);
        backdrop-filter: blur(4px);
        background-color: rgba(0, 0, 0, 0.1) !important;
        border-top-left-radius: 40px;
        border-bottom-left-radius: 40px;
        position: absolute;
        z-index: 23;
        right: 0;
    }
.owl-nav .owl-next span{
    background: url(../img/menu-arrow-right.svg) no-repeat;
    width: 20px;
    height: 20px;
    display:block;
    text-indent:-9999px;
    margin-left:15px;
}
.owl-nav .owl-prev span{
    background: url(../img/menu-arrow-right.svg) no-repeat;
    width: 20px;
    height: 20px;
    display:block;
    text-indent:-9999px;
    transform: rotate(-180deg);
}
.owl-nav .owl-next:hover,
.owl-nav .owl-prev:hover{
   background: linear-gradient(180deg, #ffc522 1%, #ff8000 100%) !important;
}

.carousel-bizdenhaberler .date {
    position: absolute;
    top: 30px;
    right: 16px;
    width: 60px;
    display: flex;
    flex-direction: column;
    text-align: center;
}

.carousel-bizdenhaberler .date strong {
    transform: rotate(-360deg);
    opacity: 0.4;
    -webkit-text-stroke: 2.5px #747e86;
    font-family: Hind, sans-serif;
    font-size: 48px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    letter-spacing: normal;
    text-align: center;
    color: rgba(0, 136, 138, 0);
    line-height: 25px;
}

    .carousel-bizdenhaberler .date span {
        opacity: 0.4;
        font-family: Hind, sans-serif;
        font-size: 16px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        letter-spacing: normal;
        color: #747e86;
    }

    .carousel-bizdenhaberler .date:before {
        width: 24px;
        height: 24px;
        content: '';
        opacity: 0.3;
        border-radius: 2px;
        box-shadow: 0 2px 64px 0 #ffd522;
        background-color: #ffd522;
        position: absolute;
        z-index: -1;
        margin-top: -13px;
    }

.div-table-border{
    float:left;
    width: 100%;
    overflow: hidden;
}
.div-table-border .width-40{
    width: 40%;
    float:left;
}
.div-table-border .width-60{
    width: 60%;
    float:left;
}
.div-table-border--header{
    font-family: Hind, sans-serif;
    font-size: 14px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.43;
    letter-spacing: normal;
    color: #747e86;
    border-bottom: 1px solid #a7afb4;
    float: left;
    width: 100%;
    margin-bottom: 10px;
}
.div-table-border--body{
    float: left;
    width: 100%;
    padding: 0 10px 10px 0;
    border-radius: 8px;
    border: solid 1px #a7afb4;
}
.div-table-border--body .label{
    float: left;
    width: 100%;
}
.div-table-border--body .label span{
    border-radius: 8px 0 8px 0;
    background-color: #173a47;
    font-family: Hind, sans-serif;
    font-size: 12px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    color: #ffffff;
    padding: 4px 10px;
    display: block;
    width: 38%;
 }
.div-table-border--body .width-40,
.div-table-border--body .width-60 {
    font-family: Hind, sans-serif;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.36;
    letter-spacing: normal;
    color: #747e86;
    padding: 5px 10px;
}
.div-table-border--body .width-40:nth-child(1),
.div-table-border--body .width-60:nth-child(2) {
    margin-top:10px;
}
.div-table-border--body .width-40{
    border-right: 1px solid rgba(167, 175, 180, 0.20);
}
.border-box--shadow.hover-hidden{
    box-shadow: 0 5px 20px 5px rgba(52, 52, 52, 0.1);
}
.accordion-open--reset .card .card-body:after{
    background:none;
}
.accordion-open--reset .card .card-header{
    margin:0;
}
.accordion-open--reset .card .card-body{
    background: transparent;
    border:none;
}
.list-number-content{
    float:left;
    width: calc(100% - 50px);
}

.list-number li .list-default li{
    margin-bottom:initial;
 }
 .list-number li .list-default li:before{
    content:initial;
 }
 .box-blau{
   padding: 10px 10px 20px;
  border-radius: 8px;
  background-color: #173a47
 }

 .site_haritasi_title{
     font-family: Hind, sans-serif;
     font-size: 20px;
     font-weight: bold;
     font-stretch: normal;
     font-style: normal;
     line-height: 1;
     letter-spacing: normal;
     color: #173a47;
     border-bottom:1px solid #173a47;
     height:40px;
     position:relative;
     cursor:default;
     word-break: break-all;
 }
 .site_haritasi_title a{
    color: #173a47;
    position:relative;
 }
 
 .site_haritasi,
 .site_haritasi .site_haritasi_sub_menu ul{
    list-style-type:none;
    padding:0;
 }
 .site_haritasi_padding{
      margin-bottom:50px;
 }
 .site_haritasi .page_title{
    background: url(../img/icons-general-mark.png) no-repeat;
    background-size: 20px;
    background-position: 0 2px;
    padding-left: 28px;
    margin-bottom:20px;
 }
 .site_haritasi_title .menu-border{
      top:initial;
 }
  .site_haritasi .page_title .page_title_link{
  position: relative;
  font-family: Hind, sans-serif;
  font-size: 16px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.25;
  letter-spacing: normal;
  color: #486072;
 }
 .site_haritasi_title .menu-border,
 .site_haritasi .menu-border{
    background: #173a47;
 }
 .site_haritasi li:hover a .menu-border,
 .site_haritasi_title a:hover .menu-border{
    width: 100%;
    background:#ffc522;
}

.site_haritasi .page_title .site_haritasi_sub_menu ul li{
    background: url(../img/list-marker.svg) no-repeat;
    background-size: 11px;
    background-position: 0 6px;
    padding-left: 20px;
    margin-bottom:15px;
 }
 .site_haritasi .page_title .site_haritasi_sub_menu ul li a{
  position: relative;
  font-family: Hind, sans-serif;
  font-size: 14px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.43;
  letter-spacing: normal;
  color: #747e86;
  transition: width 1s;
  display:inline-block;
 }
 .site_haritasi .page_title .site_haritasi_sub_menu ul li:hover{
    background: url(../img/list-marker-yellow.svg) no-repeat;
    background-size: 11px;
    background-position: 0 6px;
 }
  .site_haritasi .page_title .site_haritasi_sub_menu ul li:hover a{
    color:#ffc522;
    transition: width 1s;
 }

 .site_haritasi_padding .col-lg-3{
      padding-right:30px ;
 }
 .site_haritasi .lock{
  width: 15px;
  height: 20px;
  padding: 0px 2px 0px;
  border-radius: 0  0 50% 50%;
  background-color: #94bc4f;
  float:right;
  margin-left:10px;
 }
 .site_haritasi .lock img{
    width:10px;
 }
 .site_haritasi .site_haritasi_sub_menu small{
 font-family: Hind, sans-serif;
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.08;
  letter-spacing: normal;
  color: #94bc4f;
  display:block;
  margin-top:10px;
 }

.footer_cerezpolitikasi {
    padding: 20px 20px;
    background-color: #0a191f;
    min-height: 81px;
    width: 100%;
    position: fixed;
    bottom: 0;
    z-index: 9999;
}
  .footer_cerezpolitikasi p,.footer_cerezpolitikasi p a{
     font-size: 14px;
     font-weight: 600;
     font-stretch: normal;
     font-style: normal;
     line-height: 1.5;
     letter-spacing: normal;
     color: #ffffff;
  }
  .footer_cerezpolitikasi p a{
    border-bottom:2px solid #fff;
    border-radius:2px;
  }
  .footer_cerezpolitikasi p{
    margin:0;
  }

    .footer_cerezpolitikasi button {
        color: #ffffff;
        border: 1px solid #ffffff;
        padding: 0 20px 0 20px;
        margin-top: 10px;
    }

    .footer_cerezpolitikasi button:hover {
        opacity: 0.7;
    }

  .btn-memnuniyet-anketi {
    transform: rotate(270deg);
    min-height: 41px;
    width: 153px;
    border-radius: 18px 18px 0px 0px;
    box-shadow: 0 4px 15px 4px rgba(52, 52, 52, 0.1);
    background-image: linear-gradient(165deg,#ff8000, #ffc522 100%);
    position:fixed;
    right:-56px;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    color:  #fff;
    text-align:Center;
    padding: 12px 0 8px 0;
    cursor:pointer;
    border:none;
    bottom:30%;
     -webkit-transition: height .35s ease-in-out;
    transition: height .35s ease-in-out;
    z-index:9999;
  }

  .btn-memnuniyet-anketi:hover{
   box-shadow: 0 4px 15px 4px rgba(52, 52, 52, 0.1);
   background-image: linear-gradient(165deg, #ffa040, #f7cf5f 100%)
  }
  .btn-memnuniyet-anketi.active{
    box-shadow: 0 4px 15px 4px rgba(52, 52, 52, 0.1);
    background-image: linear-gradient(158deg, #ff8000, #ffc522 100%);
    z-index:9999;
    height:61px;
    bottom:calc(34% - 10px);
    padding: 18px 0 16px 0;
    -webkit-transition: height .35s ease-in-out;
    transition: height .35s ease-in-out;
    right: -45px;
  }

  #modal-memnuniyet-anketi .modal-dialog,
  #modal-homepage .modal-dialog{
     max-width:700px;
  }
  #modal-memnuniyet-anketi .modal-content{
    border-radius: 12px;
    box-shadow: 0 5px 20px 5px rgba(52, 52, 52, 0.1);
    background-color: #ffffff;
    border:None;
    padding:15px;
  }
  .modal-backdrop.show{
    background-color: rgba(35, 31, 32, 0.75);
    opacity:1;
    z-index:9998;
  }

  #modal-memnuniyet-anketi .modal-header,
  #modal-homepage .modal-header {
     border:none;
  }
  #modal-memnuniyet-anketi .modal-header h5,
  #modal-homepage h5{
       border:none;
       font-family: Hind, sans-serif;
       font-size: 24px;
       font-weight: bold;
       font-stretch: normal;
       font-style: normal;
       line-height: normal;
       letter-spacing: normal;
       color: #173a47;
       position: relative;
       z-index: 1;
       padding-left: 10px;
       float: left;
  }
  #modal-memnuniyet-anketi .modal-header h5:before,
  #modal-homepage h5:before {
    width: 24px;
    height: 24px;
    content: '';
    opacity: 0.5;
    border-radius: 2px;
    box-shadow: 0 2px 64px 0 #ffd522;
    background-color: #ffd522;
    position: absolute;
    z-index: -1;
    margin-left: -10px;
    margin-top: -2px;
}
  .modal-header .close{
        opacity:1;
  }

  .step-bar{
    margin:0;
    padding:0;
    list-style-type:none;
  }

  
  .step-bar li{
   float:left;
   width:25%;  
    padding:0 6px;
  }
   .step-bar li:first-child{
   padding-left:0;
  }
   .step-bar li:last-child{
   padding-right:0;
  }
  .step-bar li a{
  display:Block;
   border-radius: 5px;
   background-color:#a7afb4;
   margin-top:4px;
    height:6px;
   
  }
  .step-bar li.active a, .step-bar li.filled a:hover{
   height:10px;
   background-color:#173a47;
   margin:0;
   cursor:pointer;
  }

  .step-content{
        float:left;
        width:100%;
  }
  .step-content .item{
    display:none;
  }

  #modal-memnuniyet-anketi .step-content h4{
   font-size: 16px;
  font-weight: 600;
  text-align: center;
  color: #173a47;
  margin-top:45px;
  }

   #modal-memnuniyet-anketi .step-content .survey{
   list-style-type:none;
   padding:0;
   float: left;
   width: 100%;
   justify-content: center;
   display: flex;
   height:90px;
   margin-top:30px;
  }
   
   #modal-memnuniyet-anketi .step-content .survey li{
   float:left;
    width:100px;
     justify-content: center;
    display: flex;
  }
  #modal-memnuniyet-anketi .step-content .survey li a{
    box-shadow: 0 5px 20px 5px rgb(52 52 52 / 15%);
    background:#fff;
    border-radius:50%;
    padding:10px;
    cursor:pointer;
    width: 60px;
    height: 60px;
    float: left;
  }
  #modal-memnuniyet-anketi .step-content .survey li a.active{
    background:#ff9500;
  }
  #modal-memnuniyet-anketi .step-content .survey li a:hover{
        width: 80px;
    height: 80px;
     padding:13px;
  }
   #modal-memnuniyet-anketi .step-content .survey li a:hover img{
   height:53px;
  }
  #modal-memnuniyet-anketi .step-content .survey li a.active img.img-1{
   content:url("../img/survey1-hover.svg");
  }
    #modal-memnuniyet-anketi .step-content .survey li a.active img.img-2{
   content:url("../img/survey2-hover.svg");
  }
    #modal-memnuniyet-anketi .step-content .survey li a.active img.img-3{
   content:url("../img/survey3-hover.svg");
  }
    #modal-memnuniyet-anketi .step-content .survey li a.active img.img-4{
   content:url("../img/survey4-hover.svg");
  }
    #modal-memnuniyet-anketi .step-content .survey li a.active img.img-5{
   content:url("../img/survey5-hover.svg");
  }
   #modal-memnuniyet-anketi .step-content .survey li span{
   display:Block;
   font-size: 14px;
  font-weight: 600;
  color: #0a191f;
   margin-top: 15px;
    text-align: center;
  }
   #modal-memnuniyet-anketi .step-content .item  .form-row{
    width:60%;
    margin-left: 25%;
  }
    #modal-memnuniyet-anketi .step-content .item  .form-row-textarea{
    width: 66%;
    margin-left: 17%;
  }
.input-checkbox {
    display: block;
    position: relative;
    padding-left: 28px;
    cursor: pointer;
    font-family: Hind, sans-serif;
    font-size: 14px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.43;
    letter-spacing: normal;
    color: #fff;
    padding-top: 2px;
	text-shadow: 1px 1px 2px black;
}


/* Hide the browser's default checkbox */
.input-checkbox input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}
/* Create a custom checkbox */
.checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 20px;
    width: 20px;
     border-radius: 1px;
    border: 2px solid #fff;
}

/* On mouse-over, add a grey background color */
.input-checkbox:hover input ~ .checkmark {
    background-color: #fff;
}
/* When the checkbox is checked, add a blue background */
.input-checkbox input:checked ~ .checkmark {
    background-image: linear-gradient(135deg, #486072, #173a47 99%);
}
/* Create the checkmark/indicator (hidden when not checked) */
.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}
/* Show the checkmark when checked */
.input-checkbox input:checked ~ .checkmark:after {
    display: block;
}
/* Style the checkmark/indicator */
.input-checkbox .checkmark:after {
    left: -2px;
    top: -2px;
    width: 20px;
    height: 20px;
    background:url(../img/input-checkbox.svg)
} 
.input-textarea{
    padding: 12px 12px;
    border-radius: 8px;
    box-shadow: 0px 0px 15px 0px rgba(116,126,134,0.2);
    background-color: #ffffff;
    border: solid 1px #fff;
    resize: none;
    font-family: Hind, sans-serif;
    font-size: 14px;
    font-weight: 600;
    min-height: 90px;
    color: #173a47;
    margin-bottom: 10px;
}
.input-textarea::placeholder{
    font-weight: 300;
      color:#a7afb4;
}
.input-textarea:focus{
  box-shadow: 0 0 15px 0 rgba(116, 126, 134, 0.2) !important;
 border: solid 1px #ffc522 !important;
}

.modal{
    z-index:9999;
}


#modal-memnuniyet-anketi .survey-success,
#modal-memnuniyet-anketi .survey-info,
#modal-homepage .modal_homepage_content{
    float: left;
    width: 100%;
    display:none;
}
#modal-memnuniyet-anketi .survey-success .image,
#modal-memnuniyet-anketi .survey-info .image,
.search-no-results-found .image,
#modal-homepage .modal_homepage_content .image{
    padding: 20px;
    box-shadow: 0 5px 20px 5px rgb(52 52 52 / 10%);
    background-color: #ffffff;
    width: 120px;
    border-radius: 50%;
    margin-left: calc(50% - 60px);
    margin-top: -45px;
    margin-bottom: 30px;
}
#modal-memnuniyet-anketi .survey-success h4,
#modal-memnuniyet-anketi .survey-info h4,
.search-no-results-found p{
 font-family: Hind, sans-serif;
  font-size: 16px;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  color: #173a47;
  }
  .search-no-results-found p{font-weight:500;font-size:18px;}

  .homepage-carousel{
        margin-top:10px;
        float:left;
  }
  .homepage-carousel .owl-item img {
    display: block;
    width: 100%;
    background-size: contain;
    object-fit: cover;
    border-radius:10px;
}

.slider-mask-mobile{
    width: 100%;
    background:url(../img/banner-mask-mobile.webp);    
    position: absolute;
    z-index: 1;
    left: 0;
    top: 0;
    border-radius: 10px;
    background-position: center center;
    background-size: cover;
}
  .homepage-carousel .owl-item h3 {
  font-family: Hind, sans-serif;
  font-size: 20px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: normal;
  color: #ffffff;
      position: absolute;
    top: 15%;
    left: 5%;
    width:60%;
    z-index: 2;
}
.homepage-carousel .owl-item p {
    font-family: Hind, sans-serif;
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.4;
    letter-spacing: normal;
    color: #ffffff;
}
.homepage_slider-mobile{
    display:none;
}

.homepage_slider-mobile .owl-dots,
.carousel-bilgilendirme .owl-dots{
    float: left;
    width: 100%;
    display: flex;
    justify-content: center;
    margin-top:10px;
}
.homepage_slider-mobile .owl-dots .owl-dot,
.carousel-bilgilendirme .owl-dots .owl-dot{
    width: 4px;
    height: 4px;
    opacity: 0.6;
    border-radius: 1px;
    box-shadow: 0 2px 64px 0 #ffd522;
    background-color: #a7afb4 !important;
    border: none;
    margin: 3px;
}

.homepage_slider-mobile .owl-dots .owl-dot.active ,
.carousel-bilgilendirme .owl-dots .owl-dot.active {
    width: 22px;
    height: 8px;
    margin: 0 3px;
    opacity: 0.7;
    border-radius: 4px;
    box-shadow: 0 2px 8px 0 #ffd522;
    background-color: #ffd522 !important;
}

.carousel-bilgilendirme{
    z-index:13 !important;
    display:none;
}
.mobile-show,
.mobile-show-600{
    display:none;
}
.tooltip {
  z-index:9999999 !important;
  font-size: 14px;
  font-weight: 600;
}
.tooltip-inner {
    background: #173a47;
     font-size: 14px;
  font-weight: 600;
}
.tooltip .arrow::before{
  border-bottom-color:#173a47 !important;
  border-top-color:#173a47 !important;
}
.page_content-hide{
    display:none;
}
.textSelected:first-line {
  background-color: rgba(255, 213, 34, 0.25);
  font-size: 16px;
  font-weight: 600;
  color: #173a47;
}
.ul-search-filter li{
    margin-right:30px;
}
.ul-search-filter li a{
  font-family: Hind, sans-serif;
  font-size: 14px;
  color: #a7afb4 !important;
  cursor:pointer;
}
.ul-search-filter li a.active{
color: #ffa300 !important;
  font-weight: bold;
  border-bottom:2px solid #ffa300;
  border-radius: 2px;
}
.before_transform-reset:before{
    transform: rotate(0) !important;
}
.link-arama-sonucu{
  width: 100%;
  display:block;
  padding: 19px 20px;
  border-radius: 8px;
  box-shadow: 0 5px 20px 5px rgba(52, 52, 52, 0.1);
  background-color: #ffffff;
  margin-bottom:20px;
  color: #0a191f;
   font-family: Hind, sans-serif;
  font-size: 14px;
  overflow:hidden;
}
.link-arama-sonucu img{
 float:left;
 margin-right:10px;
}
.link-arama-sonucu:hover{
  color: #0a191f;
  box-shadow: 0 5px 20px 5px rgb(52 52 52 / 18%);
}
.link-arama-sonucu:before{
    background: url(../img/arrow-right-gray.svg) no-repeat;
    transition: .2s;
    content: '';
    width: 11px;
    height: 20px;
    margin-right: 10px;
    float: right;
}
.link-arama-sonucu p {
    width: 100%;
    float: left;
    border-top: 1px solid rgba(167, 175, 180, 0.2);
    margin-top: 18px;
    padding-top: 7px;
    margin-bottom: 0;
}

.onemli_bilgilendirme{
  position:fixed;
  z-index:98;
  top:0;
  height: 114px;
  width:100%;
  font-family: Hind, sans-serif;
  font-weight: bold;
  color:#fff;
  overflow:Hidden;  
  padding: 14px 0 0 0;
  background: #0a191f url(../img/icons-general-info-large.svg) no-repeat;
  background-position: -27px -10px;
}

.onemli_bilgilendirme h3{
  font-size: 16px;
  
  }
.onemli_bilgilendirme p{
  color:#fff;
  font-size: 14px;
}
.onemli_bilgilendirme .detay-link{
  padding-top:5px;
}
.body-onemli_bilgilendirme .homepage_slider{
    margin-top:114px;
}
.body-onemli_bilgilendirme .homepage-header{
   top:114px;
}
.body-onemli_bilgilendirme .homepage-search-box{
   top:236px;
}
.onemli_bilgilendirme .close{
    position:absolute;
    top:-6px;
    right:15px;
    opacity:1;    
}
.detay-link-white{
    color:#fff;
}
.detay-link-white:hover{
    color:#fff;
}
.detay-link-white:after{
     background: url(../img/sub_menu_arrow.svg) no-repeat;
}
.detay-link-white:hover:after{
     background: url(../img/sub_menu_arrow-hover.svg) no-repeat !important;
}

.search-input ol, .search-inputArama ol {
    list-style-type: none;
    padding: 0 0 0 15px;
}
.search-input ol li, .search-inputArama ol li{
    font-family: Hind, sans-serif;
    font-size: 20px;
    color: #747e86;
    float: left;
    line-height: 20px;
    padding-left: 15px;
}
.search-input ol li a, .search-inputArama ol li a{
    color: #173a47 !important;
    font-family: Hind, sans-serif;
    font-size: 20px;
    font-weight: bold;
    display: block;
    border-right: 1px solid rgba(179, 194, 212, 0.2);
    padding-right: 15px;
    cursor: pointer;
}
.search-input ol li a:hover{
    opacity: .7;
}
.search-input ol li:last-child a{
    border:none;
}
.search-inputArama ol li a:hover {
    opacity: .7;
}

.search-inputArama ol li a:last-child {
    border: none;
}

.search-arama-link {
    color: #173a47 !important;
    font-family: Hind, sans-serif;
    font-size: 20px;
    font-weight: bold;
    border-right: 1px solid rgba(179, 194, 212, 0.2);
    padding-right: 15px;
    cursor: pointer;
}

.container_404{
    float:left;
    width:100%;
    padding:108px 0;
}
.container_404 img{
    width:100%;
}
.container_404 h1{
    color:#173a47;
    margin-top:100px;
}
.container_404 p{
font-family: Hind, sans-serif;
  font-size: 18px;
  letter-spacing: normal;
  color: #173a47;
}
.container_404 a{
  display:block;
  width: 280px;
  height: 46px;
  padding: 10px 81px 10px 82px;
  border-radius: 16px;
  background-image: linear-gradient(99deg, #173a47, #0c1f27 99%);
  color: #ffffff;
  font-family: Hind, sans-serif;
  font-size: 16px;
  margin-top:20px;
  float:left;
}
.container_404 a:hover{
    opacity:.7;
}
.container_404.pc{
    display:block;
}
.container_404.mobile{
    display:none;
}
.container_404.mobile h1, .container_404.mobile p{
    text-align:left;
}
.body_yapim_asamasi{
    background:#0c1f27 url(../img/yapim_asamasinda.svg) no-repeat;
    background-size:cover;
    overflow:hidden;
}
.container_yapim_asamasi h1{
  font-family: Hind, sans-serif;
  font-size: 50px;
  font-weight: bold;
  color:#ffffff;
  margin-top:46%;
}
.container_yapim_asamasi p{
    font-family: Hind, sans-serif;
  font-size: 20px;
   color:#fff;
    line-height: 1.5;
   
    float:left;
}
.container_yapim_asamasi .logo{
    margin-top:52px;
    display:block;
}

.title-marker{
 font-family: Hind, sans-serif;
  font-size: 14px;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.36;
  letter-spacing: normal;
  color: #486072;
  background: url(../img/icons-general-mark.png) no-repeat;
  background-size: 20px;
  padding-left:30px;
   float:left;
  width:100%;
}
.sub_title-marker{
  font-family: Hind, sans-serif;
  font-size: 14px;
  font-weight: 600;
  line-height: 1.36;
  letter-spacing: normal;
  color: #ffa300;
  background: url(../img/list-marker-yellow.svg) no-repeat;
  background-position: 0 3px;
  padding-left:20px;
  margin-left:30px;
  float:left;
  width:100%;
}
.th-width-28.table-default--mobile th{
    width:28% !important;
}

.box-item-yetkili-elektrikci-listesi .col-lg-6:first-child,
.box-elektrik-kesintisi-sorgulama .col-lg-6:first-child{
    border-right:1px solid rgba(167, 175, 180, 0.2);
}
.box-item-yetkili-elektrikci-listesi .item-yetkili-elektrikci-listesi label,
.box-elektrik-kesintisi-sorgulama .item-elektrik-kesintisi-sorgulama label {
    font-family: Hind, sans-serif;
    font-size: 14px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.36;
    letter-spacing: normal;
    color: #747e86;
    width: 40%;
    vertical-align: top;
    margin-top: .5rem;
}
.box-elektrik-kesintisi-sorgulama .item-elektrik-kesintisi-sorgulama label.seperator{
    color: #173a47;
    font-weight: bold;
    border-left: 1px solid rgba(167, 175, 180, 0.2);
    padding-left: 10px;
    margin-left: 7px;
}

.box-item-yetkili-elektrikci-listesi .item-yetkili-elektrikci-listesi span,
.box-elektrik-kesintisi-sorgulama .item-elektrik-kesintisi-sorgulama span {
    font-family: Hind, sans-serif;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.36;
    letter-spacing: normal;
    color: #747e86;
    width: 58%;
    display: inline-block;
    vertical-align: top;
    margin-top: .5rem;
    margin-bottom: .5rem;
}

.item-elektrik-kesintisi-sorgulama .ariza,
.item-elektrik-kesintisi-sorgulama .bakim{
  font-family: Hind, sans-serif;
  font-size: 12px;
  font-weight: 600;
  margin-top:5px;
  margin-bottom:5px;
  float:left;
  width:100%;
}
.item-elektrik-kesintisi-sorgulama .ariza{color: #8b19a0}
.item-elektrik-kesintisi-sorgulama .bakim{color: #309cba}
.item-elektrik-kesintisi-sorgulama .ariza span{box-shadow: 0 2px 6px 0 rgba(139, 25, 160, 0.5);
  background-color: #8b19a0;}
.item-elektrik-kesintisi-sorgulama .bakim span{box-shadow: 0 2px 6px 0 rgba(48, 156, 186, 0.5);
  background-color: #309cba;}
.item-elektrik-kesintisi-sorgulama .ariza span,
.item-elektrik-kesintisi-sorgulama .bakim span{
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: block;
    float: left;
    margin-right: 5px;
    margin-top: 3px;
}

.filter-box-left{
width: 305px;
 height:550px;
  float:left;
  padding-right:20px;
  border-radius: 12px;
  background-color: #e5ebf0;
}

.filter-box-left:before{
    content:'';
    background:#e5ebf0;
    width:400px;
    height:550px;
        position: absolute;
    left: 0;
    margin-top:0;
    z-index:-1;
}

.link_konum{
float:left;
    border-radius: 1px;
  border-bottom:1px solid #486072;
   font-family: Hind, sans-serif;
  font-size: 14px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #486072;
  padding:2px;
  cursor:pointer;
}
.link_konum:hover{
  color: #486072;
  opacity:.7;
}
.button-filtrele,
.button-filtrele-harita{
 border-radius: 5px;
  background-image: linear-gradient(104deg, #173a47, #0c1f27 99%);
  font-family: Hind, sans-serif;
  font-size: 14px;
  color:#fff;
  display:block;
  height:40px;
  text-align:Center;
  line-height:40px;
  cursor:pointer;
}
.button-filtrele:hover{
  color: #fff;
  opacity:.7;
   cursor:pointer;
}
.filter-box-left-header{
    border-bottom: 1px solid rgba(167, 175, 180, 0.2);
    height: 50px;
    padding-top: 10px;
}
.filter-box-left-header h6{
 font-family: Hind, sans-serif;
 font-size: 14px;
 color: #0a191f;
 font-weight: normal;
 font-stretch: normal;
 float:left;
}
.filter-box-left-header .close{
    opacity:1;
    cursor:pointer;
    float:right;
    margin-top: -5px;
}

[type="radio"]:checked + label{
 font-family: Hind, sans-serif;
  font-size: 14px;
  font-weight: 600;
  color: #0a191f;
}
[type="radio"]:not(:checked) + label{
 font-family: Hind, sans-serif;
  font-size: 14px;
  font-weight: 600;
  color: #747e86;
}
[type="radio"]:checked,
[type="radio"]:not(:checked) {
    position: absolute;
    left: -9999px;
}
[type="radio"]:checked + label,
[type="radio"]:not(:checked) + label
{
    position: relative;
    padding-left: 28px;
    cursor: pointer;
    display: inline-block;
}
[type="radio"]:checked + label:before,
[type="radio"]:not(:checked) + label:before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 20px;
    height: 20px;
    border: 2px solid #2f4d5c;
    border-radius: 100%;
    background: #fff;
}
[type="radio"]:checked + label:after,
[type="radio"]:not(:checked) + label:after {
    content: '';
    width: 12px;
    height: 12px;
    background: #2f4d5c;
    position: absolute;
    top: 4px;
    left: 4px;
    border-radius: 100%;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
}
[type="radio"]:not(:checked) + label:after {
    opacity: 0;
    -webkit-transform: scale(0);
    transform: scale(0);
}
[type="radio"]:checked + label:after {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}

.file-input{
    float:left;
    width:100%;
    margin-bottom: 10px;
}
.file-input input[type='file']{
    display:none;
}
.file-input .img-button{
  width: 90px;
  height: 90px;
  border-radius: 12px;
  box-shadow: 0 0 23px 0 rgba(116, 126, 134, 0.2);
  background-color: #ffffff;
  text-align:center;
  cursor:pointer;
}
.file-input .img-button img{
   background: #ffa300;
    border-radius: 50%;
    padding: 4px;
    display: block;
    margin: 20px 35px 7px 35px;
    float: left;
}
.file-input .img-button span{
   font-family: Hind, sans-serif;
  font-size: 12px;
  font-weight: 600;
  color: #486072;
}
.file-input .img-button:hover{
  box-shadow: 0 5px 20px 5px rgb(52 52 52 / 18%);
}
.file-input .img-show{
border-radius: 12px;
  box-shadow: 0 0 23px 0 rgba(116, 126, 134, 0.2);
   width: 90px;
  height: 90px;
}
.file-input .img-show .preview{
    max-width:100%;
    max-height: 100%;
}
.file-input .img-show .remove{
    position: absolute;
    width: 20px;
    height: 20px;
    background: #ffa300;
    border-radius: 50%;
    padding: 3px;
    transform: rotate(-45deg );
    margin-left: 77px;
    display: block;
    margin-top: -6px;
    cursor:pointer;
}

.info-card{
    font-family: Hind, sans-serif;
    font-size: 12px;
    font-weight: 600;
    color: #fdf8e4;
    float: left;
    width: 100%;
    border-radius: 0px 32px 32px 0px;
    padding: 13px 13px 13px 65px;
    min-height:64px;
    display: flex;
    align-items: center;
	text-shadow: 1px 1px 2px #000;
}

.info-card.info-card-success{
	border: 1px solid #666;
  box-shadow: 0 5px 15px 3px rgba(51, 51, 51, 0.2);
  background: rgba(0, 0, 0, 0.3) url(../img/info-card-success.png) no-repeat;
}
.info-card.info-card-success.success-icon{
  box-shadow: 0 5px 15px 3px rgba(148, 188, 79, 0.1);
  background: #ffffff url(../img/info-card-success-check.png) no-repeat;
}

.info-card.info-card-alert{
 box-shadow: 0 5px 15px 3px rgba(255, 55, 0, 0.1);
   background: #ffffff url(../img/info-card-alert.png) no-repeat;
}
.info-card.info-card-danger{
    box-shadow: 0 5px 15px 3px rgba(255, 163, 0, 0.1);
    background: #ffffff url(../img/info-card-danger.png) no-repeat;
}
#accordionForm .card .card-body:after{
    background: none;
}

.bootstrap-select {
    width:100% !important;  
    margin-bottom: 10px;
}
.bootstrap-select button, .bootstrap-select button:hover{
  border-radius: 8px;
  box-shadow: 0 0 15px 0 rgba(116, 126, 134, 0.2) !important;
  background-color: #ffffff !important;
  width:100%;
  border:none !important;;
  text-align:left;
  padding:0 12px;
  height:46px;
}
.bootstrap-select .dropdown-toggle:focus, .bootstrap-select>select.mobile-device:focus+.dropdown-toggle{
    outline:none !important;
}
.bootstrap-select>.dropdown-toggle:after{
    background: url(../img/arrow-right-gray.svg) no-repeat;
    content: '';
    width: 11px;
    height: 20px;
    transform: rotate(90deg);
    transition: .2s;
    position: absolute;
    right: 17px;
    top: 14px;
    border: none;
}
.bootstrap-select.show>.dropdown-toggle:after{
 transform: rotate(-90deg);
}
.bootstrap-select  .filter-option-inner-inner{
 height: 46px;
 padding-top:20px;
 font-family: Hind, sans-serif;
 font-size: 14px;
 font-weight: 600;
 color: #0a191f;
}
.bootstrap-select .filter-option-inner-inner small{
  position: absolute;
  left: 12px;
  top: 7px;
  font-family: Hind, sans-serif;
  font-size: 10px;
  color: #486072 !important;
  font-weight: 600;
}
.bootstrap-select>.dropdown-toggle.bs-placeholder .filter-option-inner-inner{
 padding-top:15px !important;
 color:#a7afb4;
  font-family: Hind, sans-serif;
  font-size: 14px;
  font-weight: 300;
}
.bootstrap-select .dropdown-menu li.disabled,
.bootstrap-select .dropdown-menu li a span.text small{
    display:none;
}
.bootstrap-select .dropdown-menu {
    padding: 20px 15px;
    border-radius: 8px;
    box-shadow: 0 5px 20px 5px rgb(52 52 52 / 10%);
    border: none;
    overflow: hidden;
    width: calc(100% - 30px);
    margin-top:-10px;
}
.bootstrap-select .dropdown-menu ul {
    margin: 0;
    list-style-type: none;
    padding: 0;
    overflow-y: auto;
    float: left;
    width: 100%;
}
.bootstrap-select ul li a {
    margin: 0;
    list-style-type: none;
    font-family: Hind, sans-serif;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.36;
    letter-spacing: normal;
    color: #747e86;
    border-bottom: 1px solid rgb(167 175 180 / 20%);
    display: block;
    padding: 14px 10px 14px 0;
}
.bootstrap-select ul li.active a, .bootstrap-select ul li:hover a, .bootstrap-select ul li:focus a, .bootstrap-select ul li a:focus {
    background:none;
    color: #ffc522;
    outline:none;
    font-weight: 600;
}
.bootstrap-select.show{
  position:relative;
  z-index:2;
}
.bootstrap-select.show .dropdown-menu{
    z-index:-1;
} 
.bootstrap-select.disabled button{
    opacity: .6 !important;
    background-color: #eff3f6 !important;
} 
.bootstrap-select.disabled>.dropdown-toggle.bs-placeholder .filter-option-inner-inner{
    padding-top:20px !important;
    font-weight:600 !important;
}
.harita,
.harita iframe{
    height: 538px;
    width:100%;
    border-radius:10px;
    float:left;
    position:relative;
}
.harita .content{
    position:absolute;
    z-index:10;
    width:100%;
    padding:20px 10px;
}
.harita .content .btn-elektrik-kesintisi {
 color: #486072;
 font-family: Hind, sans-serif;
  font-size: 12px;
  font-weight: 600;
  border:none;
  border-radius: 8px;
  box-shadow: 0 5px 20px 5px rgba(52, 52, 52, 0.05);
  background-color: #ffffff;
  width: 145px;
  height: 46px;
}
.harita .content .btn-elektrik-kesintisi:hover {
    opacity:.9;
}
.harita .content .btn-elektrik-kesintisi span{
  width: 10px;
  height: 10px;
  margin: 3px 2px;
  box-shadow: 0 2px 6px 0 rgba(72, 96, 114, 0.5);
  background-color: #486072;
  display:block;
  border-radius:50%;
  float:left;
}
.liste_gorunumu{
    position:relative;
}
.ariza_bildir{
  width: calc( 100% - 19px);
  height: 34px;
  border-radius: 4px;
  box-shadow: 0 5px 20px 5px rgba(52, 52, 52, 0.05);
  background-image: linear-gradient(92deg, #173a47, #0c1f27 99%);
  position:absolute;
  bottom:-33px;
  font-family: Hind, sans-serif;
  font-size: 14px;
  font-weight: normal;
  text-align: center;
  color: #ffffff;
  padding: 6px 0;
}
.ariza_bildir a{
  font-size: 14px;
  font-weight: bold;
  color: #e5ebf0;
  border-radius: 2px;
  border-bottom:2px solid #fff;
  cursor:pointer;
  margin:0 5px;
  padding:0 4px;
}
.ariza_bildir a:hover{
    opacity:.7;
}
.input-percentage img{
    position: absolute;
    top: 12px;
    right: 27px;
    width: 20px;
}
.input-percentage label{
    color:#0a191f !important;
    font-weight:600 !important;
}
.mobile-banner-button{
  width: 40px;
  height: 40px;
  background:url('../img/mobile-banner-button.png');
  position:absolute;
  bottom:19px;
  right:15px;
  z-index:9;
}

.black-link{
  font-family: Hind, sans-serif;
  font-size: 14px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #fff !important;
  float:left;
  padding:0 3px;
  cursor:pointer;
}
.black-link a {
    color: #ffce44;
	text-decoration:none;
}
.black-link a:hover {
    color: #fcfaf2;
}


.search-input-focus{
  width: 80px;
  height: 46px;
  transition: width 0.5s ease-in-out, right 0.5s ease-in-out;
  position:absolute;
  z-index:2;
  right:300px;
  top:14px; 
/* arama butonu yükseklik - masaüstü */
}


.search-input-focus input{
    padding:10px;
    height: 46px;
}
.search-input-focus.active:hover{
    position:absolute;  
    width:50%;
}
.search-input-focus.active:hover input{
    padding: 10px 10px 10px 100px; 
}
.search-input-focus:before{
    height:46px;
}
.header-mobile .search-input-focus{
    top:15px;
     right:15px;
     width: 60px;
}
.header-mobile .search-input-focus input{
     padding: 13px 10px 10px 10px !important;
}
.header-mobile .search-input-focus.active:hover input{
    padding: 10px 10px 10px 70px !important; 
}
.header-mobile .search-input-focus.active:hover{
   width:92%;
   right: 15px;
}
.kariyer-box{
    min-height:385px;
}
.chatbot {
    position: fixed;
    right: 2%;
    bottom: 30px;
    width: 150px;
    height: 150px;
    background: url('../img/volti_reveal.gif') Center center no-repeat;
    cursor: pointer;
    z-index: 98;
    transition: .2s;
}

  .chatbot.sticky{
        position:absolute;
        transition:.2s;
  }
  .header-scroll .search-input-focus{
        top:14px;
  }
   .bulten-box--list a,
   .bulten-box--list a:focus{
        outline:none !important;
    }

.modal-open{
    overflow-x:hidden !important;
    overflow-y:initial !important;
 }
 .link-click{
      cursor:pointer;
 }
 .datepicker table tr td, .datepicker table tr th{
      font-family: Hind, sans-serif;
  font-size: 14px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #486072 ;
 }
 .datepicker table tr td.active.active.focus, .datepicker table tr td.active.active:focus, .datepicker table tr td.active.active:hover, .datepicker table tr td.active.highlighted.active.focus, .datepicker table tr td.active.highlighted.active:focus, .datepicker table tr td.active.highlighted.active:hover, .datepicker table tr td.active.highlighted:active.focus, .datepicker table tr td.active.highlighted:active:focus, .datepicker table tr td.active.highlighted:active:hover, .datepicker table tr td.active:active.focus, .datepicker table tr td.active:active:focus, .datepicker table tr td.active:active:hover,
.datepicker table tr td.active.active {
      background: linear-gradient(99deg, #ffc522, #ff8000 99%) !important;
      border-color:#ff8000 !important;
 }

 .word-break-all{
    word-break:break-all;
 }
 .modal_homepage_content .scroll-tesisat-secimi{
      padding-right:10px;
 }
 .arama_sonucu{
  float:left;
  margin-top:20px;
 }
 .arama_sonucu label{
  float: left;
  font-family: Hind, sans-serif;
  font-size: 20px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: normal;
  color: #747e86;
    margin: 13px 10px 0 0;

 }
  .arama_sonucu div{
  float:left;
  padding: 10px 20px 7px 30px;
  border-radius: 20px;
  box-shadow: 0 5px 20px 5px rgba(52, 52, 52, 0.05);
  background-color: #ffffff;
  font-family: Hind, sans-serif;
  font-size: 14px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: right;
  color: #486072;
 }
 .arama_sonucu img{
    cursor:pointer;
    margin-left:10px;
    width:14px;
      
 }
 .body-login, .login-page{
    margin-bottom:80px;
}
.body-loading {
    position: fixed;
}
.body-overflow-hidden {
    overflow-y: hidden;
}
.loading-bg{
 -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
  background-color: rgba(35, 31, 32, 0.9);
  position:fixed;
  width:100%;
  height:100%;
  z-index:9999;
  display:none;
  top:0;
}
.loading-bg .center{
    display:Block;
    margin: 17% auto;
    width:245px;
}
.loading-bg .spinner-border{
    width: 245px;
    height: 248px;
    background: url(../img/loading-bg.svg) no-repeat center center;
    animation: 1.75s linear infinite spinner-border;
    border: none;
}
.loading-bg .loading-img{
    margin-left: 80px;
    margin-top: -165px;
}
.loading-bg h3{
 font-family: Hind, sans-serif;
  font-size: 24px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #ffa300;
  text-align:center;
}
.homepage-bilgilendirme-item .bulten-box p{
     display: -webkit-box;
 max-width: 100%;
 margin: 0 auto;
 -webkit-line-clamp: 2;
 /* autoprefixer: off */
 -webkit-box-orient: vertical;
 /* autoprefixer: on */
 overflow: hidden;
 text-overflow: ellipsis;
}

.line-clamp-3 {
    display: -webkit-box;
    max-width: 100%;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.line-clamp-2 {
    display: -webkit-box;
    max-width: 100%;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.line-clamp-1 {
    display: -webkit-box;
    max-width: 100%;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.line-clamp-5 {
    display: -webkit-box;
    max-width: 100%;
    -webkit-line-clamp: 5;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.line-clamp-6 {
    display: -webkit-box;
    max-width: 100%;
    -webkit-line-clamp: 6;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.line-clamp-11 {
    display: -webkit-box;
    max-width: 100%;
    -webkit-line-clamp: 11;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.homepage-bizden-haberler-bg .detay-link {
    position: relative;
    z-index: 10;
}

.container-detail-block p img {
    height: inherit;
    width: 100%;
    object-fit: contain;
}

.container-detail---shadow-box p img {
    height: inherit;
    width: 100%;
    object-fit: contain;
}
.mb-dp-40 {
    margin-bottom: 40px;
}
.sonucvar .ul-search-filter {
    white-space: nowrap;
    overflow-y: hidden;
    overflow-x: scroll;
    min-height: 35px;
}

    .sonucvar .ul-search-filter li {
        display: inline;
    }

    .sonucvar .ul-search-filter::-webkit-scrollbar {
        width: 3px;
        height: 3px;
    }

    .sonucvar .ul-search-filter::-webkit-scrollbar-thumb {
        background: #a7afb4;
    }

        .sonucvar .ul-search-filter::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
@media (min-width: 1700px) and (max-width:1920px){
    .container, .container-lg, .container-md, .container-sm, .container-xl {
        max-width: 1600px;
    }
    .homepage-bilgilendirme .col-lg-6.mb-40{
        margin-bottom:52.3px !important;
    }
    .container-detail-block{
            width: calc(100% - 350px);
             min-height:605px
    }
    .container-box{
     min-height:570px
    }
    .menu-left,
    .filter-box-left,
    .box-musteri-hizmetleri{
        width:350px;
    }
    .homepage_slider .carousel-caption{
        left:9% !important;
    }
}

@media (min-width: 1400px) and (max-width:1700px){
    .container, .container-lg, .container-md, .container-sm, .container-xl {
        max-width: 90%;
    }
    .homepage_slider .carousel-caption{
        left:6% !important;
    }
    .chatbot{
        right:2%;
    }
    .container-detail-block,
    .container-box{
        min-height:420px
    }
    .filter-box-left:before {
        width: 350px
    }
    .border-box--shadow {
        min-height: 195px;
    }
}
@media (max-width: 1400px) and  (min-width: 1000px){
    .homepage_slider .carousel-caption{
        left:5% !important;
    }
    .filter-box-left:before{
        width:300px
    }
    header .button--default-padding{
       padding: 4px;
       font-size: 11px;
       max-width:70px;
    }
    .homepage-header .menu ul li, .header .menu ul li{
            padding: 24px 20px 18px 20px;
    }   
    .search-input-focus{
        right:15px;
    }
    .homepage-box-item{
        overflow:Hidden;
    }
    .homepage-box-group .homepage-box-item{
        min-height:337px;
    }
    .carousel-bizdenhaberler .text h3 {
        font-size: 16px !important;
        margin-bottom: 20px;
    }
    .carousel-bizdenhaberler .text p {
        font-size: 14px !important;
    }

    .homepage_slider .carousel-indicators li{
     background-color: #a7afb4;
    }
    .homepage_slider .carousel-caption{
        left:10%;
    }
    .homepage_slider .carousel-caption p{
        font-size:16px;
    }
    .homepage_slider .carousel-caption h5{
        font-size:28px;
    }
    .homepage_slider .carousel-caption{
        top:240px;
    }
    .homepage_slider .img-fu{
        height:900px !important;
    }
    .border-box--shadow {
        min-height: 210px;
    }
}

@media (max-width: 1000px){
    .search-input.show .bg_search-input {
        width: 100%;
        height: 100%;
        background-color: rgba(35, 31, 32, 0.85);
        position: fixed;
        left: 0;
        top: 0;
        z-index: 103;
    }
    .filter-box-left:before {
        width: 300px
    }
    .search-input.show input {
        z-index: 103;
        position: relative;
    }

    .search-input-radius.show:before, .button-box {
        z-index: 104;
    }

    .search-input--autocomplete .dropdown-menu .detay-link,
    .search-input--autocompleteArama .dropdown-menu .detay-link {
        padding-right: 5px;
    }

    .homepage-search-box .search-input.show {
        margin-top: -70px;
    }
    .homepage-search-box .search-input--autocomplete .dropdown-menu.show {
        margin-top: -70px;
    }

    .scrollable {
        max-height: 400px;
        overflow-y: scroll;
        margin-right: 20px;
        margin-bottom: 40px;
    }


        .scrollable::-webkit-scrollbar {
            width: 6px;
            right: -35px;
        }

        .scrollable::-webkit-scrollbar-track {
            background: #e5ebf0;
            border-radius: 3px;
        }

        .scrollable::-webkit-scrollbar-thumb {
            background: #a7afb4;
            border-radius: 3px;
        }

            .scrollable::-webkit-scrollbar-thumb:hover {
                background: #555;
            }

    header .search-input--autocomplete .dropdown-menu,
    header .search-input--default .dropdown-menu {
        width: 100%;
        margin-left: -15px;
    }
    .breadcrumb {
        float: left;
        width: 100%;
        margin-top: 10px;
    }
    .carousel-bilgilendirme .bulten-box {
        min-height: 178px;
    }
    .table-link-mobile {
        position: absolute;
        margin-top: 22%;
    }

    .m-table-border-left {
        min-width: 33px;
        border-left: 1px solid #a7afb4;
    }
    .mobile-w-50 {
        width: 50% !important
    }
    .kariyer-box {
        min-height: initial !important;
    }
    .mb-dp-40 {
        margin-bottom: 0 !important;
    }
    .chatbot {
        right: 20px;
        bottom:0;
        background-repeat:no-repeat;
        position:fixed !important;
        top:initial !important;

    }
    .body_yapim_asamasi{
        background:#0c1f27 url(../img/yapim_asamasi_mobile.svg) no-repeat;
        background-size:cover;
    }
    .container_yapim_asamasi .logo{
        margin-top:25px;
    }
    .container_yapim_asamasi .mobile-text-position{
        position:absolute;
        bottom:60px;
        width:100%;
        overflow:hidden;
    }
    .container_yapim_asamasi h1{
      font-size: 24px;
      margin-top:0;
    }
    .container_yapim_asamasi p{
      font-size: 16px;
       width:90%;
    }
    
    .container-box {
        width: 100%;
    }
    .breadcrumb-item{
        display:none;
    }
    .breadcrumb-item.active{
        display:block;
        padding-left: 0;
    }
    .breadcrumb-item+.breadcrumb-item.active::before{
        background: url("../img/arrow-yellow2.png") no-repeat !important;
    }
    .button--default{
        width: 100%;
    }
    .spec-box{
        width: 33% !important;
        max-width: 33%;
        border:none;
    }
    .spec-box p{
        text-align: center;
        line-height: 20px;
        padding: 0;
    }
    .spec-box .rounded-circle{
        margin-left: calc(50% - 20px);
        margin-bottom:10px;
    }

    .list-number li:last-child:before {
        background: white !important;
        border: none;
        left: -5px;
        width: 46px;
    }
    
    .list-number li span{
        display: table;
    }
    .menu-left{display:none;}
    .menu-left--mobile{display:block}
    .container-detail-block{
        width:100%;
        padding-left:0;
    }
    ul.table-list li .p_bold {
        width: 100%;
    }

    .isbirliklerimiz .item-isbirliklerimiz{
        width:50%;
    }
    .ayedas-genel-mudurluk .iframe {
        margin-top: 20px;
        margin-bottom: -26px;
        margin-right: -20px;
        margin-left: -20px;
    }
    .kurumsal_kariyer{
        background-size: cover;
    }
    .kurumsal_kariyer .bg{
        border-bottom:1px solid rgb(255 255 255 / 20%);
        padding-bottom:30px;
    }
    .kariyer-box .spec-box p{
       width:100%;
       text-align:center;
    }
    .img-content--banner img{
        content:url("../img/kariyer_yonetim_banner_mobile.jpg");
    }

    .img-content--banner .text{
        font-size:20px;
        top: calc(50% - 10px);
        padding-left: 15px;
    }

    .img-content--banner a{
       bottom: 20px;
       right: 40px;
       padding: 10px 15px;
    }
    .bulten-box--list:before {
         margin-top: 80px;
    }
    .video--banner:after{
        height:100px;
    }
     .video--banner:before{
    left: calc(50% - 20px);
    }
    #video{
        height:initial !important;
    }
    .carouselMedia:after{
        height:100px;
    }
    .carouselMedia .carousel-control-prev,
    .carouselMedia .carousel-control-next{
        display:none;
    }
    .bulten-box--list-img .img{
        width:40%;
        min-height:124px;
        margin-right: 10px;
    }
    .bulten-box--list-img:before {
         margin-top: 55px;
    }
    .bulten-box--list-img .img img {
        height: 124px;
        padding: 20px 0 20px 10px;
    }
    .bulten-box--list-img p{
        white-space: nowrap;
        overflow: hidden !important;
        text-overflow: ellipsis;
    }
    .bulten-box--list-img h3{
        margin-top:5px !important;
        margin-bottom:5px !important;
    }
    .bulten-box--list .img:after{
        height:initial;
    }   
    .table-default{
        display:none;
    }
    .table-default--mobile{
        display:block;
    }
    .box-musteri-hizmetleri{
        width:100%;
        margin-top:20px;
    }
    .container-detail-block.pr-20{
        padding-right:0 !important;
    }
    .header,
    .homepage-header,
    .footer-desk{
        display:none;
    }
    .header-mobile,
    .footer-mobile{
        display:block;
    }
    .footer-mobile {
        margin-bottom: 0px;
    }
    .homepage_slider{
    display:none;
    }
    .homepage_slider .carousel {
        height:220px;
    }
    .homepage_slider-mobile {
        float: left;
        width: 100%;
        display: Block;
        background-image: linear-gradient(#f5f7f9, white);
    }
    .slider-mask{
        background-size: contain;
    }
    .footer{
        padding:20px 0 0 ;
    }
    .footer .social{ float:left;}
    .footer-top ul{padding-left:0;}
    .footer-top ul li{width:100%;     padding: 10px 8px;}
    .footer-bottom{height:auto;border:none;}
    .footer-bottom ul li{width:100%;padding-left: 8px; }
    .footer-bottom p{
        float:left;
        margin:0;
        padding-left: 8px;

    }
    .footer hr{
        width:100%;
        float:left;
    }
    .footer-top{
        margin-bottom:18px;
    }
    .footer .cagri-merkezi{
        float: right;
        margin-top: 14px;
    }
    .footer-top .whatsapp img{
        padding-left:5px;
    }
    .homepage-search-box{
        position:initial;
        margin-top:15px;
        float:left;
    }
    .search-input-radius input{
        height:40px;
        padding: 13px 10px 10px 70px;
    }
    .search-input-radius:before {
        width: 60px;
        height: 40px;
    }
    .btn-hashtag{
        font-size: 10px;
            padding: 7px;
            margin-right:5px;
            margin-bottom:5px;
    }
    .btn-hashtag img{
        width:10px;
        margin-right: 2px;
    }
    .homepage-link-box{
        margin-top:40px;
    }
    .homepage-box-group{
        margin-top:10px;
    }
    .homepage-box-group .title,
     .homepage-box-item .title, .homepage-bilgilendirme-item .title, .homepage-bizden-haberler .title{
        width:60%;
        font-size:20px;
    }
    .homepage-box-group .homepage-box-item--body p{
        font-size:12px !important;
    }
    .homepage-box-group .bg-elektrik-Kesintisi .col-lg-9{
        width:70%;
        padding-right:0;
    }
    .homepage-box-group .bg-elektrik-Kesintisi .col-lg-3{
        width:30%;
         padding-right:0;
    }
    .mobile-100{
        width:100% !important;
    }    

    .homepage-box-group .bg-online-islemler .col-lg-4{
        width:33.3% !important;
        padding-left: 2.5px;
        padding-right: 2.5px;
    }
    .homepage-box-item--body .row {
        margin-left: -2.5px;
        margin-right: -2.5px;
    }
    .homepage-box-item--body .item h5{
        font-size:11px;
        margin-top:10px;
    }
    .homepage-box-item--body .item small, .container_404.mobile{
        display:block;
    }
    .homepage-box-group .bg-online-islemler .item p, .container_404.pc{
        display:none;
    }
    .homepage-box-group .homepage-box-item{
        padding:15px;
        min-height:initial;
        overflow:hidden;
    }
    .homepage-box-item--body .item{
        min-height:110px;
    }
    .homepage-box-group .col-lg-6:nth-child(1), .homepage-box-group .col-lg-6:nth-child(3), .homepage-bilgilendirme .col-lg-6:nth-child(1){
        padding-right:15px;
    }
    .homepage-bilgilendirme .homepage-bilgilendirme-item, .homepage-seo .homepage-bilgilendirme-item {
        padding: 15px;
    }
    .homepage-bilgilendirme-item .col-lg-6.mb-40{
        margin-bottom:10px !important;
    }
    .homepage-bilgilendirme:before{
     width: 95%;
    }
    .homepage_slider .carousel-control-prev,
    .homepage_slider .carousel-control-next{
        top:30%;
    }
    .owl-nav{
    display:None;
    }
    .carousel-bizdenhaberler .text h3 {
        font-size: 14px !important;
    }
    .carousel-bizdenhaberler .text p {
        font-size: 12px !important;
    }
    .carousel-bizdenhaberler .text .detay-link {
        font-size: 12px !important;
    }

    .carousel-bizdenhaberler .owl-stage .owl-item.img-large .text h3 {
        font-size: 16px !important;
    }

    .carousel-bizdenhaberler .owl-stage .owl-item.img-large .text p {
        font-size: 14px !important;
    }

    .carousel-bizdenhaberler .owl-stage .owl-item.img-large .text .detay-link {
        font-size: 14px !important;
    }

    .carousel-bizdenhaberler .owl-stage .owl-item .text {
        padding-left: 10% !important;
        padding-bottom: 10px;
        padding-top: 20px !important;
    }
    .carousel-bizdenhaberler.owl-drag .owl-item {
        min-height: 170px;
    }

    .carousel-bizdenhaberler .date span {
        font-size: 12px;
        padding-top:10px;
    }

    .carousel-bizdenhaberler .date strong {
        font-size: 36px;
        line-height: 0px;
        font-weight: normal;
        -webkit-text-stroke: 1.5px #747e86;
    }

    .carousel-bizdenhaberler .date:before {
        margin-top: -20px;
        width: 15px;
        height: 15px;
    }

    .carousel-bizdenhaberler .date {
        top: 26px;
        right: 0px;
        width: 50px;
    }
    .homepage_slider .carousel-indicators{
        bottom:0;
    }
    .homepage_slider .carousel-indicators li{
        background: #a7afb4; 
    }

    .accordion-default img.pl-40,
    .accordion-default img.pr-40{
    padding-left:0 !important;
    padding-right:0 !important;
    }
     .accordion-default .card-body img{
     width:100%;
    }
    .site_haritasi_padding{
        margin:0;
    }
    .footer_cerezpolitikasi .mobile-p{
        display:Block;
        margin-top:10px;
    }
    .btn-memnuniyet-anketi {
        position: fixed;
        transform: inherit;
        margin-top: 20px;
        right: 0;
        left: 15px;
        bottom: 0;
        z-index: 98 !important;
    }
    .btn-memnuniyet-anketi.active{
         padding: 3px 0 16px 0;
    }
    #modal-memnuniyet-anketi .modal-content{
        padding:0;
    }
    #modal-memnuniyet-anketi .step-content h4{
        font-size:14px;
    }
    #modal-memnuniyet-anketi .step-content .survey li a{
        width:45px;
        height:45px;
        padding: 8px;
    }
     #modal-memnuniyet-anketi .step-content .survey li a img{
        width:30px;
    }
    #modal-memnuniyet-anketi .step-content .survey li a:hover {
        width: 60px;
        height: 60px;
        padding: 4px 10px;
        margin-top: -12px;
    }
     #modal-memnuniyet-anketi .step-content .survey li a:hover img{
        width:40px;
    }
    #modal-memnuniyet-anketi .step-content .item .form-row {
        width: 100%;
        margin-left: inherit;
    }
     #modal-memnuniyet-anketi .step-content .item .form-row .col-lg-6{
        width:50%;
    }
    #modal-memnuniyet-anketi .step-content .item .form-row-textarea {
        width: inherit;
        margin-left: 0;
		text-align: left;
    }
    .homepage-bizden-haberler{
        margin-top:20px;
    }
    .mobile-show{
        display:block !important;
    }
    .mobile-hidden{
        display:none !important;
    }
    .search-input--autocomplete .dropdown-menu ul li a, 
    .search-input--autocompleteArama .dropdown-menu ul li a, 
    .search-input--default .dropdown-menu ul li a, 
    .search-input ol li a, 
    .search-inputArama ol li a {
        font-size: 14px;
    }
    .search-input--autocomplete .dropdown-menu .input-text, 
    .search-input--autocompleteArama .dropdown-menu .input-text, 
    .search-input--default .dropdown-menu .input-text, 
    .search-input .dropdown-menu .input-title, 
    .search-input ol li .search-inputArama ol li {
        font-size: 16px;
    }
    .filter-box-left {
        width: 100%;
        background-color: #e5ebf0;
        position: absolute;
        z-index: 9999;
        height: 100%;
        top: 0;
        left: 0;
        border-radius: 0;
        padding-left: 20px;
        display: none;
        overflow-y: scroll;
        padding-bottom: 20px;
    }
    .info-card{
     border-radius: 0px 82px 82px 0px;
     padding: 13px 35px 13px 65px;
    }
    .ul-search-filter-mobile{
    border-radius: 12px 12px 0 0;
  background-color: #ffffff;
  width:100%;
  
        padding-top:8px;
  min-height:40px;
    }
    .ul-search-filter-mobile li{
        width:49%;
        margin:0;
        text-align:Center;
    }
   .ul-search-filter-mobile li:first-child{
            border-right: 1px solid rgba(167, 175, 180, 0.2);
    } 
    .harita .content{
        padding:10px 0;
    }
    .harita .content .btn-elektrik-kesintisi{
        margin-top:5px;
    }
    .item-elektrik-kesintisi-sorgulama .seperator {
        width:70% !important;
    }
    .ariza_bildir{
        width:100%;
    }
    .border-box--shadow {
        min-height: unset;
    }
    .homepage-box-item .title:before, .homepage-bilgilendirme-item .title:before {
        margin-top: -5px;
    }
    .search-input ol li, .search-inputArama ol li {
        font-size: 16px;
        padding-left: 10px;
    }
    .search-input ol li:first-child, .search-inputArama ol li:first-child {
        padding-bottom: 10px;
    }
    .search-input ol li a, .search-inputArama ol li a {
        padding-right: 10px;
    }
    .carousel-bizdenhaberler .owl-stage .owl-item.img-large {
        margin-top: 0 !important;
        bottom: 0;
    }
}


@media (min-width: 600px) and (max-width: 1000px){  

    .carousel-bizdenhaberler .owl-stage .owl-item{
        margin-top:20px !important;
    }
    .homepage-bizden-haberler{
        height:360px;
    }
    .homepage-bizden-haberler:before,
    .homepage-bizden-haberler-bg:before{
        height:380px;
    }
     .homepage-carousel .owl-item img.full,
    .homepage-carousel .owl-item .slider-mask-mobile{
        height:300px !important;
    }
    .homepage-carousel .owl-item{
        margin-top:40px !important;
    }
    .homepage-carousel .owl-item.img-large{
        margin-top:0 !important;
    }
    .homepage-carousel .owl-item.img-large img.full,
    .homepage-carousel .owl-item.img-large .slider-mask-mobile{
        height:360px !important;
    }
     .owl-item .dekupe{
        width: 276px !important;
        height: 276px !important;
        margin-top: 30px;
        margin-bottom: 20px;
    }
    .owl-item  .dusuk_kalite {
       float: right;
        width: 384px !important;
        margin-top: 30px;
        margin-right: -35px;
    }
    .owl-carousel .owl-item{
        overflow:hidden;
    }
     .body-onemli_bilgilendirme .homepage_slider{
        margin-top:144px;
    }
    .body-onemli_bilgilendirme .header-mobile{
        margin-top:144px;
    }
    .body-onemli_bilgilendirme .homepage-search-box{
       top:266px;
    }
    .onemli_bilgilendirme{
        height: 144px;
        background-size: 60px;
        background-position: -14px -5px;
    }
}

@media (max-width: 600px){  
    .loading-bg .center{
        display:Block;
        margin: 35% auto;
        width:245px;
    }
    .container_404.mobile a{
        width:100%;
        text-align:center;
    }

    .carousel-bizdenhaberler .owl-stage .owl-item{
        margin-top:25px !important;
    }
    .homepage-bizden-haberler{
        height:260px;
    }
    .homepage-bizden-haberler:before,
    .homepage-bizden-haberler-bg:before{
        height:305px;
    }
        .homepage-bizden-haberler:before {
            width: 95%;
        }
        .homepage-bizden-haberler .owl-carousel .owl-stage-outer {
            overflow: hidden;
        }
    .homepage-carousel .owl-item img.full,
    .homepage-carousel .owl-item .slider-mask-mobile{
        height:180px !important;
    }
    .homepage-carousel .owl-item{
        margin-top:25px !important;
    }
    .homepage-carousel .owl-item.img-large{
        margin-top:0 !important;
    }
    .homepage-carousel .owl-item.img-large img.full,
    .homepage-carousel .owl-item.img-large .slider-mask-mobile{
        height:230px !important;
    }

    .owl-item .dekupe {
        width: 165px !important;
        height: 165px !important;
        margin-top: 20px;
        margin-bottom: 20px;
        margin-right: -45px
    }
    .owl-item  .dusuk_kalite {
        float: right;
        width: 200px !important;
        margin-top: 30px;
        margin-right: -14px;
    }
    .owl-item.img-large  .dusuk_kalite {
        float: right;
        width: 290px !important;
        margin-top: 30px;
        margin-right: -35%;
    }
      .owl-carousel .owl-item{
        overflow:hidden;
    }
     .mobile-show-600{
        display:block !important;
    }
    .mobile-hidden-600{
        display:none !important;
    }
     .body-onemli_bilgilendirme .homepage_slider{
        margin-top:184px;
    }
    .body-onemli_bilgilendirme .header-mobile{
        margin-top:184px;
    }
    .body-onemli_bilgilendirme .homepage-search-box{
       top:304px;
    }
    .onemli_bilgilendirme{
        height: 184px;
        background-size: 40px;
        background-position: -11px -5px;
    }
    .onemli_bilgilendirme{
       padding: 10px 20px;
    }
    .onemli_bilgilendirme .close{
        top:-3px;
    }
    .onemli_bilgilendirme h3 {
        font-size: 14px;
    }
    .carousel-bilgilendirme p {
        font-size: 12px;
    }
    .modal-content {
        border-radius: 12px;
    }
    .title {
        font-size: 20px;
    }
    .sub-title{
        font-size: 16px;
    }
    .accordion-default .card .card-header button {
        font-size: 14px;
    }
}
@media (max-width: 400px){  
.carousel-bizdenhaberler .owl-stage .owl-item {
    margin-top: 40px !important;
}
}
/* samsung s7 landscape*/
@media (-webkit-max-device-pixel-ratio:3) and (max-resolution: 192dpi) {
    .scroll-tesisat-secimi {
        min-height: 200px !important;
    }
}
 body{overflow-x:hidden !important;}
@-moz-document url-prefix() {
    .owl-carousel .owl-nav button.owl-next, .owl-carousel .owl-nav button.owl-prev, .owl-carousel button.owl-dot,
    .homepage_slider .carousel-control-prev, .homepage_slider .carousel-control-next {
        background-color: rgba(255, 255, 255, 0.7) !important;
    }

    .owl-nav .owl-prev {
        position: absolute;
    }

    .homepage-bizden-haberler::before {
        width: initial !important
    }
}

.font-size-20{
    font-size:20px;
}
.font-size-18{
    font-size:18px !important;
}
.mt-0 {
    margin-top: 0px !important;
}
.mt-5 {
    margin-top: 5px !important;
}
.mt-10 {
    margin-top: 10px !important;
}
.mt-15 {
    margin-top: 15px !important;
}
.mt-20 {
    margin-top: 20px !important;
}
.mt-25 {
    margin-top: 25px !important;
}
.mt-30 {
    margin-top: 30px !important;
}
.mt-40 {
    margin-top: 40px !important;
}
.mt-50 {
    margin-top: 50px !important;
}
.mt-60 {
    margin-top: 60px !important;
}
.mt-80 {
    margin-top: 80px !important;
}
.ml-30 {
    margin-left: 30px !important;
}

.mb-0 {
    margin-bottom: 0px !important;
}
.mb-5 {
    margin-bottom: 5px !important;
}
.mb-10 {
    margin-bottom: 10px !important;
}
.mb-15 {
    margin-bottom: 15px !important;
}
.mb-20 {
    margin-bottom: 20px !important;
}
.mb-25 {
    margin-bottom: 25px !important;
}
.mb-30 {
    margin-bottom: 30px !important;
}
.mb-35 {
    margin-bottom: 35px !important;
}
.mb-40 {
    margin-bottom: 40px !important;
}
.mb-45 {
    margin-bottom: 45px !important;
}
.mb-50 {
    margin-bottom: 50px !important;
}
.mb-60 {
    margin-bottom: 60px !important;
}
.mb-80 {
    margin-bottom: 80px !important;
}
.pt-0 {
    padding-top: 0px !important;
}
.pt-5 {
    padding-top: 5px !important;
}
.pt-10 {
    padding-top: 10px !important;
}
.pt-15 {
    padding-top: 15px !important;
}
.pt-20 {
    padding-top: 20px !important;
}
.pt-25 {
    padding-top: 25px !important;
}
.pt-30 {
    padding-top: 30px !important;
}
.pt-40 {
    padding-top: 40px !important;
}
.pt-50 {
    padding-top: 50px !important;
}
.pt-60 {
    padding-top: 60px !important;
}
.pt-70 {
    padding-top: 70px !important;
}
.pt-80 {
    padding-top: 80px !important;
}

.pb-0 {
    padding-bottom: 0px !important;
}
.pb-5 {
    padding-bottom: 5px !important;
}
.pb-10 {
    padding-bottom: 10px !important;
}
.pb-15 {
    padding-bottom: 15px !important;
}
.pb-20 {
    padding-bottom: 20px !important;
}
.pb-25 {
    padding-bottom: 25px !important;
}
.pb-30 {
    padding-bottom: 30px !important;
}
.pb-40 {
    padding-bottom: 40px !important;
}
.pb-50 {
    padding-bottom: 50px !important;
}
.pb-60 {
    padding-bottom: 60px !important;
}
.pb-80 {
    padding-bottom: 80px !important;
}

.pl-0 {
    padding-left: 0px !important;
}
.pl-5 {
    padding-left: 5px !important;
}
.pl-10 {
    padding-left: 10px !important;
}
.pl-15 {
    padding-left: 15px !important;
}
.pl-20 {
    padding-left: 20px !important;
}
.pl-25 {
    padding-left: 25px !important;
}
.pl-30 {
    padding-left: 30px !important;
}
.pl-40 {
    padding-left: 40px !important;
}
.pl-50 {
    padding-left: 50px !important;
}
.pl-60 {
    padding-left: 60px !important;
}
.pl-80 {
    padding-left: 80px !important;
}
.pr-0 {
    padding-right: 0px !important;
}
.pr-5 {
    padding-right: 5px !important;
}
.pr-10 {
    padding-right: 10px !important;
}
.pr-15 {
    padding-right: 15px !important;
}
.pr-20 {
    padding-right: 20px !important;
}
.pr-25 {
    padding-right: 25px !important;
}
.pr-30 {
    padding-right: 30px !important;
}
.pr-40 {
    padding-right: 40px !important;
}
.pr-50 {
    padding-right: 50px !important;
}
.pr-60 {
    padding-right: 60px !important;
}
.pr-80 {
    padding-right: 80px !important;
}


.pa-0 {
    padding: 0px !important;
}
.pa-5 {
    padding: 5px !important;
}
.pa-10 {
    padding: 10px !important;
}
.pa-15 {
    padding: 15px !important;
}
.pa-20 {
    padding: 20px !important;
}
.pa-25 {
    padding: 25px !important;
}
.pa-30 {
    padding: 30px !important;
}
.pa-40 {
    padding: 40px !important;
}
.mobile-pa-0{
    padding:0 !important;
}
.page-link.next.last {
        background: url(../img/sub_menu_arrow-gray.svg) no-repeat !important;
        background-position: center center !important;
    }

.page-link.previous.first {
    background: url(../img/sub_menu_arrow-gray.svg) no-repeat !important;
    background-position: center center !important;
    transform: rotate(-180deg);
    margin-right: 5px;
}


.ariza-bildirme-tebrikler {
    padding-left: 110px;
    position: relative;
    z-index: 2;
    overflow: Hidden;
}

.ariza-bildirme-tebrikler:before {
    background: url(../img/info-success.svg) no-repeat;
    position: absolute;
    content: '';
    width: 110px;
    height: 110px;
    left: 0;
    top: 0px;
    z-index: -1;
    background-size: cover;
}

.text-input-error input {
    box-shadow: 0 0 15px 0 rgba(116, 126, 134, 0.2) !important;
    border: solid 1px #ff3700 !important;
}


/*Arama*/
/*.search-input {
    width: 100%;
}*/

.search-inputArama input {
    width: 100%;
    padding: 10px 10px 10px 60px;
    border-radius: 8px;
    box-shadow: 0 0 15px 0 rgba(116, 126, 134, 0.2);
    background-color: #ffffff;
    border: solid 1px #fff;
    height: 46px;
    font-family: Hind, sans-serif;
    font-size: 16px;
    font-weight: 300;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    color: #a7afb4;
}

    .search-inputArama input::placeholder {
        color: #a7afb4;
    }

    .search-inputArama input[type="text"]:focus {
        box-shadow: 0 0 15px 0 rgba(116, 126, 134, 0.2) !important;
        border: solid 1px #ffc522 !important;
    }

.search-inputArama.shadow-clear input[type="text"]:focus {
    box-shadow: 0 0 15px 0 rgba(116, 126, 134, 0.2) !important;
    border: solid 1px #fff !important;
}

.search-inputArama:before {
    content: '';
    width: 33px;
    height: 20px;
    position: absolute;
    background: url(../img/search-input-icon.svg) no-repeat;
    margin-top: 13px;
    margin-left: 13px;
    border-right: 1px solid rgb(179 194 212 / 50%);
}

/*Arama*/

/*Client side validation error*/
.field-validation-error {
    font-family: Hind, sans-serif;
    font-size: 12px;
    font-weight: 600;
    color: #173a47;
    float: center;
    width: 90%;
    border-radius: 32px 32px 32px 32px;
    display: flex;
    align-items: center;
    box-shadow: 0 5px 15px 3px rgba(255, 55, 0, 0.1);
    background: #ffffff url(../img/info-card-alert.png) no-repeat;
    min-height: 20px;
    padding: 2px 30px 0px 35px;
	margin: 0px 0px 0px 15px;
    background-size: 30px !important;
}

.popover {
    padding: 10px;
    box-shadow: 0 0 15px 0 rgba(116, 126, 134, 0.2);
    background-color: #ffffff;
    border: None;
    max-width: 334px;
}

.popover-body img {
    width: 100%;
}

.popover-body h1 {
    font-family: Hind, sans-serif;
    font-size: 16px;
    font-weight: bold;
    color: #173a47;
}

.popover-body p {
    font-family: Hind, sans-serif;
    font-size: 14px;
    color: #747e86;
}

.bs-popover-auto[x-placement^=right] > .arrow::before, .bs-popover-right > .arrow::before {
    left: 0;
    border-width: .5rem .5rem .5rem 0;
    border-right-color: rgba(255, 255, 255, 0.25);
}


.araBtnDisabled {
    pointer-events: none;
    cursor: default;
}

.pac-item {
    font-family: Hind, sans-serif;
    font-size: 16px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.19;
    letter-spacing: normal;
    color: #747e86;
    display: block;
    border-bottom: 1px solid rgb(167 175 180 / 20%);
    padding: 14px 20px;
    cursor: pointer;
}

    .pac-item:hover {
        box-shadow: 0 5px 20px 5px rgb(52 52 52 / 10%);
        background-color: #ffffff;
    }

.pac-item-query {
    font-family: Hind, sans-serif;
    font-size: 16px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.19;
    letter-spacing: normal;
    color: #747e86;
}

.pac-item:hover .pac-icon {
    background-image: url('../img/combined-shape.svg') !important;
}

.pac-icon {
    width: 15px;
    height: 20px;
    margin-right: 4px;
    margin-top: -3px;
    display: inline-block;
    vertical-align: top;
    background-image: url('../img/combined-shape-grey.svg') !important;
    background-size: contain;
}

input:focus::placeholder {
    color: transparent;
}

a[href^="tel"] {
    color: inherit;
    text-decoration: none;
}

.max_char_info {
    position: absolute;
    bottom: 10px;
    right: 20px;
    font-size: 10px;
    color: #a7afb4;
}
.max_char_info_koor_1 {
    position: absolute;
    bottom: 70px;
    right: 20px;
    font-size: 10px;
    color: #a7afb4;
}
.max_char_info_koor_2 {
    position: absolute;
    bottom: 5px;
    right: 20px;
    font-size: 10px;
    color: #a7afb4;
}
.toggle {
    background-color: white;
    border-color: black;
}
.toggle-on {
    background-color: black;
    color: white;
}
.toggle-on:hover {
    color: white;
}
.toggle.off {
    background-color: white;
}
.toggle, .toggle-on, .toggle-off {
    border-radius: 20px;
}
.toggle .toggle-handle {
    border-radius: 20px;
}
.toggle:hover {
    opacity: 0.7;
}

span.charCount {
    opacity: 0.5;
    font-size: 7pt;
    float: right;
    margin-bottom: -15px;
    margin-right: 5px;
}