<?php
include "../db/baglanti.php";
require 'fonksiyonlar.php';

if (!isset($_SESSION['kullanici_adi'])) {
    header("Location:giris.php");
    exit;
}

$kullanici_adi = $_SESSION['kullanici_adi'];
$kullaciSor = $db->prepare("SELECT * FROM yonetim WHERE kullanici_adi = ?");
$kullaciSor->execute(array($kullanici_adi));
$kullaniciCek = $kullaciSor->fetch(PDO::FETCH_ASSOC);

if (!$kullaniciCek) {
    header("Location:giris.php"); 
    exit;
}

$ayarsor=$db->prepare("select * from ayar where ayar_id=:ayar_id");
$ayarsor->execute(array(
        "ayar_id"=>1
));
$ayarcek=$ayarsor->fetch(PDO::FETCH_ASSOC);

$sayfatip=$_POST['st'];
$yonlenurl=$_POST['url'];
$yol = $ayarcek['dosyadizin']."/";
$menuid = $_POST['menu_id_dosya'];
$dosyaboyutu = $ayarcek['maxdosyaboyutu'];

if (isset($_POST["upload"])) {

    if ($_POST['yaziicidosya'] == "on") {
        $yaziicidosya=1;
    }else {
        $yaziicidosya=0;
    }


    include 'dosyaupload.php';

    

} else {
    
extract($_POST);
if($_POST){ 

$medyaKaydet=$db->prepare("update dokuman set baslik=:baslik where id=:id");
$medyaKaydet->execute(array(
    "id"=>$id,
    "baslik"=>$baslik
));
echo "OK";
}


extract($_GET);
if($_GET['islem'] == 1){ 

$medyaKaydet=$db->prepare("update dokuman set yaziicidosya=1 where id=:id");
$medyaKaydet->execute(array(
    "id"=>$id
));
echo "OK";
}
elseif($_GET['islem'] == 2){ 
    
$medyaKaydet=$db->prepare("update dokuman set yaziicidosya=0 where id=:id");
$medyaKaydet->execute(array(
    "id"=>$id
));
echo "OK";
}

elseif($_GET['islem'] == 3){ 

$dosyasor=$db->prepare("select * from dokuman where id=:id");
$dosyasor->execute(array("id"=>$id));
$dosyacek=$dosyasor->fetch(PDO::FETCH_ASSOC);

$dosyalink = $dosyacek['link'];
unlink("../" . $dosyalink . "");
    
$kalicisil=$db->prepare("delete from dokuman where id=:id");
$kalicisil->execute(array(
    "id"=>$id
));
echo "OK";
}

}

?>