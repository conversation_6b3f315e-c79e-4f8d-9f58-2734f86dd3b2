<?php
$ip = $_SERVER['REMOTE_ADDR'];
$kesik_ip = substr("$ip", 0, 6);
$kesik_ip_out = substr("$ip", 0, 11);

include ("iplist.php");
include ("head.php");
if (in_array($ip, $ip_array))
{
session_start();
if (isset($_SESSION['LAST_ACTIVITY']) && (time() - $_SESSION['LAST_ACTIVITY'] > 420)) {
    // last request was more than 30 minutes ago
    session_unset();     // unset $_SESSION variable for the run-time 
    session_destroy();   // destroy session data in storage
}
$_SESSION['LAST_ACTIVITY'] = time(); // update last activity time stamp

if ($_GET['logout'] == 'true')
{
    session_start();
    session_unset();
    session_destroy();
	setcookie("frm_user",$kullanici,time()-1);
}


print '<div id="govde">';


	
	if(isset($_POST['frm_user']) AND isset($_POST['frm_passwd']))
	{
		// INJECTION CONTROL
		$clean_frm_user_cift = strpos($_POST['frm_user'],"\"");	
		$clean_pass_cift = strpos($_POST['frm_passwd'],"\"");
		$clean_frm_user_tek = strpos($_POST['frm_user'],'\'');
		$clean_pass_tek = strpos($_POST['frm_passwd'],'\'');
		$clean_frm_user_eq = strpos($_POST['frm_user'],"=");	
		$clean_pass_eq = strpos($_POST['frm_passwd'],"=");
		$clean_frm_user_par = strpos($_POST['frm_user'],"(");	
		$clean_pass_par = strpos($_POST['frm_passwd'],"(");
		$clean_frm_user_par2 = strpos($_POST['frm_user'],")");	
		$clean_pass_par2 = strpos($_POST['frm_passwd'],")");
		
		if ($clean_pass_cift !== false or $clean_frm_user_cift !== false OR $clean_pass_tek !== false or $clean_frm_user_tek !== false){
			$sonuc.=  "<br><br><br><br><br><font color=red size=25>Tırnak işareti kullanamazsınız.</font><br />";
			echo $sonuc;
			exit;
		}elseif($clean_pass_eq !== false or $clean_frm_user_eq !== false){
			$sonuc.=  "<br><br><br><br><br><font color=red size=25>Eşittir işareti kullanamazsınız.</font><br />";
			echo $sonuc;
			exit;
		}elseif($clean_pass_par !== false or $clean_frm_user_par !== false OR $clean_pass_par2 !== false or $clean_frm_user_par2 !== false){
			$sonuc.=  "<br><br><br><br><br><font color=red size=25>Parantez işareti kullanamazsınız.</font><br />";
			echo $sonuc;
			exit;
		}else{	
		// INJECTION CONTROL END
		
			include ("fonksiyon.php");
			$frm_user = $_POST['frm_user'];
			$frm_passwd = md5($_POST['frm_passwd']);
			
			$sql		= "SELECT * FROM ".TABLO_USER." WHERE kullanici_adi='".$frm_user."' and sifre='".$frm_passwd."'";	
			$result		= mysqli_query($con,$sql);
			$num_rows	= mysqli_num_rows(mysqli_query($con,$sql));
			$row		= mysqli_fetch_array($result);	
			
			if (($num_rows > 0) OR (isset($_SESSION['frm_user'])))
			{
				ob_start();
				session_start();
				$_SESSION['frm_user']		= $row['kullanici_adi'];
				$_SESSION['user_id']		= $row['id'];
				$_SESSION['user_isim']		= $row['isim'];
				$_SESSION['user_soyisim']	= $row['soyisim'];
				$_SESSION['user_level']		= $row['yetki'];
				$_SESSION['LAST_ACTIVITY']	= time();				
				include ("top.php");
				include ("icerik.php");
			}
			else
			{
				include ("login.php");
			}
		}
	}
	else
	{
		if (isset($_SESSION['frm_user']))
		{
			include ("fonksiyon.php");
			include ("top.php");
			include ("icerik.php");
		}
		else
		{
			include ("login.php");
		}
	}
}else{
	print $ip;
	exit;
}
?>
</html>
