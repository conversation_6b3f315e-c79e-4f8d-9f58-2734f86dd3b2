// search input - autocomplete
$('.search-input--autocomplete input').on("keyup, click", function (e) {
    let ths = $(this);
    $(ths).dropdown('toggle');
    if ($(window).width() < 1000) {
        $('body').addClass('body-overflow-hidden');
    }

    if ($(ths).val().replace(/\s+/g, '').length >= 3) {

        //$('.search-input--autocomplete .input-text').html($(ths).val());
        $('.oneri-text').hide();
        $('.s-body').show();
    } else {
        $('.oneri-text').show();
        $('.s-body').hide();
    }

    //btn add active class 
    if ($(ths).val().replace(/\s+/g, '').length >= 1) {
        $('.btn-search-input').addClass('active');
    } else {
        $('.btn-search-input').removeClass('active');
    }

});

$('.search-input--default input').on("keyup", function (e) {
    let ths = $(this);
    $(ths).dropdown('toggle');
    if ($(window).width() < 1000) {
        $('body').addClass('body-overflow-hidden');
    }
});

$('.search-input--default .btn-clear-li').click(function () {
    let ths = $(this);
    $(ths).closest('.col-lg-6').find('ul').html('');
});
// search input - autocomplete
$('.search-input--autocomplete input').on("keyup", function (e) {
    let ths = $(this);
    $(ths).dropdown('toggle');
    if ($(window).width() < 1000) {
        $('body').addClass('body-overflow-hidden');
    }

    if ($(ths).val().replace(/\s+/g, '').length >= 3) {

        //$('.search-input--autocomplete .input-text').html($(ths).val());
        $('.aranan').hide();
        $('.oneri-text').hide();
        $('.s-body').show();
    } else {
        $('.oneri-text').show();
        $('.aranan').hide();
        $('.s-body').hide();
    }

    //btn add active class 
    if ($(ths).val().replace(/\s+/g, '').length >= 1) {
        $('.btn-search-input').addClass('active');
    } else {
        $('.btn-search-input').removeClass('active');
    } 
});
$('.search-input--autocomplete .close, .search-input--default .close ').click(function () {
    autocompleteClear($(this));
    $('.search-input--autocomplete input, .search-input--default input').dropdown('hide');
    $('body').removeClass('body-overflow-hidden');
});

function autocompleteClear(ths) {
    $(ths).closest('.search-input--autocomplete').find('input').val('');
    $(ths).closest('.search-input--autocomplete').find('input')[0].setAttribute("data-value", '');
    textBold('', '.search-input--autocomplete ul li a');
    $('.btn-search-input').removeClass('active');
}
$('.search-input--autocomplete .clear').click(function (e) {
    autocompleteClear($(this));
    $('.search-input--autocomplete .input-text').html('');
});

//Arama Sayfası AutoComplete
$('.search-input--autocompleteArama input').on("keyup", function (e) {
    let ths = $(this);
    $(ths).dropdown('toggle');
    if ($(window).width() < 1000) {
        $('body').addClass('body-overflow-hidden');
    }

    if ($(ths).val().replace(/\s+/g, '').length >= 3) {

        $('.search-input--autocompleteArama .input-text').html($(ths).val());
        $('.oneri-text').hide();
        $('.s-bodyArama').show();
    } else {
        $('.oneri-text').show();
        $('.s-bodyArama').hide();
    }

    //btn add active class 
    if ($(ths).val().replace(/\s+/g, '').length >= 1) {
        $('.btn-search-input').addClass('active');
    } else {
        $('.btn-search-input').removeClass('active');
    }

});
$('.search-input--autocompleteArama .close').click(function () {
    autocompleteAramaClear($(this));
    $('.search-input--autocompleteArama input').dropdown('hide');
    $('body').removeClass('body-overflow-hidden');
});

function autocompleteAramaClear(ths) {
    $(ths).closest('.search-input--autocompleteArama').find('input').val('');
    $(ths).closest('.search-input--autocompleteArama').find('input')[0].setAttribute("data-value", '');
    textBold('', '.search-input--autocompleteArama ul li a');
    $('.btn-search-input').removeClass('active');
}
$('.search-input--autocompleteArama .clear').click(function (e) {
    autocompleteAramaClear($(this));
    $('.search-input--autocompleteArama .input-text').html('');
});

/*select-box--full*/
$('.select-box--full .dropdown-menu ul a').click(function(){
	$(this).closest('.select-box--full').find('button p').text($(this).text());
});
$('.select-box--full .clear').click(function(e){
	$(this).closest('.select-box--full').find('.select-box--full__search-input input').val('');
    e.stopPropagation();
     textBold('', '.select-box--full ul li a');
});
$('.select-box--full__search-input input').on("keyup", function(e) {
   textBold($(this).val(), '.select-box--full ul li a')
});


//pagination size
$('.pagination-default a').each(function(){
    $(this).width($(this).width() + 4);
});



function textBold(text, content) {

  $.fn.wrapInTag = function(opts) {
  var tag = 'strong',
      words = opts.words || [],
      regex = RegExp(words.join('|'), 'gi'),
      replacement = '<'+ tag +'>$&</'+ tag +'>';
  
  return this.html(function() {
    return $(this).text().replace(regex, replacement);
    });
};
    $(content).wrapInTag({
        tag: 'em',
        words: [text]
    });

}

//media - video banner
$('.video--banner').click(function(){
    $(this).hide();
    $('#video').show();
    $('#video').get(0).play();
});


/* sticky */
$(function () {
    var w = $(window);
    var doc = $(document);
    var chtbt = $('.chatbot');
    var body = $('body');
    var onemliBilgilendirme = $('.onemli_bilgilendirme');
			  var navbar = $('.homepage-header,.header,.header-mobile');			 
              if(w.width() < 1000){   
                    var last = 0;
                  w.scroll(function(event){
                       var Pos = $(this).scrollTop();
                       if (Pos > last ){
                           navbar.removeClass('header-scroll');
                           body.removeClass('pt-70');
                       } else {
                           navbar.addClass('header-scroll');
                           body.addClass('pt-70');
                       }
                       last = Pos;
                    });
                }else{
                   w.scroll(function(){
			    	if(w.scrollTop() <=35){
                        navbar.removeClass('header-scroll');
                        //$('body').css('padding-bottom', '0');
                        $('body > .container').css('padding-top', '0');
                    } else {
                        $('body > .container').css('padding-top', '70px');
                        navbar.addClass('header-scroll');
			    	}
			      });
                }
                w.scroll(function() {
                   if(w.scrollTop() + w.height() >= doc.height()-250) {
                       chtbt.addClass('sticky');
                       if (body.hasClass("body-onemli_bilgilendirme")) {
                          chtbt.css('top', document.body.scrollHeight- (385 - onemliBilgilendirme.height()));
                      }else{
                          chtbt.css('top', document.body.scrollHeight- 400);
                      }
                	  
                   }else{
                      chtbt.removeClass('sticky');
                      chtbt.css('top', 'initial');
                   }
                });
});


/* mobile menu*/
$('.header-mobile .dropdown-menu').click(function(e){
    e.stopPropagation();
});

//homepage header z-index
$('.homepage-header').hover(
    function () { $(this).addClass('hover') },
    function () { $(this).removeClass('hover') }
);

$('.header-mobile .dropdown-menu ul li .menu_href').click(function () {
    $(this).closest('li').find('.sub_menu-box').toggle();
    if ($(this).closest('li').hasClass("active")) {
        $(this).closest('li').removeClass('active')
    } else {
        $(this).closest('li').addClass('active')
    }

});

/*memnuniyet anketi button active class*/
$('.btn-memnuniyet-anketi').click(function(){
    $(this).addClass('active');
});
$('#modal-memnuniyet-anketi').on('hidden.bs.modal', function(){
     $('.btn-memnuniyet-anketi').removeClass('active');
});


/*memnuniyet anketi step*/
$('.step-content .next-button').click(function(){
    if(!$(this).hasClass('disabled')){
        let ths = $(this);
        let step = ths.data('step');
        nextStep(step);
    }    
});
$('.step-bar').on('click', '.filled', function() {
     let ths = $(this).find('a');
        let next = ths.data('step');
         nextStep(next -1);
});
function nextStep(step){
    $('.step-content .item').hide();
    $('.step-content .item').eq(step).show();
    $('.step-bar .active').addClass('filled');
    $('.step-bar .active').removeClass('active');
    $('.step-bar li').eq(step).addClass('active');
}

$('.survey li a').click(function(){
    $(this).closest('.survey').find('a').removeClass('active');

     $(this).closest('.survey').find('a img').map(function() {
        $(this).attr('src', $(this).data('img'));
    })
    $(this).addClass('active');
     $(this).find('img').attr('src', $(this).find('img').data('imghover'));
    $(this).closest('.item').find('.next-button').removeClass('disabled');
});
$('.step-content .item .input-checkbox input').change(function() {
        if($('.step-content .item .input-checkbox input').is(":checked")) {
             $(this).closest('.item').find('.next-button').removeClass('disabled');
        }else{
         $(this).closest('.item').find('.next-button').addClass('disabled');
        }     
 });

 
//tooltip
$('[data-toggle="tooltip"]').tooltip();

//SSS page content show
$("a[data-url]").click(function() {
    let ths = $(this);
    let page = ths.data('url');
    $('.menu-left .active').removeClass('active')
    ths.addClass('active');
    $('.page_content-hide').hide();
    $('.'+page).show();
});


//html5 video pause
$('#carouselMedia').on('slide.bs.carousel', function () {
    if ($('#video').get(0) != undefined) {
        $('#video').get(0).pause();
    }
});

// Arama Sonuçları 
$(".ul-search-filter a[data-url]").click(function() {
    let ths = $(this);
    let page = ths.data('url');
    $(this).closest('.ul-search-filter').find('a').removeClass('active');
     ths.addClass('active');
    if(page == 'all'){
        $('.page_content-hide').show();
    }else{
        $('.page_content-hide').hide();
        $('.'+page).show();
    }
});



//önemli bilgilendirme close buton
  $('.onemli_bilgilendirme .close').click(function(){
    $(this).closest('.onemli_bilgilendirme').hide();
    $('body').removeClass('body-onemli_bilgilendirme');
});



//homepage responsive fixed problem firefox
//if($(window).width() < 700){   
//    $('.modal-dialog').css('width',$(window).width() - 15);
//    $('.onemli_bilgilendirme, .header-mobile').css('width',$(window).width());
//}

//yetkili elektrikci listesi mobile filter
  $('.button-filtrele').click(function(){
      $('.filter-box-left').show();
      if($(window).width() < 1000){   
            $('body').css('overflow-y','hidden');
        }
});
 $('.filter-box-left .close').click(function(){
     $('.filter-box-left').hide();
     if ($(window).width() < 1000) {
         $('body').css('overflow-y', 'auto');
     }
});

//fileInput
function fileInputImageShow(input) {
    $(input).closest('.file-input').find('.img-show').show();
    $(input).closest('.file-input').find('.img-button').hide();
    if (input.files[0].type == "application/pdf") {
        $(input).closest('.file-input').find('.preview').attr('src', 'img/pdf-icon.png');
        $(input).closest('.file-input').find('.show-file').show();
    } else if (input.files[0].type == "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") {
        $(input).closest('.file-input').find('.preview').attr('src', 'img/excel-icon.png');
        $(input).closest('.file-input').find('.show-file').show();
    } else {
        var reader = new FileReader();
        reader.onload = function (e) {
            $(input).closest('.file-input').find('.preview').attr('src', e.target.result);
        }
        reader.readAsDataURL(input.files[0]); // convert to base64 string
    }
}

//Koray bu bölüm, ilgili sayfalardaki limit farklılıkları nedeniyle, dosya eklenen sayfalara taşındı
//$(document.body).on("change", ".file-input input[type=\"file\"]", function () {
//    fileInputImageShow(this);
//});
//document
$(document).on("click", ".file-input .img-button", function () {
    $(this).closest('.file-input').find('input[type="file"]').trigger('click');
});
$(document).on("click", '.file-input .remove', function () {
    $(this).closest('.file-input').find('.img-show').hide();
    $(this).closest('.file-input').find('.img-button').show();
    $(this).closest('.file-input').find('input[type="file"]').val('');
    $(this).closest('.file-input').find('.show-file').hide();
});

$(document).on('click', '.show-file', function () {
    ShowFile($(this).closest('.file-input').find('input[type="file"]'));
});

function ShowFile(fileObject) {

    var self = fileObject;
    if (self.prop('files') && self.prop('files')[0]) {

        var file = self.prop('files')[0];

        if (file.type == "application/pdf") {
            var fr = new FileReader();
            fr.readAsDataURL(file);
            // It is necessary to create a new blob object with mime-type explicitly set
            // otherwise only Chrome works like it should
            var newBlob = new Blob([file], { type: "application/pdf" })

            // IE doesn't allow using a blob object directly as link href
            // instead it is necessary to use msSaveOrOpenBlob
            if (window.navigator && window.navigator.msSaveOrOpenBlob) {
                window.navigator.msSaveOrOpenBlob(newBlob);
                return;
            }

            // For other browsers: 
            // Create a link pointing to the ObjectURL containing the blob.
            var data = window.URL.createObjectURL(newBlob);
            window.open(data);
        } else if (file.type == "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") {
            var fr = new FileReader();
            fr.readAsDataURL(file);
            // It is necessary to create a new blob object with mime-type explicitly set
            // otherwise only Chrome works like it should
            var newBlob = new Blob([file], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" })

            // IE doesn't allow using a blob object directly as link href
            // instead it is necessary to use msSaveOrOpenBlob
            if (window.navigator && window.navigator.msSaveOrOpenBlob) {
                window.navigator.msSaveOrOpenBlob(newBlob);
                return;
            }

            // For other browsers: 
            // Create a link pointing to the ObjectURL containing the blob.
            var data = window.URL.createObjectURL(newBlob);
            window.open(data);
        }
    }
}

//search input hover width
$(".search-input-focus").hover(
  function () {
    $(this).addClass('active');

  }, 
  function () {
    $(this).removeClass("active");
    $(".dropdown-menu.show, .search-input.show").removeClass("show");

  }
);

$("[target='_top']").click(function() {
   window.open($(this).attr('href'),'mywindow','width="'+window.getWidth()+'",height="'+window.getHeight()+'"');
});
$('.kariyer-box').click(function(){
    window.location.href= $(this).find('.detay-link').attr('href');
});
$('.link-click').click(function(){
    window.location.href= $(this).find('a').attr('href');
});

//homepage-bilgilendirme height fixed
setTimeout(function(){ 
    let hb1 = $('.homepage-bilgilendirme .homepage-bilgilendirme-item:eq(0)');
    let hb2 = $('.homepage-bilgilendirme .homepage-bilgilendirme-item:eq(1)');
    var width = $(window).width();
    if (width >= 975) {
        if (hb1.height() >= hb2.height()) {
            $('.homepage-bilgilendirme .homepage-bilgilendirme-item').css('height', hb1.height());
        } else {
            $('.homepage-bilgilendirme .homepage-bilgilendirme-item').css('height', hb2.height() + 40);
        }
    }
}, 1000);


//arama sonucu clear
$('.arama_sonucu img').click(function(){
	
	$.ajaxSetup({
        async: false
    });

    $('.arama_sonucu').remove();
    $('.search-input input').val('');

    SssFormSubmit();
	
    $.ajaxSetup({
        async: true
    });
});


$('.header-mobile .dropdown').on('show.bs.dropdown', function () {
    $('.header-mobile').addClass('header-fixed');
})
$('.header-mobile .dropdown').on('hide.bs.dropdown', function () {
   $('.header-mobile').removeClass('header-fixed');
});

//Arama

//Statik Liste
var fuseStaticlist;
///SSS Listesi
var fuseSssList;
///Sayfalar
var fuseSayfaList;
///Medya Listesi
var fuseMedyaList;
///Yasal Bildirimler Listesi
var fuseYasalList;

//Statik Liste
var ajaxStaticlist;
///SSS Listesi
var ajaxSssList;
///Medya Listesi
var ajaxMedyaList;
///Yasal Bildirimler Listesi
var ajaxYasalList;

const options = {
    includeMatches: true,
    threshold: 0.2,
    ignoreLocation: true,
    keys: ['name', 'keywords']
}
const options2 = {
    includeMatches: true,
    threshold: 0,
    ignoreLocation: true,
    keys: ['metin', 'soru', 'kategoriAdi']
}
const optionsYasal = {
    includeMatches: true,
    threshold: 0.1,
    ignoreLocation: true,
    keys: ['adi', 'aciklama', 'yasalBildirimAltBaslikAdi', 'yasalBildirimKategoriAdi']
}
const optionsMedya = {
    includeMatches: true,
    threshold: 0.2,
    ignoreLocation: true,
    keys: ['adi', 'aciklama']
}


    ajaxSssList = $.ajax({
        type: "GET",
        url: "/all-sss",
        async: true,
        success: function (res) {
            fuseSssList = new Fuse(res, options2);
        },
        error: function (err) {
        }
    });

    ajaxYasalList = $.ajax({
        type: "GET",
        url: "/all-yasal-bildirim",
        async: true,
        success: function (res) {
            fuseYasalList = new Fuse(res, optionsYasal);
        },
        error: function (err) {
        }
    });

    ajaxMedyaList = $.ajax({
        type: "GET",
        url: "/all-genel-arama",
        async: true,
        success: function (res) {
            fuseMedyaList = new Fuse(res, optionsMedya);
        },
        error: function (err) {
        }
    });

    ajaxStaticlist = $.ajax({
        type: "GET",
        url: "/arama/islemler-listesi",
        async: true,
        success: function (res) {
            fuseStaticlist = new Fuse(res, options);
        },
        error: function (err) {
        }
    });

    var sayfalarListForSearch = [
        { url: "/is-ilanlari", name: "İş İlanları", keywords: ["iş", "ilan", "kariyer"] },
        { url: "/basin-bultenleri", name: "Haberler ve Duyurular", keywords: ["Duyuru", "Haber", "medya"] },
        { url: "/guvenlik-onlemleri", name: "Güvenlik Önlemleri", keywords: ["önlem", "tedbir"] },
        { url: "/bizden-haberler", name: "Bizden Haberler", keywords: ["Duyuru", "medya"] },
        { url: "/sonuclanan-ihaleler", name: "Sonuçlanan İhaleler", keywords: ["ihale"] },
        { url: "/teminat-iade-talep-form", name: "Teminat İade Talep Başvurusu", keywords: ["Başvuru", "iade", "form", "hizmetler"] },
        { url: "/kisa-liste-on-basvuru-form", name: "İhale Kısa Liste Ön Başvurusu", keywords: ["başvuru", "ihale", "form", "hizmetler"] },
        { url: "/sayac-bedeli-tazminati-sorgulama", name: "Sayaç Bedeli Geri Ödeme Başvuru Sorgulama", keywords: ["ödeme", "hizmetler"] },
        { url: "/sayac-bedeli-tazminati-basvuru", name: "Sayaç Bedeli Geri Ödeme Başvuru Formu", keywords: ["ödeme", "hizmetler"] },
        { url: "/site-haritasi", name: "Site Haritası", keywords: [] },
        { url: "/stajlar", name: "Stajlar", keywords: ["stajer", "kariyer"] },
        { url: "/tazminat-sorgulama-ve-basvuru", name: "Tazminat Sorgulama ve Başvuru", keywords: ["ödeme", "hizmetler", "tazminat"] },
        { url: "/yasal-bildirim/KVKK", name: "Yasal Bildirimler", keywords: ["raporlar", "yayınlar"] },
        { url: "/yayindaki-ihaleler", name: "Yayındaki İhaleler", keywords: ["ihale"] },
        { url: "/ihale-arsivi", name: "İhale Arşivi", keywords: ["ihale"] },
        { url: "/standart-baglanti-durum-sorgulama", name: "Yeni Standart Bağlantı Hattı Durumu Sorgulama", keywords: ["hizmetler"] },
        { url: "/standart-baglanti-yeni-basvuru", name: "Yeni Standart Bağlantı Hattı Başvuru", keywords: ["form", "hizmetler"] },
        { url: "/yetkili-elektrikci", name: "Yetkili Elektrikçiler", keywords: [] },
        { url: "/iletisim", name: "İletişim", keywords: ["Operasyon Merkezi", "Ulaşım", "Telefon", "Harita", "adres"] }
    ];
    $.ajax({
        type: "GET",
        url: "/arama/sayfa-listesi",
        async: true,
        success: function (res) {
            sayfalarListForSearch = sayfalarListForSearch.concat(res);
            fuseSayfaList = new Fuse(sayfalarListForSearch, options);
        }
    });



window.addEventListener('load', function () {
    var inp = document.querySelectorAll('input');
    for (var i = 0; i < inp.length; i++) {
        inp[i].addEventListener('change', function () {
            this.setAttribute("data-value", this.value);
        });
        if (inp[i].value) {
            inp[i].setAttribute("data-value", inp[i].value);
        }
    }
});

$('input').on('blur', function () {
    this.setAttribute("data-value", this.value);
});

//tooltip
$('[data-toggle="tooltip"]').tooltip();
$('[data-toggle="popover"]').popover();

$(document).ready(function () {

    var $window = $(window);
    $('.tesisat-no-nedir-img').on('click', function (e) {
        if ($window.width() <= 600) {
            $('#tesisat-no-nedir').modal('show');
            $('[data-toggle=popover]').popover('hide');
        }
    });
});

$('.collapse').on('show.bs.collapse', function () {
    let ths = $(this);
    setTimeout(function () { 
        var panel = $(ths).closest('.card');
        if (panel) {
            $('html, body').animate({
                scrollTop: panel.offset().top - 40
            }, 500);
        }
    }, 200);
});

//firefox safari line-clamp fix
$(document).ready(function () {
    $('.line-clamp-1, .line-clamp-2, .line-clamp-3, .line-clamp-4, .line-clamp-5, .line-clamp-6, .line-clamp-11').children().not('button').not(':first-child').remove();
});

$('#dropdownMenuButton').click(function () {
    $('body').addClass('body-loading');
});
$('.menu-header .btn-menu-close').click(function () {
    $('body').removeClass('body-loading');
});

$('textarea').on("input", function () {
    var maxLength = $(this).attr("maxlength");
    var currentLength = $(this).val().length;
    $(this).siblings('.char_count_js')[0].innerHTML = currentLength + "/" + maxLength;
    if (currentLength >= maxLength) {
        return console.log("max karakter limiti");
    }
});

function SetCerezAccepted(mandatory, functional, performance, domain) {
    Cookies.remove('cerez_accepted_v1', { domain: domain } );
    Cookies.set('cerez_accepted_v1', "{mandatory:" + mandatory + ",functional:" + functional + ",performance:" + performance + "}", { expires: 365, secure: true, domain: domain });
    $('.footer_cerezpolitikasi').hide();
}

function htmlEncode(str) {
    return String(str).replace(/[^\w. ıİğĞüÜşŞöÖçÇ]/gi, function (c) {
        return '&#' + c.charCodeAt(0) + ';';
    });
}