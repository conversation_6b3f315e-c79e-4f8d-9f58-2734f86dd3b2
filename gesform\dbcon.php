<?php
	define("DB_USER", "ges");				// <-- veritaban<PERSON> kullanıcı adı
	define("DB_PASS", "72I3Oav9rTnzuuqx~");		// <-- veritabanı kullanıcı parolası
	define("DB_NAME", "kayseriosb_org_ges");		       	// <-- veritabanı ismi
	define("DB_HOST", "localhost");		    // <-- veritabanı hostname
	define("TABLO", "ges_veri");				// <-- veritabanı tablosu
 
$con = mysqli_connect(DB_HOST, DB_USER, DB_PASS, DB_NAME);
if (!$con) {
       die('Could not connect: ' . mysql_error());
}
if($con === false)
{
    die("ERROR: Could not connect. " . mysqli_connect_error());
}


function tr_strtolower_en($text)
{
    $search=array("ı","ç","ş","ö","ü","ğ","İ","Ç","Ş","Ö","Ü","Ğ");
    $replace=array("i","c","s","o","u","g","i","c","s","o","u","g");
    $text=str_replace($search,$replace,$text);
    $text=strtolower($text);
    return $text;
}

?>