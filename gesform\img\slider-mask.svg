<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="1366" height="675" viewBox="0 0 1366 675">
    <defs>
        <linearGradient id="1ve6qnw3sb" x1="0%" x2="100%" y1="62.712%" y2="37.791%">
            <stop offset="0%" stop-color="#FF8000"/>
            <stop offset="100%" stop-color="#FFC522"/>
        </linearGradient>
        <linearGradient id="0k999z7y0e" x1="0%" x2="98.806%" y1="0%" y2="100%">
            <stop offset="0%" stop-color="#FFC522"/>
            <stop offset="100%" stop-color="#FF8000"/>
        </linearGradient>
        <filter id="r6v9rcyw5c" width="133.4%" height="133.4%" x="-16.7%" y="-16.7%" filterUnits="objectBoundingBox">
            <feGaussianBlur in="SourceGraphic" stdDeviation="50"/>
        </filter>
        <path id="c0ewuxzz3a" d="M0 0H1366V675H0z"/>
    </defs>
    <g fill="none" fill-rule="evenodd">
        <g>
            <g transform="translate(-294 -6582) translate(294 6582)">
                <mask id="ka943qirad" fill="#fff">
                    <use xlink:href="#c0ewuxzz3a"/>
                </mask>
                <use fill="url(#1ve6qnw3sb)" fill-opacity=".6" xlink:href="#c0ewuxzz3a"/>
                <circle cx="235" cy="426" r="449" fill="#FFD522" fill-opacity=".238" filter="url(#r6v9rcyw5c)" mask="url(#ka943qirad)"/>
                <path fill="url(#0k999z7y0e)" d="M-123.515-382.515l1205.03 1205.03c4.686 4.686 4.686 12.284 0 16.97-2.25 2.25-5.303 3.515-8.486 3.515H-132c-6.627 0-12-5.373-12-12V-374.03c0-6.627 5.373-12 12-12 3.183 0 6.235 1.265 8.485 3.515z" style="mix-blend-mode:overlay" mask="url(#ka943qirad)" opacity=".228"/>
            </g>
        </g>
    </g>
</svg>
