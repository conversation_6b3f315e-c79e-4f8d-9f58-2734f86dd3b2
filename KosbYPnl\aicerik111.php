<?php
include "../db/baglanti.php";
extract($_GET);
$sayfatip = 10;
$sayfatipi = 10;
$tablo = "aciliricerik";


if (!isset($_SESSION['kullanici_adi'])) {
    header("Location:giris.php");
    exit;
}

$kullanici_adi = $_SESSION['kullanici_adi'];
$kullaciSor = $db->prepare("SELECT * FROM yonetim WHERE kullanici_adi = ?");
$kullaciSor->execute(array($kullanici_adi));
$kullaniciCek = $kullaciSor->fetch(PDO::FETCH_ASSOC);

if (!$kullaniciCek) {
    header("Location:giris.php"); 
    exit;
}

if ($_POST['islem'] == 1) {

    $id = $_POST['id'];
    $icresimcek = $db->prepare("SELECT * FROM $tablo WHERE id=:id"); // Menüye ait iç resimleri çağırıp sileceğiz.
    $icresimcek->execute(array("id" => $id));
    foreach ($icresimcek as $icresim) {
        $resimlink = $icresim['resim'];
        unlink('../' . $resimlink);
    }
    $menusil = $db->prepare("DELETE FROM $tablo WHERE id=:id");
    $menusil->execute(array("id" => $id));
} else {
    //MENÜ SİLME İŞLEMİ YOKSA SAYFAYI YÜKLÜYORUZ -- EĞER SİLME İŞLEMİ VARSA AŞAĞISI YÜKLENMİYOR... YANİ ASIL SAYFA KODLARI AŞAĞIDAN BAŞLIYOR.

    require 'header.php';
    require 'solmenu.php';
    include "../db/baglan.php";

    $yonlenurl = $_POST['url'];

    $dilSorgusu = $db->query("SELECT kisaltma FROM dil");
    $diller = $dilSorgusu->fetchAll(PDO::FETCH_COLUMN);

    /*********************************************************** Yeni Menü Ekleme Alanı ***********************************************************************/
    if (isset($_POST['ekle'])) {
        $yenimenu = "1";
        include 'icresimyukle.php';

        foreach ($_POST as $column => $value) {
            if ($column != "url" & $column != "ekle" & $column != "id") {
                // Sütun varlığını kontrol et
                $SutunVarmi = $db->prepare("DESCRIBE $tablo");
                $SutunVarmi->execute();
                $Sutunlar = $SutunVarmi->fetchAll(PDO::FETCH_COLUMN);
                if (!in_array($column, $Sutunlar)) {
                    // Sütun yoksa oluştur
                    $db->exec("ALTER TABLE $tablo ADD COLUMN $column TEXT");
                }

                if (strpos($column, "icerik_") !== false) {
                    $value = YazidakiResimleriIndir($value);
                    $value = str_replace('&nbsp;', '', $value);
                } 

                if (strpos($column, "baslik_") !== false) {
                    $value = str_replace('"', "'", $value);
                }

                $value = addslashes($value);

                $keys[] = "{$column}";
                $values[] = "'{$value}'";
            }
        }


        $seoSutunlar = [];
        $seoDegerleri = [];
        foreach ($diller as $dil) {
            $seoSutunlar[] = "seo_$dil";
            $seoDegerleri[] = "'" . SEOLink($_POST["baslik_$dil"]) . "'";
        }

        $sutunlar = implode(",", $keys) . "," . implode(",", $seoSutunlar);
        $degerler = implode(",", $values) . "," . implode(",", $seoDegerleri);

        
        try {
            $sql = "INSERT INTO $tablo ($sutunlar, resim, dosya) 
        VALUES ($degerler, '$resim', '$dosya')";

            $kayit_ekle = $db->exec($sql);

            Basarili("Açılır içerik başarıyla eklendi!");
        } catch (PDOException $e) {
            // Veritabanı hatası oluştuğunda burası çalışır
            Hata("Veritabanı hatası: <br>" . $e->getMessage());
        } catch (Exception $e) {
            // Diğer hatalar için burası çalışır
            Hata("Beklenmeyen bir hata oluştu: <br>" . $e->getMessage());
        }


    }
    /*********************************************************** Menü Ekleme Alanı Sonu ***********************************************************************/


    /*********************************************************** Menü Düzenleme Alanı ***********************************************************************/
    if (isset($_POST['duzenle'])) {
        $yenimenu = "0";
        $id = $_POST['id'];

        $fields = array(
            'resim' => 'resimlink',
            'dosya' => 'dosyalink',
        );

        foreach ($fields as $postKey => $linkVar) {
            if ($_POST[$postKey . 'sil'] == "on") {
                $columnName = str_replace('sil', '', $postKey); // 'sil' son eki kaldırarak sütun adını elde ediyoruz
                $logosil = $db->prepare("UPDATE $tablo SET $columnName = '' WHERE id = :id");
                $logosil->execute(array("id" => $id));
                unlink("../" . $_POST[$linkVar]);
            }
        }


        include 'icresimyukle.php';

        $setIfadesi = "";

        foreach ($diller as $dil) {
            $setIfadesi .= "seo_$dil = '" . SEOLink($_POST["baslik_$dil"]) . "', ";
        }


        foreach ($_POST as $column => $value) {

            if ($column != "url" & $column != "duzenle" & $column != "resim1link" & $column != "resim2link" & $column != "resim1sil" & $column != "resim2sil" & $column != "dosyalink" & $column != "dosyasil") {

                $SutunVarmi = $db->prepare("DESCRIBE $tablo");
                $SutunVarmi->execute();
                $Sutunlar = $SutunVarmi->fetchAll(PDO::FETCH_COLUMN);
                if (!in_array($column, $Sutunlar)) {
                    // Sütun yoksa oluştur
                    $db->exec("ALTER TABLE $tablo ADD COLUMN $column TEXT");
                }


                if (strpos($column, "icerik_") !== false) {
                    $value = YazidakiResimleriIndir($value);
                    $value = str_replace('&nbsp;', '', $value);
                }

                if (strpos($column, "baslik_") !== false) {
                    $value = str_replace('"', "'", $value);
                }
                $value = addslashes($value);

                $keys[] = "{$column}";
                $values[] = "'{$value}'";
                $setIfadesi .= "$column = '$value', ";
            }
        }
        


        try {
            $setIfadesi = rtrim($setIfadesi, ', '); // Son virgülü kaldır
            // Diğer verileri manuel olarak ekliyoruz
            $duzenleSorgusu = "UPDATE $tablo SET 
            resim='$resim', 
            dosya='$dosya', 
            $setIfadesi 
            WHERE id = $id";

            $duzenle = $db->query($duzenleSorgusu);

            Basarili("Açılır içerik başarıyla düzenlendi.");
        } catch (PDOException $e) {
            // Veritabanı hatası oluştuğunda burası çalışır
            Hata("Veritabanı hatası: " . $e->getMessage());
        } catch (Exception $e) {
            // Diğer hatalar için burası çalışır
            Hata("Beklenmeyen bir hata oluştu: " . $e->getMessage());
        }


    }

    /*********************************************************** Menü Düzenleme Alanı Sonu ***********************************************************************/


    $yetki = $db->prepare("SELECT * FROM yonetim WHERE kullanici_adi=:id");
    $yetki->execute(array("id" => $_SESSION['kullanici_adi']));
    $yetkicek = $yetki->fetch(PDO::FETCH_ASSOC);

    if ($yetkicek['aciliricerik'] == "1") {

        $url = 'https://' . $_SERVER['SERVER_NAME'] . $_SERVER['REQUEST_URI'];

        $uid = $_GET['uid'];
        $id = $_GET['id'];
        $st = $_GET['st'];


        if ($_GET["id"]) {
            $MenuSor = $db->prepare("SELECT * FROM $tablo WHERE id=$id");
            $MenuSor->execute();
            $MenuVeri = $MenuSor->fetch(PDO::FETCH_ASSOC);
        }

?>


        <div class="main-content app-content mt-0">
            <div class="side-app">

                <div id="gizle" class="main-container container-fluid <?= isset($_GET['id']) ? " gizle" : "goster"; ?>">
                    <div class="page-header">

                        <div>
                            <?php if (empty($_GET['id'])) { ?>
                                <button type="button" id="gizle" onclick="menuler()" class="btn btn-green"><i class="fe fe-plus me-2"></i>Yeni Açılır İçerik Ekle</button>
                            <?php } else { ?>
                                <a href="aicerik.php?uid=<?php echo $_GET['uid']; ?>&st=<?php echo $st ?>"><button type="button" class="btn btn-green"><i class="fe fe-plus me-2"></i>Yeni Açılır İçerik Ekle</button></a>
                                <button type="button" id="gizle" onclick="menuler()" class="btn btn-gray"><i class="fe fe-edit me-2"></i>Açılır İçeriği Düzenlemeye Devam Et</button>
                            <?php } ?>
                        </div>

                        <div>
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="javascript:void(0)">Anasayfa</a></li>
                                <li class="breadcrumb-item active" aria-current="page">Açılır İçerikler</li>
                            </ol>
                        </div>
                    </div>


                    <div class="row ">
                        <div class="col-md-12 ">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Açılır İçerikler</h3>
                                </div>
                                <div class="card-body">

                                    <div class="table-responsive">
                                        <table class="table text-nowrap text-md-nowrap mb-0">
                                            <thead>
                                                <tr>
                                                    <th>Resim</th>
                                                    <th>Başlık</th>
                                                    <?php if (AktifKontrol("kisiekle")) { ?>
                                                        <th class="text-center">Kişi Ekle</th>
                                                    <?php } ?>
                                                    <?php if (AktifKontrol("dokuman")) { ?>
                                                        <th class="text-center">Döküman</th>
                                                    <?php } ?>
                                                    <?php if (AktifKontrol("resimler")) { ?>
                                                        <th class="text-center">Resimler</th>
                                                    <?php } ?>
                                                    <?php if (AktifKontrol("videolar")) { ?>
                                                        <th class="text-center">Videolar</th>
                                                    <?php } ?>
                                                    <th class="text-center">Düzenle</th>
                                                    <th class="text-center">Sil</th>

                                                </tr>
                                            </thead>
                                            <tbody>


                                                <?php
                                                function kategoriListe($id, $tab)
                                                {
                                                    global $db, $sayfatip, $tablo;

                                                    $acilirid = $_GET['uid'];
                                                    $sayfatipi = $_GET['st'];

                                                    $kod = "SELECT *,
                                                    (SELECT COUNT(A.id) FROM $tablo AS A WHERE A.ustmenu=K.id and A.menuid=$acilirid) as altKategoriSayisi
                                                    FROM $tablo AS K
                                                    WHERE K.ustmenu=:id and K.menuid=:acilirid and K.sayfatipi=:sayfatip
                                                    ORDER BY menu_sira";

                                                    $stmt = $db->prepare($kod);
                                                    $stmt->execute(array('id' => $id, 'sayfatip' => $sayfatipi, 'acilirid' => $acilirid));


                                                    while ($veri = $stmt->fetch(PDO::FETCH_ASSOC)) { ?>

                                                        <?php if ($veri['ustmenu'] == '0') { ?>
                                                            <tr id='Rowai<?= $veri["id"] ?>' class='usttablohover'>
                                                            <?php } else { ?>
                                                            <tr id='Rowai<?= $veri["id"] ?>' class='tablohover'>
                                                            <?php } ?>

                                                            <?php if ($veri['resim'] == '') { ?>
                                                                <td><img class='w-7 h-7' src='../images/images/resimyok.png'></td>
                                                            <?php } else { ?>
                                                                <td><img class='w-7 h-7' src='../<?= $veri["resim"] ?>'></td>
                                                            <?php } ?>

                                                            <?php if ($veri['ustmenu'] == '0') { ?>
                                                                <td>
                                                                <?php } else { ?>
                                                                <td class='' style='padding-left:<?= $tab ?>px !important'><i class='fe fe-corner-down-right'></i>
                                                                <?php } ?>
                                                                <?= kes($veri["baslik_tr"], 85) ?>
                                                                </td>

                                                                <?php if (AktifKontrol("kisiekle")) { ?>
                                                                    <td class="text-center"><a href="acilirkisiekle.php?uid=<?= $veri['id'] ?>&st=<?= $sayfatip ?>"> <button class="btn btn-default" data-bs-effect="effect-scale" data-bs-toggle="modal">Kişi Ekle</button></a></td>
                                                                <?php } ?>

                                                                <?php if (AktifKontrol("dokuman")) { ?>
                                                                    <td class="text-center"><button class="btn btn-outline-default" data-bs-effect="effect-scale" data-bs-toggle="modal" onclick="javascript:ModalID_dosya(<?= $veri['id'] ?>)" href="#dosyaekle"><i class="fa fa-folder me-2"></i> Dosya Ekle</button></td>
                                                                <?php } ?>

                                                                <?php if (AktifKontrol("resimler")) { ?>
                                                                    <td class="text-center"><button class="btn btn-outline-default" data-bs-effect="effect-scale" data-bs-toggle="modal" onclick="javascript:ModalID(<?= $veri['id'] ?>)" href="#resimekle"><i class="fa fa-file-photo-o me-2"></i> Resim Ekle</button></td>
                                                                <?php } ?>

                                                                <?php if (AktifKontrol("videolar")) { ?>
                                                                    <td class="text-center"><button class="btn btn-outline-default" data-bs-effect="effect-scale" data-bs-toggle="modal" onclick="javascript:ModalID_video(<?= $veri['id'] ?>)" href="#videoekle"><i class="fa fa-file-video-o me-2"></i> Video Ekle</button></td>
                                                                <?php } ?>

                                                                <td class='text-center'><a href='aicerik.php?uid=<?= $acilirid ?>&id=<?= $veri["id"] ?>&st=<?= $veri["sayfatipi"] ?>'><i class='fe fe-edit fs-25 text-green'></i></a></td>
                                                                <td class='text-center'><a style='cursor: pointer' onclick='javascript:AciliricerikSilConfirm(<?= $veri["id"] ?>)' name='sil'><i class='fe fe-trash-2 fs-25 text-danger'></i></a></td>
                                                            </tr>

                                                    <?php if ($veri["altKategoriSayisi"] > 0) {
                                                            kategoriListe($veri["id"], $tab + 30);
                                                        }
                                                    }
                                                }

                                                kategoriListe(0, 10); ?>



                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="goster" class="main-container container-fluid <?= isset($_GET['id']) ? " goster" : "gizle"; ?>">
                    <div class="page-header">
                        <div>
                            <?php if (isset($_GET['id'])) { ?>
                                <a href="aicerik.php?uid=<?php echo $_GET['uid']; ?>&st=<?php echo $st ?>"><button type="button" class="btn btn-green"><i class="fe fe-plus me-2"></i>Yeni Açılır İçerik Ekle</button></a>
                            <?php } ?>
                            <button type="button" id="goster" onclick="menuekle()" class="btn btn-btn btn-gray"><i class="fe fe-list me-2"></i>Açılır İçerikleri Listele</button>
                        </div>
                        <div>
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="javascript:void(0)">Anasayfa</a></li>
                                <li class="breadcrumb-item active" aria-current="page">Açılır İçerik Ekle</li>
                            </ol>
                        </div>
                    </div>



                    <div class="row ">
                        <div class="col-md-12 ">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title"><?= isset($_GET['id']) ? "Açılır İçerik Düzenle" : "Açılır İçerik Ekle" ?></h3>
                                </div>
                                <div class="card-body">
                                    <div class="card-pay">
                                        <ul class="tabs-menum nav">
                                            <?php if (isset($_GET['id'])) { ?>
                                                <li class=""><a href="#menuayar" class="active" data-bs-toggle="tab"><i class="fa fa-list"></i> Menü Ayarları</a></li>
                                                <?php if (AktifKontrol("resimler")) { ?>
                                                    <li><a href="#resimler" data-bs-toggle="tab"><i class="fa fa-image"></i>Resimler</a></li>
                                                <?php }
                                                if (AktifKontrol("dokuman")) { ?>
                                                    <li><a href="#dokuman" data-bs-toggle="tab"><i class="fa fa-file"></i> Dökümanlar</a></li>
                                                <?php }
                                                if (AktifKontrol("videolar")) { ?>
                                                    <li><a href="#videolar" data-bs-toggle="tab"><i class="fa fa-video-camera"></i> Videolar</a></li>
                                                <?php } ?>
                                            <?php } ?>
                                        </ul>
                                        <div class="tab-content">

                                            <div class="tab-pane show active" id="menuayar">
                                                <form action="" method="POST" enctype="multipart/form-data" id="demo-form2" data-parsley-validate class="form-horizontal form-label-left">
                                                    <input type="hidden" name="id" value="<?php echo $_GET['id']; ?>">
                                                    <input type="hidden" name="menuid" value="<?php echo $_GET['uid']; ?>">
                                                    <input type="hidden" name="sayfatipi" value="<?php echo $_GET['st']; ?>">
                                                    <input type="hidden" name="url" value="<?php echo $url; ?>">

                                                    <div class="row">
                                                        <div class="panel panel-primary">
                                                            <div class="row">
                                                                <!-- Menu Sıra -->
                                                                <div class="form-group col-md-4 col-sm-4 col-xs-12">
                                                                    <label class="control-label" for="first-name">Menu Sıra<span class="required">*</span>
                                                                    </label>
                                                                    <div>
                                                                        <?php if ($_GET["id"]) {
                                                                            $MenuSira = $MenuVeri["menu_sira"];
                                                                        } else {
                                                                            $MenuSira = $db->query("SELECT count(*) FROM $tablo")->fetchColumn() + 1;
                                                                        } ?>
                                                                        <input type="number" class="form-control col-md-12 col-xs-12" value="<?php echo $MenuSira; ?>" name="menu_sira">
                                                                    </div>
                                                                </div>

                                                                <div class="tab-menu-heading">
                                                                    <div class="tabs-menu1">
                                                                        <ul class="nav panel-tabs">

                                                                            <?php
                                                                            $dil = $db->prepare("SELECT * FROM dil WHERE aktif=1 ORDER BY id");
                                                                            $dil->execute();
                                                                            foreach ($dil as $dilcek) { ?>
                                                                                <li>
                                                                                    <a href="#<?php echo $dilcek['kisaltma']; ?>" class="<?= ($dilcek['id'] == 1) ? 'active' : '' ?>" data-bs-toggle="tab">
                                                                                        <i class="flag flag-<?php echo $dilcek['kisaltma']; ?>"></i>
                                                                                        <?php echo $dilcek['dil']; ?>
                                                                                    </a>
                                                                                </li>
                                                                            <?php } ?>
                                                                        </ul>
                                                                    </div>
                                                                </div>




                                                                <div class="panel-body tabs-menu-body">
                                                                    <div class="tab-content">
                                                                        <?php
                                                                        $dil = $db->prepare("select * from dil where aktif='1'");
                                                                        $dil->execute();
                                                                        foreach ($dil as $dilcek) {
                                                                            $dil = $dilcek['kisaltma']; ?>

                                                                            <div class="tab-pane <?= ($dilcek['id'] == 1) ? 'active' : '' ?>" id="<?php echo $dilcek['kisaltma']; ?>">

                                                                                <?php if ($dilcek['kisaltma'] != "tr") { ?>
                                                                                    <div class="btn btn-success ms-3 mt-4 mb-5 float-none" onclick="cevir('<?php echo $dilcek['kisaltma']; ?>')">
                                                                                        <i class="fe fe-check me-2" style="color:#fff !important"></i>
                                                                                        <?php echo $dilcek['dil']; ?> diline otomatik çevir
                                                                                    </div>
                                                                                <?php } ?>



                                                                                <?php if (AktifKontrol("baslik")) { ?>
                                                                                    <div class="form-group">
                                                                                        <label class="control-label col-md-12 col-sm-12 col-xs-12" for="first-name">Başlık</label>
                                                                                        <div class="col-md-12 col-sm-12 col-xs-12">
                                                                                            <input type="text" class="form-control baslik" dil="<?php echo $dilcek["dil"] ?>" name="baslik_<?php echo $dilcek['kisaltma']; ?>" value="<?php echo $MenuVeri["baslik_$dil"]; ?>">
                                                                                        </div>
                                                                                    </div>
                                                                                <?php } ?>

                                                                                <?php if (AktifKontrol("baslik1")) { ?>
                                                                                    <div class="form-group">
                                                                                        <label class="control-label col-md-12 col-sm-12 col-xs-12" for="first-name">Alt Başlık </label>
                                                                                        <div class="col-md-12 col-sm-12 col-xs-12">
                                                                                            <input type="text" class="form-control" name="baslik1_<?php echo $dilcek['kisaltma']; ?>" value="<?php echo $MenuVeri["baslik1_$dil"]; ?>">
                                                                                        </div>
                                                                                    </div>
                                                                                <?php } ?>

                                                                                <?php if (AktifKontrol("baslik2")) { ?>
                                                                                    <div class="form-group">
                                                                                        <label class="control-label col-md-12 col-sm-12 col-xs-12" for="first-name">Alt Başlık 2 </label>
                                                                                        <div class="col-md-12 col-sm-12 col-xs-12">
                                                                                            <input type="text" class="form-control" name="baslik2_<?php echo $dilcek['kisaltma']; ?>" value="<?php echo $MenuVeri["baslik2_$dil"]; ?>">
                                                                                        </div>
                                                                                    </div>
                                                                                <?php } ?>

                                                                                <?php if (AktifKontrol("icerik")) { ?>
                                                                                    <div class="form-group">
                                                                                        <label class="control-label col-md-12 col-sm-12 col-xs-12" for="first-name">İçerik </label>
                                                                                        <div class="col-md-12 col-sm-12 col-xs-12">
                                                                                            <textarea class="icerikalan<?php echo $dilcek['kisaltma']; ?>" id="icerik_<?php echo $dilcek['kisaltma']; ?>" name="icerik_<?php echo $dilcek['kisaltma']; ?>"><?php echo $MenuVeri["icerik_$dil"]; ?></textarea>
                                                                                        </div>
                                                                                    </div>
                                                                                <?php } ?>

                                                                                <?php if (AktifKontrol("icerik1")) { ?>
                                                                                    <div class="form-group">
                                                                                        <label class="control-label col-md-12 col-sm-12 col-xs-12" for="first-name">İçerik 2 </label>
                                                                                        <div class="col-md-12 col-sm-12 col-xs-12">
                                                                                            <textarea id="icerik1_<?php echo $dilcek['kisaltma']; ?>" name="icerik1_<?php echo $dilcek['kisaltma']; ?>"><?php echo $MenuVeri["icerik1_$dil"]; ?></textarea>
                                                                                        </div>
                                                                                    </div>
                                                                                <?php } ?>

                                                                                <?php if (AktifKontrol("icerik2")) { ?>
                                                                                    <div class="form-group">
                                                                                        <label class="control-label col-md-12 col-sm-12 col-xs-12" for="first-name">İçerik 3</label>
                                                                                        <div class="col-md-12 col-sm-12 col-xs-12">
                                                                                            <textarea id="icerik2_<?php echo $dilcek['kisaltma']; ?>" name="icerik2_<?php echo $dilcek['kisaltma']; ?>"><?php echo $MenuVeri["icerik2_$dil"]; ?></textarea>
                                                                                        </div>
                                                                                    </div>
                                                                                <?php } ?>



                                                                                <?php if (AktifKontrol("link")) { ?>
                                                                                    <div class="form-group">
                                                                                        <label class="control-label col-md-12 col-sm-12 col-xs-12" for="first-name">Link </label>
                                                                                        <div class="col-md-12 col-sm-12 col-xs-12">
                                                                                            <input type="text" class="form-control" name="link_<?php echo $dilcek['kisaltma']; ?>" value="<?php echo $MenuVeri["link_$dil"]; ?>">
                                                                                        </div>
                                                                                    </div>
                                                                                <?php } ?>


                                                                                <div class="row">
                                                                                    <?php if (AktifKontrol("title")) { ?>
                                                                                        <div class="form-group col-md-6 col-sm-12 col-xs-12">
                                                                                            <label class="control-label col-md-12 col-sm-12 col-xs-12" for="first-name">Özel Title </label>
                                                                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                                                                <input type="text" class="form-control" name="title_<?php echo $dilcek['kisaltma']; ?>" value="<?php echo $MenuVeri["title_$dil"]; ?>">
                                                                                            </div>
                                                                                        </div>
                                                                                    <?php } ?>


                                                                                    <?php if (AktifKontrol("description")) { ?>
                                                                                        <div class="form-group col-md-6 col-sm-12 col-xs-12">
                                                                                            <label class="control-label col-md-12 col-sm-12 col-xs-12" for="first-name">Özel Description </label>
                                                                                            <div class="col-md-12 col-sm-12 col-xs-12">
                                                                                                <input type="text" class="form-control" name="description_<?php echo $dilcek['kisaltma']; ?>" value="<?php echo $MenuVeri["description_$dil"]; ?>">
                                                                                            </div>
                                                                                        </div>
                                                                                    <?php } ?>
                                                                                </div>



                                                                            </div>

                                                                        <?php } ?>


                                                                        <div class="col-12">

                                                                            <div class="row">
                                                                                <?php if(AktifKontrol("resim1")) { ?>
                                                                                <div class="col-6">
                                                                                    <div class="form-group">
                                                                                        <label class="control-label col-12" for="logo">Resim Seç</label>
                                                                                        <div class="col-12">
                                                                                            <input type="file" class="form-control col-12" name="resim" id="resim">
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="col-6">
                                                                                    <div style="margin-left:10px">
                                                                                        <?php
                                                                                        if (strlen($MenuVeri["resim"]) > 0) { ?>
                                                                                            <div id="thumbwrap">
                                                                                                <a class="thumb">
                                                                                                    <img style="width: 80px; height: 80px;" src="../<?php echo $MenuVeri["resim"] ?>">
                                                                                                    <span><img src="../<?php echo $MenuVeri["resim"] ?>"></span>
                                                                                                </a>
                                                                                            </div>
                                                                                            <label class="custom-control custom-checkbox-md mt-2 colorinput">
                                                                                                <input type="checkbox" class="custom-control-input" name="resimsil">
                                                                                                <input type="hidden" name="resimlink" value="<?php echo $MenuVeri["resim"] ?>">
                                                                                                <span class="custom-control-label">Resmi Sil</span>
                                                                                            </label>
                                                                                        <?php } else { ?>
                                                                                            <div class="mt-5">
                                                                                                <img style="width: 40px; height: 40px;" src="../images/logo-yok.webp">

                                                                                                Yüklü Resim Yok
                                                                                            </div>
                                                                                        <?php }
                                                                                        ?>
                                                                                    </div>
                                                                                </div>
                                                                                <?php } ?>

                                                                                <?php if(AktifKontrol("dosya")) { ?>
                                                                                <div class="col-6">
                                                                                    <div class="form-group">
                                                                                        <label class="control-label col-12" for="logo">Dosya Yükle</label>
                                                                                        <div class="col-12">
                                                                                            <input type="file" class="form-control col-12" name="dosya" id="dosya">
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="col-6 mt-5">
                                                                                    <div style="margin-left:10px">
                                                                                        <?php
                                                                                        if (strlen($MenuVeri["dosya"]) > 0) { ?>
                                                                                            <div><a download href="/<?php echo $MenuVeri["dosya"] ?>" target="_blank">Dosyayı Görmek İçin Tıkla</a></div>
                                                                                            <label class="custom-control custom-checkbox-md mt-2 colorinput">
                                                                                                <input type="checkbox" class="custom-control-input" name="dosyasil">
                                                                                                <input type="hidden" name="dosyalink" value="<?php echo $MenuVeri["dosya"] ?>">
                                                                                                <span class="custom-control-label">Ekli Dosyayı Sil</span>
                                                                                            </label>
                                                                                        <?php } else { ?>
                                                                                            <div class="">
                                                                                                <img style="width: 40px; height: 40px;" src="../images/logo-yok.webp">
                                                                                                Ekli dosya Yok
                                                                                            </div>
                                                                                        <?php }
                                                                                        ?>
                                                                                    </div>
                                                                                </div>
                                                                                <?php } ?>
                                                                            </div>

                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <hr>
                                                        <div class="col-12">
                                                            <button name="<?= isset($_GET['id']) ? " duzenle" : "ekle"; ?>" type="submit" class="btn btn-dark btn-lg"><i class="fa fa-floppy-o me-2" style="color:#fff !important"></i> Kaydet</button>
                                                        </div>
                                                    </div>
                                                </form>
                                            </div>

                                            <?php if (AktifKontrol("resimler")) { ?>
                                                <div class="tab-pane" id="resimler">
                                                    <div class="col-lg-12">
                                                        <div class="card">
                                                            <div class="float-end" style="float:right !important"><button class="btn btn-green" data-bs-effect="effect-scale" data-bs-toggle="modal" onclick="javascript:ModalID(<?php echo $_GET['id']; ?>)" href="#resimekle"><i class="fe fe-plus me-2" style="color:#fff !important"></i>Resim Ekle</i></div>
                                                            <div class="card-header">
                                                                <h3 class="card-title">Resimler</h3>
                                                            </div>
                                                            <div>
                                                                <label>
                                                                    <input id="SilindifilitreCheck" type="checkbox" onchange="javascript:SilinenResimler(this)">Gizlenen Resimleri Göster
                                                                </label>
                                                            </div>
                                                            <div class="card-body">
                                                                <div class="row">

                                                                    <?php
                                                                    $resimsor = $db->prepare("SELECT * FROM resimler WHERE menu_id=:id AND sayfatip=:sayfatip");
                                                                    $resimsor->execute(array("id" => $_GET['id'], "sayfatip" => $sayfatip));
                                                                    $sayac = 1;
                                                                    foreach ($resimsor as $resimcek) {
                                                                    ?>

                                                                        <div id="RowResim<?php echo $resimcek['id']; ?>" silindi="<?= ($resimcek['sil'] == true) ? "true" : "false"; ?>" style="<?= ($resimcek['sil'] == true) ? "display:none" : "" ?>" class="col-md-6 col-xl-2 text-center mt-5 Rows" id="RowResim<?php echo $resimcek['id']; ?>">
                                                                            <form onsubmit="javascript:return TextKaydet(this)">

                                                                                <img src="../<?php echo $resimcek['link']; ?>" style="object-fit:contain; width:100%; height:200px; margin-bottom:30px">

                                                                                <div class="mb-2" link="https://<?php echo $_SERVER['SERVER_NAME'] . "/" . $resimcek['link'] ?>" onclick="copyLink(this)">
                                                                                    <span class="input-group-text text-black"><i class="fa fa-copy me-2"></i> Linki Kopyalamak İçin Tıkla</span>
                                                                                </div>


                                                                                <?php
                                                                                $dilsor = $db->prepare("SELECT * FROM dil WHERE aktif=:id ORDER BY id ASC");
                                                                                $dilsor->execute(array("id" => 1));
                                                                                foreach ($dilsor as $dilcek) {
                                                                                    $dil = $dilcek['kisaltma'];
                                                                                ?>
                                                                                    <div class="input-group">
                                                                                        <span class="input-group-text" id="basic-addon1"><i class="flag flag-<?php echo $dil; ?>"></i></span>
                                                                                        <input type="text" class="form-control" name="<?php echo "resimbaslik_" . $dilcek['kisaltma']; ?>" id="<?php echo "baslik_" . $dilcek['kisaltma']; ?><?php echo $resimcek['id']; ?>" value="<?php echo $resimcek['baslik_' . $dil . '']; ?>" aria-describedby="basic-addon1">
                                                                                    </div>
                                                                                <?php $sayac++;
                                                                                } ?>
                                                                                <input type="hidden" class="form-control" name="id" value="<?php echo $resimcek['id']; ?>">
                                                                                <button style="<?= $resimcek['sil'] ? "display:none" : "" ?>" type="submit" id="gonder" name="resimler" class="btn btn-dark mt-2 me-2 mb-0 col-12 notice1 mt-2 mb-2 KaydetBtn"><i class="fe fe-check me-2" style="color:#fff !important"></i>Kaydet</button>
                                                                            </form>

                                                                            <button onClick="ResimDilCevir('<?php echo $resimcek['id']; ?>','<?php echo $dilcek['kisaltma']; ?>');" class="btn btn-success w-100 mb-4 float-none "><i class="fe fe-check me-2" style="color:#fff !important"></i>Otomatik Çevir</button>
                                                                            <div class="row ps-2 pe-2">
                                                                                <button style="<?= $resimcek['sil'] ? "display:none" : "" ?>" id="" onclick="javascript:KayitSilConfirm(<?php echo $resimcek['id']; ?>)" name="sil" class="btn btn-indigo col notice1 me-1 SilBtn"><i class="fe fe-check me-2 " style="color:#fff !important"></i>Gizle</button>
                                                                                <button onclick="javascript:ResimKaliciSilConfirm(<?php echo $resimcek['id']; ?>)" name="kalicisil" class="btn btn-danger col notice1 ms-1 KaliciSilBtn"><i class="fe fe-check me-2 " style="color:#fff !important"></i>Sil</button>
                                                                            </div>
                                                                            <button style="<?= $resimcek['sil'] != true ? "display:none" : "" ?>" id="" onclick="javascript:GeriAlConfirm(<?php echo $resimcek['id']; ?>)" name="sil" class="btn btn-warning mt-4 me-2 mb-0 col-12 notice1 mb-5 GeriAlBtn"><i class="fe fe-check me-2" style="color:#fff !important"></i>Geri Al</button>
                                                                        </div>

                                                                    <?php }
                                                                    ?>

                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php } ?>

                                            <?php if (AktifKontrol("dokuman")) { ?>
                                                <div class="tab-pane" id="dokuman">
                                                    <div class="col-lg-12">
                                                        <div class="card">
                                                            <div class="card-header">
                                                                <h3 class="card-title">Dökümanlar</h3>
                                                            </div>
                                                            <div>
                                                                <label>
                                                                    <input id="DosyaSilindifilitreCheck" type="checkbox" onchange="javascript:SilinenDosyalar(this)">Gizlenen Dökümanlar
                                                                </label>
                                                            </div>
                                                            <div class="card-body">
                                                                <div class="row">
                                                                    <?php
                                                                    if ($_GET['id']) {
                                                                    ?>
                                                                        <?php
                                                                        $dosyasor = $db->prepare("SELECT * FROM dokuman WHERE menu_id=" . $_GET['id'] . " AND sayfatip =:sayfatip");
                                                                        $dosyasor->execute(array("sayfatip" => $sayfatip));
                                                                        foreach ($dosyasor as $dosyacek) {
                                                                        ?>

                                                                            <div silindi="<?= $dosyacek['sil'] ? "true" : "false" ?>" style="<?php if ($dosyacek['sil'] == true) {
                                                                                                                                                    echo "display:none";
                                                                                                                                                } ?>" class="col-md-6 col-xl-2 text-center mt-5 Rows" id="RowDosya<?php echo $dosyacek['id']; ?>">
                                                                                <form onsubmit="javascript:return DosyaTextKaydet(this)">
                                                                                    <img src="../images/images/<?php $dosyaadi = $dosyacek['link'];
                                                                                                                if (strstr($dosyaadi, "xlsx")) {
                                                                                                                    echo "excel.png";
                                                                                                                } elseif (strstr($dosyaadi, "docx")) {
                                                                                                                    echo "word.png";
                                                                                                                } elseif (strstr($dosyaadi, "pdf")) {
                                                                                                                    echo "pdf.png";
                                                                                                                } elseif (strstr($dosyaadi, "rar") or strstr($dosyaadi, "zip")) {
                                                                                                                    echo "rar.png";
                                                                                                                } else {
                                                                                                                    echo "dosya.png";
                                                                                                                }  ?>" style="object-fit: contain; width: 50%; height:100px; padding-bottom:30px; cursor: default;">
                                                                                    <input type="text" class="form-control" name="baslik" value="<?php echo $dosyacek['baslik']; ?>">
                                                                                    <div class="form-control"><?php if ($dosyacek['boyut']){?> <?php echo round($dosyacek['boyut'] / 1024 / 1024); ?> MB <?php }?></div>
                                                                                    <input type="hidden" class="form-control" name="id" value="<?php echo $dosyacek['id']; ?>">
                                                                                    <button style="<?= $dosyacek['sil'] ? "display:none" : "" ?>" type="submit" id="gonder" name="resimler" class="btn btn-dark mt-4 me-2 mb-0 col-12 notice1 mb-5 KaydetBtn"><i class="fe fe-check me-2" style="color:#fff !important"></i>Kaydet</button>
                                                                                </form>
                                                                                <div class="row ps-2 pe-2">
                                                                                    <button style="<?= $dosyacek['sil'] ? "display:none" : "" ?>" id="" onclick="javascript:DosyaSilConfirm(<?php echo $dosyacek['id']; ?>)" name="sil" class="btn btn-indigo col notice1 ms-1 SilBtn"><i class="fe fe-check me-2 " style="color:#fff !important"></i>Gizle</button>

                                                                                    <button style="<?= $dosyacek['sil'] ? "display:none" : "" ?>" id="" onclick="javascript:DosyaKaliciSilConfirm(<?php echo $dosyacek['id']; ?>)" name="sil" class="btn btn-danger col notice1 ms-1 SilBtn"><i class="fe fe-check me-2 " style="color:#fff !important"></i>Sil</button>
                                                                                </div>
                                                                                <button style="<?= !$dosyacek['sil'] ? "display:none" : "" ?>" id="" onclick="javascript:DosyaGeriAlConfirm(<?php echo $dosyacek['id']; ?>)" name="sil" class="btn btn-warning mt-4 me-2 mb-0 col-12 notice1 mb-5 GeriAlBtn"><i class="fe fe-check me-2" style="color:#fff !important"></i>Geri Al</button>
                                                                            </div>

                                                                        <?php }
                                                                        ?>
                                                                    <?php
                                                                    }
                                                                    ?>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php } ?>

                                            <?php if (AktifKontrol("videolar")) { ?>
                                                <div class="tab-pane" id="videolar">
                                                    <div class="col-lg-12">
                                                        <div class="card">
                                                            <div class="float-end" style="float:right !important"><button class="btn btn-green" data-bs-effect="effect-scale" data-bs-toggle="modal" onclick="javascript:ModalID_video(<?php echo $_GET['id']; ?>)" href="#videoekle"><i class="fe fe-plus me-2" style="color:#fff !important"></i>Video Ekle</i></div>
                                                            <div class="card-header">
                                                                <h3 class="card-title">Videolar</h3>
                                                            </div>
                                                            <div class="card-body">
                                                                <div class="row">
                                                                    <?php
                                                                    $videosor = $db->prepare("SELECT * FROM videolar WHERE menu_id=:id AND sayfatip =:sayfatip");
                                                                    $videosor->execute(array("id" => $MenuVeri['id'], "sayfatip" => $sayfatip));
                                                                    foreach ($videosor as $videocek) {
                                                                        if ($videocek["videoresim"]) {
                                                                            $videoresim = $videocek["videoresim"];
                                                                        } else {
                                                                            $videoresim = "../images/images/video.png";
                                                                        }

                                                                        if ($videocek["link"]) {
                                                                            $videolink = "https://" . $_SERVER['SERVER_NAME'] . "/" . $videocek["link"];
                                                                        } else {
                                                                            $videolink = $videocek["youtube"];
                                                                        }

                                                                        if ($videocek['boyut']) {
                                                                            $videoboyut = dosyaboyutu($videocek['boyut']);
                                                                        } else {
                                                                            $videoboyut = "Youtube Videosu";
                                                                        }

                                                                    ?>
                                                                        <div class="col-md-6 col-xl-2 text-center mt-5 Rows" id="RowDosya<?php echo $videocek['id']; ?>">
                                                                            <form onsubmit="javascript:return VideoTextKaydet(this)">
                                                                                <?php if ($videocek["youtube"]) { ?>
                                                                                    <img src="<?php echo $videoresim ?>" style="object-fit: cover; width: 100%; height: 250px; cursor: default;">
                                                                                <?php } else { ?>
                                                                                    <video width="100%" height="245" controls>
                                                                                        <source src="<?php echo $videolink ?>" type="video/mp4">
                                                                                    </video>
                                                                                <?php } ?>
                                                                                <input type="text" class="form-control mt-3" name="baslik" value="<?php echo $videocek['baslik']; ?>">
                                                                                <input type="hidden" class="form-control" name="id" value="<?php echo $videocek['id']; ?>">
                                                                                <button style="<?= $videocek['sil'] ? "display:none;" : "" ?>" type="submit" id="gonder" name="resimler" class="btn btn-dark mt-4 me-2 mb-2 col-12 notice1 KaydetBtn">
                                                                                    <i class="fe fe-check me-2" style="color:#fff !important"></i>Kaydet
                                                                                </button>
                                                                            </form>
                                                                            <div class="mb-2">
                                                                                <span class="input-group-text text-black">
                                                                                    <i class="fa fa-copy me-2"></i>
                                                                                    <?php echo $videoboyut ?>
                                                                                </span>
                                                                            </div>
                                                                            <div class="mb-2" link="<?php echo $videolink ?>" onclick="copyLink(this)">
                                                                                <span class="input-group-text text-black">
                                                                                    <i class="fa fa-copy me-2"></i> Linki Kopyalamak İçin Tıkla
                                                                                </span>
                                                                            </div>
                                                                            <div class="row ps-2 pe-2">
                                                                                <button id="" onclick="javascript:VideoKaliciSilConfirm(<?php echo $videocek['id']; ?>)" name="sil" class="btn btn-danger col notice1 ms-1 SilBtn">
                                                                                    <i class="fe fe-check me-2 " style="color:#fff !important"></i>Sil
                                                                                </button>

                                                                            </div>
                                                                        </div>
                                                                    <?php } ?>
                                                                </div>
                                                            </div>

                                                        </div>
                                                    </div>
                                                </div>
                                            <?php } ?>

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal fade" id="resimekle">
                    <div class="modal-dialog modal-dialog-centered" role="document">
                        <div class="modal-content modal-content-demo">
                            <div class="modal-header">
                                <h6 class="modal-title">Resim Yükle</h6><button aria-label="Close" class="btn-close" data-bs-dismiss="modal"><span aria-hidden="true">&times;</span></button>
                            </div>
                            <div class="modal-body">
                                <form class="form-horizontal" action="resim.php" method="post" enctype="multipart/form-data">
                                    <div class="input">
                                        <input class="form-control" type="file" name="resim[]" multiple="multiple" />
                                        <input type="hidden" name="menu_id" id="menu_id" value="" />
                                        <input type="hidden" name="url" value="<?php echo $url; ?>">
                                        <input type="hidden" name="st" value="<?php echo $sayfatip; ?>">
                                        <div class="form-check mt-2 mb-2">
                                        </div>
                                        <button class="btn btn-dark">Yükle</button>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button class="btn btn-light" data-bs-dismiss="modal">Kapat</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal fade" id="dosyaekle">
                    <div class="modal-dialog modal-dialog-centered text-center" role="document">
                        <div class="modal-content modal-content-demo">
                            <div class="modal-header">
                                <h6 class="modal-title">Döküman Yükle</h6><button aria-label="Close" class="btn-close" data-bs-dismiss="modal"><span aria-hidden="true">&times;</span></button>
                            </div>
                            <div class="modal-body">
                                <form name="from_file_upload" action="dosya.php" method="post" enctype="multipart/form-data" class="frm-upload">
                                    <div class="input-group">
                                        <input class="form-control" type="file" name="dosya[]" multiple="multiple">
                                        <input type="hidden" name="menu_id_dosya" id="menu_id_dosya" />
                                        <input type="hidden" name="url" value="<?php echo $url; ?>">
                                        <input type="hidden" name="st" value="<?php echo $sayfatip; ?>">
                                        <button name="upload" class="btn btn-dark">Yükle</button>
                                    </div>
                            </div>
                            </form>
                            <div class="modal-footer">
                                <button class="btn btn-light" data-bs-dismiss="modal">Kapat</button>
                            </div>
                        </div>
                    </div>
                </div>


                <div class="modal fade" id="videoekle">
                    <div class="modal-dialog modal-dialog-centered" role="document">
                        <div class="modal-content modal-content-demo">
                            <div class="modal-header">
                                <h6 class="modal-title">Video Yükle</h6>
                                <button aria-label="Close" class="btn-close" data-bs-dismiss="modal">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <form name="from_file_upload" action="video.php" method="post" enctype="multipart/form-data" class="frm-upload">
                                    <div class="input">
                                        <input class="form-control" type="file" name="dosya[]" multiple="multiple" accept="video/*" />
                                        <input type="text" class="form-control mt-2" name="youtube" placeholder="YouTube Linki (Opsiyonel)">
                                        <input type="hidden" name="menu_id_video" id="menu_id_video" />
                                        <input type="hidden" name="url" value="<?php echo $url; ?>">
                                        <input type="hidden" name="st" value="<?php echo $sayfatip; ?>">
                                        <button name="upload" class="btn btn-dark mt-2">Yükle</button>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button class="btn btn-light" data-bs-dismiss="modal">Kapat</button>
                            </div>
                        </div>
                    </div>
                </div>


            </div>
        </div>


<?php } else {
        require 'yetkisizislem.php';
    }
    require 'footer.php';
} ?>