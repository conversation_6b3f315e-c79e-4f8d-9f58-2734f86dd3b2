function IlceListCascade(actionUrl, loadSelectId, data, onsuccesscallBack, onclickFuncName) {
    var returnLoadDivId = $("#" + loadSelectId);
    var prefix = "javascript:";

    $.ajax({
        async: true,
        url: actionUrl,
        type: 'GET',
        data: data,
        beforeSend: function () {

        },
        success: function (response) {
            if (response.hasOwnProperty('success') && response.success == false) {
            }
            else {
                var html = "";
                returnLoadDivId.html('');
                $.each(response, function (id, option) {
                    html += "<li><a  href=" + prefix + onclickFuncName + "('" + option.value + '\',\'' + option.text + "')" + ">" +
                        option.text +
                        "</a></li>";
                });
                returnLoadDivId.html(html);
                onsuccesscallBack();
            }
        },
        error: function (response) {
        },
        complete: function (response) {
        }
    });
}






function MakeAjaxCall(controllerName, action, data, type, isASync, onSuccessCallback, onErrorCallback, onBeginCallback, onCompleteCallback) {

    onSuccessCallback = typeof onSuccessCallback !== 'undefined' ? onSuccessCallback : null;
    onErrorCallback = typeof onErrorCallback !== 'undefined' ? onErrorCallback : null;
    onBeginCallback = typeof onBeginCallback !== 'undefined' ? onBeginCallback : null;
    onCompleteCallback = typeof onCompleteCallback !== 'undefined' ? onCompleteCallback : null;

    var url = '/' + controllerName + '/' + action;
    $.ajax({
        url: url,
        type: type,
        async: isASync,
        data: data,
        beforeSend: function () {
            //LoadingShowContainer();
            if (onBeginCallback !== null) {
                onBeginCallback();
            }
        },
        success: function (response) {
            if (onSuccessCallback !== null) {
                onSuccessCallback(response);
            }
        },
        error: function () {
            if (onErrorCallback !== null) {
                onErrorCallback(data);
            } else {
                //ShowUnknownErrorMessage();
            }

        },
        complete: function () {
            if (onCompleteCallback !== null) {
                onCompleteCallback();
            }
            //LoadingHideContainer();
        }

    });
}

function loadingGif() {

    $('.loading-bg').show();
    $('body').addClass('body-loading');
}
function removeloadingGif() {
    $('.loading-bg').hide();
    $('body').removeClass('body-loading');
}

function formOnlineIslemBaseOnBegin() {
    loadingGif();
}
function formOnlineIslemBaseFailure(err) {
    console.log(err);
}
function formOnlineIslemBaseComplete() {
    removeloadingGif();
}

function ShowMessage(error, success) {

    if (error != '' && error != undefined) {
        $(".errorFormMessage").html(error);
        $(".errorFormMessage").show();
        $(".successFormMessage").hide();
    }
    if (success != '' && success != undefined) {
        $(".successFormMessage").html(success);
        $(".successFormMessage").show();
        $(".errorFormMessage").hide();
    }
}

function setWithExpiry(key, value, ttl = 3600000) {
    const now = new Date()

    var item = {
        value: value,
        expiry: now.getTime() + ttl,
        fId: key
    }

    var formKontrolValueList = JSON.parse(localStorage.getItem('formKontrolValueList') || "[]");

    if (formKontrolValueList == null) {
        formKontrolValueList = [item];
    } else {
        let formKontrolItem = formKontrolValueList.find(x => x.fId === key);
        if (formKontrolItem == null) {
            formKontrolValueList.push(item);
        } else {
            var foundIndex = formKontrolValueList.findIndex(x => x.fId == key);
            formKontrolValueList[foundIndex] = item;
        }
    }
    localStorage.setItem('formKontrolValueList', JSON.stringify(formKontrolValueList));
}
function getWithExpiry(key) {

    var formKontrolValueList = JSON.parse(localStorage.getItem('formKontrolValueList'));

    var item = null;
    if (formKontrolValueList != null)
        item = formKontrolValueList.find(x => x.fId === key);

    if (!item) {
        return 0;
    }
    const now = new Date()
    // compare the expiry time of the item with the current time
    if (now.getTime() > item.expiry) {
        formKontrolValueList = $.grep(formKontrolValueList, function (e) {
            return e.fId != key;
        });
        
        localStorage.setItem('formKontrolValueList', JSON.stringify(formKontrolValueList));
        return 0;
    }
    return item.value;
}

function formSiteFailure(err) {
    console.log(err);
}

function CaptchaShowHide(formId) {
    $("." + formId).show();
}
function CaptchaValueCheck() {
    var res = $(".g-recaptcha").css("display");
    if (res !== "none") {
        if ($("#g-recaptcha-response").val() === undefined || $("#g-recaptcha-response").val() === "")
            return false;
        return true;
    }
    else {
        return true;
    }
}
function SetCaptchaValue(formId) {
    return;
}

function AddValidationError(elementId, message) {
    var item = $("[data-valmsg-for=" + elementId + "]");
    item.removeClass("field-validation-valid");
    item.addClass("field-validation-error");
    item.text(message);
}
function RemoveValidationError(elementId) {
    var item = $("[data-valmsg-for=" + elementId + "]");

    if (item.hasClass("field-validation-error")) {
        item.removeClass("field-validation-error");
        item.addClass("field-validation-valid");
        item.text("");
    }
}

function RemoveAllValidationError() {
    $(".field-validation-error").each(function (index, item) {

        if ($(this).hasClass("field-validation-error")) {
            $(this).removeClass("field-validation-error");
            $(this).addClass("field-validation-valid");
            $(this).text("");
        }

    });
}
function ClientSideFluentValidation(response, elementId, submitButtonId, allFormValidate) {

    allFormValidate = typeof allFormValidate !== 'undefined' ? allFormValidate : false;
    var clientSideError = false;
    if (response.state == 1) {
        RemoveValidationError(elementId);
        $("#" + submitButtonId).removeClass("disabled");
    }
    else if (response.state == 2) {

        var elementValid = true;
        $.each(response.data, function (index, element) {
            if (allFormValidate) {
                AddValidationError(element.fieldName, element.message);
                clientSideError = true;
            } else {
                if (elementId == element.fieldName) {
                    elementValid = false;
                    AddValidationError(element.fieldName, element.message);
                    clientSideError = true;

                }
            }

            
        });
        if (elementValid && !allFormValidate) {
            RemoveValidationError(elementId);
        }
    }
    else {
        ShowMessage(response.message);
    }
    return clientSideError;
}