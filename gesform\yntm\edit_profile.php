<?php 
session_start();
include('fonksiyon.php');
if ($_SESSION['user_id'] == $_GET['id'])
{	


$sql		= "SELECT * FROM ".TABLO_USER." WHERE id='".$_GET['id']."'";
$result		= mysqli_query($con, $sql);
$row		= mysqli_fetch_array($result);
	
?>
<link rel="stylesheet" href="css/style.css" type="text/css" />
<center>
<form name="yeni" action="javascript:void(0);" method="post">
<b>
<table class="table_radius_border" border="0" style="margin:0px;font-size:12px;width:320px;">
<tr class="popup_5">
 <td>
	<table class="table_radius_border" border="0" style="margin:0px;font-size:12px;width:98%;">
	<tr class="popup_1">
		<td class="popup_2" >ID :</td>
		<td class="popup_6">
		<input value="<?=$row['id']?>" class="input_text_profil" type="text" disabled>
		<input value="<?=$row['id']?>" type="hidden" name="id">
		</td>
	</tr>
	<tr class="popup_1">
		<td class="popup_2" >Kullanıcı Adı :</td>
		<td class="popup_6">
			<input value="<?=$row['kullanici_adi']?>" class="input_text_profil" type="text" disabled>
			<input value="<?=$row['kullanici_adi']?>" class="input_text_profil" type="hidden" name="kullanici_adi">
		</td>
	</tr>
	<tr class="popup_1">
		<td class="popup_2">Şifre :</td>
		<td class="popup_6"><input value="" placeholder="Yeni Şifrenizi Girin" class="input_text_profil" type="password" name="sifre"></td>
	</tr>
	<tr class="popup_1">
		<td class="popup_2">İsim :</td>
		<td class="popup_6"><input value="<?=$row['isim']?>" class="input_text_profil" type="text" name="isim"></td>
	</tr>
	<tr class="popup_1">
		<td class="popup_2">Soy İsim :</td>
		<td class="popup_6"><input value="<?=$row['soyisim']?>" class="input_text_profil" type="text" name="soyisim"></td>
	</tr>
	</table>
 </td>
</tr>
<tr>
 <td align="right">
	<input type="hidden" name="pass_id" value="<?=$row['id']?>">
	<input class="input_btn" type="submit" value="Düzenle" onclick="duzenle_profil();">&nbsp;
 </td>
</tr>
</table>
</b>
<div id="profil_duzenleniyor"></div>
</form>
</center>

<?php
}else{ header("Location: index.php"); }
?>