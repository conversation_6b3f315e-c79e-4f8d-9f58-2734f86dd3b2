function ModalID(id) {
  $("#menu_id").val(id);
}
function ModalID_dosya(id) {
  $("#menu_id_dosya").val(id);
}

function ModalID_video(id) {
  $("#menu_id_video").val(id);
}
function KutuEkle(id) {
  $("#kutuuid").val(id);
}

function KullaniciDuzenle(isim, kadi, id) {
  $("#duzenle_isim").val(isim);
  $("#duzenle_kullanici_adi").val(kadi);
  $("#duzenle_kullanici_id").val(id);
}

function BildirimAdresi(isim,id) {
  $("#duzenle_bildirimmail").val(isim);
  $("#duzenle_bildirimid").val(id);
}


function FormDuzenle(isim,id) {
  $("#duzenle_formadi").val(isim);
  $("#duzenle_id").val(id);
}

function <PERSON><PERSON>n<PERSON><PERSON>mler(obj) {
  $(".Rows").css("display", "none");
  if ($(obj).prop("checked") === true) {
    $(".Rows").each(function () {
      if ($(this).attr("silindi") === "true") $(this).css("display", "block");
      else $(this).css("display", "none");
    });
  } else {
    $(".Rows").each(function () {
      if ($(this).attr("silindi") === "false") $(this).css("display", "block");
      else $(this).css("display", "none");
    });
  }
}

function KayitGuncelle(islem) {
  $.ajax({
    url: "resim.php",
    type: "get",
    data: { id: _id, islem: islem },
    success: function (data,status) {
      if (status === "success") {
        //$('#RowResim' + _id).remove();
        if (islem === 1) $("#RowResim" + _id).attr("silindi", true);
        else $("#RowResim" + _id).attr("silindi", false);

        $("#SilindifilitreCheck").trigger("click");

        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Resim Silindi",
        });
      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Hata Oluştu",
        });
      }
    },
    error: function (error) {
      //console.log("Error:");
      //console.log(error);
    },
  });
}

function urunKayitGuncelle(islem) {
  $.ajax({
    url: "db/ResimSil.php",
    type: "get",
    data: { id: _id, islem: islem },
    success: function (data,status) {
      if (status === "success") {
        //$('#RowResim' + _id).remove();
        if (islem === 1) $("#RowResim" + _id).attr("silindi", true);
        else $("#RowResim" + _id).attr("silindi", false);

        $("#SilindifilitreCheck").trigger("click");

        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Resim Silindi",
        });
      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Hata Oluştu",
        });
      }
    },
    error: function (error) {
      //console.log("Error:");
      //console.log(error);
    },
  });
}


$(document).ready(function () {
  $("#SilindifilitreCheck").on("change", function () {
    if ($(this).prop("checked") === true) {
      $(".KaydetBtn").css("display", "none");
      $(".SilBtn").css("display", "none");
      $(".GeriAlBtn").css("display", "");
    } else {
      $(".KaydetBtn").css("display", "");
      $(".SilBtn").css("display", "");
      $(".GeriAlBtn").css("display", "none");
    }
  });
});
var _id = 0;
function KayitSilConfirm(id) {
  _id = id;
  Swal.fire({
    html: "Resmi Gizlemek İstediğinize Emin misiniz?",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) KayitGuncelle(1);
  });
}


var _id = 0;
function urunKayitSilConfirm(id) {
  _id = id;
  Swal.fire({
    html: "Resmi Gizlemek İstediğinize Emin misiniz?",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) urunKayitGuncelle(20);
  });
}


var _id = 0;
function GeriAlConfirm(id) {
  _id = id;
  Swal.fire({
    html: "Resmi Geri Almak İstediğinize Emin misiniz?",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) KayitGuncelle(2);
  });
}


var _id = 0;
function urunGeriAlConfirm(id) {
  _id = id;
  Swal.fire({
    html: "Resmi Geri Almak İstediğinize Emin misiniz?",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) urunKayitGuncelle(21);
  });
}


function TextKaydet(frm) {
  event.preventDefault();
  event.stopPropagation();
  var Message = "";
  $.ajax({
    url: "resim.php", // serileştirilen değerleri ajax.php dosyasına
    type: "POST", // post metodu ile
    data: $(frm).serialize(), // yukarıda serileştirdiğimiz gonderilenform değişkeni
    success: function (data,status) {
      // gonderme işlemi başarılı ise e değişkeni ile gelen değerleri aldı
      if (status === "success") {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Resim başlığı başarıyla kaydedildi",
        });
      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Hata Oluştu",
        });
      }
    },
  });
  return false;
}





function KutuKaydet(frm) {
  event.preventDefault();
  event.stopPropagation();
  var Message = "";
  $.ajax({
    url: "iletisimformlari.php", // serileştirilen değerleri ajax.php dosyasına
    type: "POST", // post metodu ile
    data: $(frm).serialize(), // yukarıda serileştirdiğimiz gonderilenform değişkeni
    success: function (data,status) {
      // gonderme işlemi başarılı ise e değişkeni ile gelen değerleri aldı
      if (status === "success") {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Kutu başarıyla kaydedildi",
        });
      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Hata Oluştu",
        });
      }
    },
  });
  return false;
}






function urunTextKaydet(frm) {
  event.preventDefault();
  event.stopPropagation();
  var Message = "";
  $.ajax({
    url: "db/urun_resim_islem.php", // serileştirilen değerleri ajax.php dosyasına
    type: "POST", // post metodu ile
    data: $(frm).serialize(), // yukarıda serileştirdiğimiz gonderilenform değişkeni
    success: function (data,status) {
      // gonderme işlemi başarılı ise e değişkeni ile gelen değerleri aldı
      if (status === "success") {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Resim başlığı başarıyla kaydedildi",
        });
      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Hata Oluştu",
        });
      }
    },
  });
  return false;
}



function ResimKaliciSil(islem) {
  $.ajax({
    url: "resim.php",
    type: "GET",
    data: { id: _id, islem: islem },
    success: function (data,status) {
      if (status === "success") {
        $("#RowResim" + _id).remove();
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Resim Kalıcı Olarak Silindi",
        });
      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Bir Hata Oluştu",
        });
      }
    },
    error: function (error) {
      //console.log("Error:");
      //console.log(error);
    },
  });
}

var _id = 0;
function ResimKaliciSilConfirm(id) {
  _id = id;
  Swal.fire({
    html: "Resmi kalıcı olarak silmek istediğinize Emin misiniz?",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) ResimKaliciSil(11);
  });
}






function urunResimKaliciSil(islem) {
  $.ajax({
    url: "db/ResimSil.php",
    type: "GET",
    data: { id: _id, islem: islem },
    success: function (data,status) {
      if (status === "success") {
        $("#RowResim" + _id).remove();
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Resim Kalıcı Olarak Silindi",
        });
      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Bir Hata Oluştu",
        });
      }
    },
    error: function (error) {
      //console.log("Error:");
      //console.log(error);
    },
  });
}

var _id = 0;
function urunResimKaliciSilConfirm(id) {
  _id = id;
  Swal.fire({
    html: "Resmi kalıcı olarak silmek istediğinize Emin misiniz?",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) urunResimKaliciSil(22);
  });
}









function SilinenDosyalar(obj) {
  $(".Rows").css("display", "none");
  if ($(obj).prop("checked") === true) {
    $(".Rows").each(function () {
      if ($(this).attr("silindi") === "true") $(this).css("display", "block");
      else $(this).css("display", "none");
    });
  } else {
    $(".Rows").each(function () {
      if ($(this).attr("silindi") === "false") $(this).css("display", "block");
      else $(this).css("display", "none");
    });
  }
}

function DosyaKayitGuncelle(islem) {
  $.ajax({
    url: "dosya.php",
    type: "get",
    data: { id: _id, islem: islem },
    success: function (data,status) {
      if (status === "success") {
        //$('#RowResim' + _id).remove();
        if (islem === 1) $("#RowResim" + _id).attr("silindi", true);
        else $("#RowResim" + _id).attr("silindi", false);

        $("#DosyaSilindifilitreCheck").trigger("click");

        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Başarılı",
        });
      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Hata Oluştu",
        });
      }
    },
    error: function (error) {
      //console.log("Error:");
      //console.log(error);
    },
  });
}


function urunDosyaKayitGuncelle(islem) {
  $.ajax({
    url: "db/DosyaSil.php",
    type: "get",
    data: { id: _id, islem: islem },
    success: function (data,status) {
      if (status === "success") {
        //$('#RowResim' + _id).remove();
        if (islem === 1) $("#RowResim" + _id).attr("silindi", true);
        else $("#RowResim" + _id).attr("silindi", false);

        $("#DosyaSilindifilitreCheck").trigger("click");

        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Döküman Silindi",
        });
      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Hata Oluştu",
        });
      }
    },
    error: function (error) {
      //console.log("Error:");
      //console.log(error);
    },
  });
}


$(document).ready(function () {
  $("#DosyaSilindifilitreCheck").on("change", function () {
    if ($(this).prop("checked") === true) {
      $(".KaydetBtn").css("display", "none");
      $(".SilBtn").css("display", "none");
      $(".GeriAlBtn").css("display", "");
    } else {
      $(".KaydetBtn").css("display", "");
      $(".SilBtn").css("display", "");
      $(".GeriAlBtn").css("display", "none");
    }
  });
});
var _id = 0;
function DosyaSilConfirm(id) {
  _id = id;
  Swal.fire({
    html: "Dökümanı Gizlemek İstediğinize Emin misiniz?",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) DosyaKayitGuncelle(1);
  });
}


var _id = 0;
function DosyaGeriAlConfirm(id) {
  _id = id;
  Swal.fire({
    html: "Dökümanı Göstermek İstediğinize Emin misiniz?",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) DosyaKayitGuncelle(2);
  });
}


function DosyaKaliciSil(islem) {
  $.ajax({
    url: "dosya.php",
    type: "GET",
    data: { id: _id, islem: islem },
    success: function (data,status) {
      if (status === "success") {
        $("#RowDosya" + _id).remove();
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Dosya Kalıcı Olarak Silindi",
        });
      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Bir Hata Oluştu",
        });
      }
    },
    error: function (error) {
      //console.log("Error:");
      //console.log(error);
    },
  });
}

var _id = 0;
function DosyaKaliciSilConfirm(id) {
  _id = id;
  Swal.fire({
    html: "Dosyayı kalıcı olarak silmek istediğinize emin misiniz?",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) DosyaKaliciSil(3);
  });
}




function VideoKaliciSil(islem) {
  $.ajax({
    url: "video.php",
    type: "GET",
    data: { id: _id, islem: islem },
    success: function (data,status) {
      if (status === "success") {
        $("#RowDosya" + _id).remove();
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Video Kalıcı Olarak Silindi",
        });
      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Bir Hata Oluştu",
        });
      }
    },
    error: function (error) {
      //console.log("Error:");
      //console.log(error);
    },
  });
}

var _id = 0;
function VideoKaliciSilConfirm(id) {
  _id = id;
  Swal.fire({
    html: "Videoyu kalıcı olarak silmek istediğinize emin misiniz?",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) VideoKaliciSil(3);
  });
}



var _id = 0;
function urunDosyaSilConfirm(id) {
  _id = id;
  Swal.fire({
    html: "Dökümanı Gizlemek İstediğinize Emin misiniz?",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) urunDosyaKayitGuncelle(11);
  });
}


var _id = 0;
function urunDosyaGeriAlConfirm(id) {
  _id = id;
  Swal.fire({
    html: "Dökümanı Geri Almak İstediğinize Emin misiniz?",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) urunDosyaKayitGuncelle(12);
  });
}




var _id = 0;
function urunDosyaKaliciSilConfirm(id) {
  _id = id;
  Swal.fire({
    html: "Dosyayı kalıcı olarak silmek istediğinize emin misiniz?",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) urunDosyaKaliciSil(13);
  });
}


function urunDosyaKaliciSil(islem) {
  $.ajax({
    url: "db/DosyaSil.php",
    type: "GET",
    data: { id: _id, islem: islem },
    success: function (data,status) {
      if (status === "success") {
        $("#RowDosya" + _id).remove();
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Dosya Kalıcı Olarak Silindi",
        });
      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Bir Hata Oluştu",
        });
      }
    },
    error: function (error) {
      //console.log("Error:");
      //console.log(error);
    },
  });
}





function DosyaTextKaydet(frm) {
  event.preventDefault();
  event.stopPropagation();
  var Message = "";
  $.ajax({
    url: "dosya.php", // serileştirilen değerleri ajax.php dosyasına
    type: "POST", // post metodu ile
    data: $(frm).serialize(), // yukarıda serileştirdiğimiz gonderilenform değişkeni
    success: function (data,status) {
      // gonderme işlemi başarılı ise e değişkeni ile gelen değerleri aldı
      if (status === "success") {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Döküman ismi başarıyla değiştirildi.",
        });
      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Hata Oluştu",
        });
      }
    },
  });
  return false;
}





function VideoTextKaydet(frm) {
  event.preventDefault();
  event.stopPropagation();
  var Message = "";
  $.ajax({
    url: "video.php", // serileştirilen değerleri ajax.php dosyasına
    type: "POST", // post metodu ile
    data: $(frm).serialize(), // yukarıda serileştirdiğimiz gonderilenform değişkeni
    success: function (data,status) {
      // gonderme işlemi başarılı ise e değişkeni ile gelen değerleri aldı
      if (status === "success") {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Video ismi başarıyla değiştirildi.",
        });
      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Hata Oluştu",
        });
      }
    },
  });
  return false;
}



function urunDosyaTextKaydet(frm) {
  event.preventDefault();
  event.stopPropagation();
  var Message = "";
  $.ajax({
    url: "db/urun_dosya_islem.php", // serileştirilen değerleri ajax.php dosyasına
    type: "POST", // post metodu ile
    data: $(frm).serialize(), // yukarıda serileştirdiğimiz gonderilenform değişkeni
    success: function (data,status) {
      // gonderme işlemi başarılı ise e değişkeni ile gelen değerleri aldı
      if (status === "success") {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Döküman ismi başarıyla değiştirildi.",
        });
      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Hata Oluştu",
        });
      }
    },
  });
  return false;
}


function MenuGuncelle(islem) {
  $.ajax({
    url: "menuler.php",
    type: "get",
    data: { id: _id, islem: islem },
    success: function (data,status) {
      data = data.replace("<br>", "");
      if (data === "OK") {
        $("#RowMenu" + _id).remove();
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Menü Silindi",
        });
      }
      else if (data === "OKK") {
        location.reload(true);
      }
      else if (data === "VAR") {

        Swal.fire({
          html: "Bu menü alt menülere sahip. Lütfen önce alt menüleri silin.",
          icon: "error",
          buttonsStyling: false,
          confirmButtonText: "Tamam",
          cancelButtonText: "Kapat",
          customClass: {
            confirmButton: "btn btn-danger me-3",
            cancelButton: "btn btn-secondary",
          },
        });


      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Bir Hata Oluştu",
        });
      }
    },
    error: function (error) {
      //console.log("Error:");
      //console.log(error);
    },
  });
}

var _id = 0;
function MenuSilConfirm(id) {
  _id = id;
  Swal.fire({
    html: "Menüyü Silmek İstediğinize Emin misiniz?",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) MenuGuncelle(1);
  });
}




function FirmaGuncelle(islem) {
  $.ajax({
    url: "firmasil.php",
    type: "get",
    data: { id: _id, islem: islem },
    success: function (data,status) {
      data = data.replace("<br>", "");
      if (data === "OK") {
        $("#RowMenu" + _id).remove();
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Firma Silindi",
        });
      }
      else if (data === "OKK") {
        location.reload(true);
      }
      else if (data === "VAR") {

        Swal.fire({
          html: "Bu menü alt menülere sahip. Lütfen önce alt menüleri silin.",
          icon: "error",
          buttonsStyling: false,
          confirmButtonText: "Tamam",
          cancelButtonText: "Kapat",
          customClass: {
            confirmButton: "btn btn-danger me-3",
            cancelButton: "btn btn-secondary",
          },
        });


      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Bir Hata Oluştu",
        });
      }
    },
    error: function (error) {
      //console.log("Error:");
      //console.log(error);
    },
  });
}

var _id = 0;
function FirmaSilConfirm(id) {
  _id = id;
  Swal.fire({
    html: "Firmayı Silmek İstediğinize Emin misiniz?",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) FirmaGuncelle(1);
  });
}




function IndirimGuncelle(islem) {
  $.ajax({
    url: "indirimrehberi.php",
    type: "get",
    data: { id: _id, islem: islem },
    success: function (data,status) {
      data = data.replace("<br>", "");
      if (data === "OK") {
        $("#RowMenu" + _id).remove();
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "İndirim Rehberi Silindi",
        });
      }
      else if (data === "OKK") {
        location.reload(true);
      }
      else if (data === "VAR") {

        Swal.fire({
          html: "Bu menü alt menülere sahip. Lütfen önce alt menüleri silin.",
          icon: "error",
          buttonsStyling: false,
          confirmButtonText: "Tamam",
          cancelButtonText: "Kapat",
          customClass: {
            confirmButton: "btn btn-danger me-3",
            cancelButton: "btn btn-secondary",
          },
        });


      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Bir Hata Oluştu",
        });
      }
    },
    error: function (error) {
      //console.log("Error:");
      //console.log(error);
    },
  });
}

var _id = 0;
function IndirimSilConfirm(id) {
  _id = id;
  Swal.fire({
    html: "İndirim Rehberini Silmek İstediğinize Emin misiniz?",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) IndirimGuncelle(1);
  });
}



function GrupGuncelle(islem) {
  $.ajax({
    url: "gruplar.php",
    type: "get",
    data: { id: _id, islem: islem },
    success: function (data,status) {
      data = data.replace("<br>", "");
      if (data === "OK") {
        $("#RowMenu" + _id).remove();
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Grup Silindi",
        });
      }
      else if (data === "OKK") {
        location.reload(true);
      }
      else if (data === "VAR") {

        Swal.fire({
          html: "Bu grup alt gruplara sahip. Lütfen önce alt grupları silin.",
          icon: "error",
          buttonsStyling: false,
          confirmButtonText: "Tamam",
          cancelButtonText: "Kapat",
          customClass: {
            confirmButton: "btn btn-danger me-3",
            cancelButton: "btn btn-secondary",
          },
        });


      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Bir Hata Oluştu",
        });
      }
    },
    error: function (error) {
      //console.log("Error:");
      //console.log(error);
    },
  });
}

var _id = 0;
function GrupSilConfirm(id) {
  _id = id;
  Swal.fire({
    html: "Grubu Silmek İstediğinize Emin misiniz?",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) GrupGuncelle(1);
  });
}





function TakvimGuncelle(islem) {
  $.ajax({
    url: "takvim.php",
    type: "get",
    data: { id: _id, islem: islem },
    success: function (data,status) {
      data = data.replace("<br>", "");
      if (data === "OK") {
        $("#RowMenu" + _id).remove();
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Etkinlik Silindi",
        });
      }
      else if (data === "OKK") {
        location.reload(true);
      }
      else if (data === "VAR") {

        Swal.fire({
          html: "Bu etkinlik alt menülere sahip. Lütfen önce alt menüleri silin.",
          icon: "error",
          buttonsStyling: false,
          confirmButtonText: "Tamam",
          cancelButtonText: "Kapat",
          customClass: {
            confirmButton: "btn btn-danger me-3",
            cancelButton: "btn btn-secondary",
          },
        });


      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Bir Hata Oluştu",
        });
      }
    },
    error: function (error) {
      //console.log("Error:");
      //console.log(error);
    },
  });
}

var _id = 0;
function TakvimSilConfirm(id) {
  _id = id;
  Swal.fire({
    html: "Etkinliği Silmek İstediğinize Emin misiniz?",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) TakvimGuncelle(1);
  });
}





function HesapDondur(islem) {
  $.ajax({
    url: "kullanicilar.php",
    type: "POST",
    data: { id: _id, islem: islem },
    success: function (data,status) {
      data = data.replace("<br>", "");
      if (status == "success") {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Hesap Donduruldu",
        });
      }
    else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Bir Hata Oluştu",
        });
      }
    },
  });
}

var _id = 0;
function HesapDondurConfirm(id) {
  _id = id;
  Swal.fire({
    html: "Hesabı Dondurmak İstediğinize Emin misiniz?",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) HesapDondur(1538);
  });
}





function HesapAc(islem) {
  $.ajax({
    url: "kullanicilar.php",
    type: "POST",
    data: { id: _id, islem: islem },
    success: function (data,status) {
      data = data.replace("<br>", "");
      if (status == "success") {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Hesap Tekrar Aktifleştirildi.",
        });
      }
    else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Bir Hata Oluştu",
        });
      }
    },
  });
}

var _id = 0;
function HesapAcConfirm(id) {
  _id = id;
  Swal.fire({
    html: "Hesabı Tekrar Aktifleştirmek İstediğinize Emin misiniz?",
    icon: "info",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-info me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) HesapAc(1539);
  });
}






function HesapSil(islem) {
  $.ajax({
    url: "kullanicilar.php",
    type: "POST",
    data: { id: _id, islem: islem },
    success: function (data,status) {
      data = data.replace("<br>", "");
      if (status == "success") {
        event.preventDefault();
        event.stopPropagation();
        window.location = "kullanicilar.php";
      }
    else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Bir Hata Oluştu",
        });
      }
    },
  });
}

var _id = 0;
function HesapSilConfirm(id) {
  _id = id;
  Swal.fire({
    html: "Hesabı Silmek İstediğinize Emin misiniz?",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) HesapSil(1540);
  });
}





function KategoriGuncelle(islem) {
  $.ajax({
    url: "kategoriler.php",
    type: "get",
    data: { id: _id, islem: islem },
    success: function (data,status) {
      data = data.replace("<br>", "");
      if (data === "OK") {
        $("#RowMenu" + _id).remove();
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Menü Silindi",
        });
      }
      else if (data === "OKK") {
        location.reload(true);
      }
      else if (data === "VAR") {

        Swal.fire({
          html: "Bu menü alt menülere sahip. Lütfen önce alt menüleri silin.",
          icon: "error",
          buttonsStyling: false,
          confirmButtonText: "Tamam",
          cancelButtonText: "Kapat",
          customClass: {
            confirmButton: "btn btn-danger me-3",
            cancelButton: "btn btn-secondary",
          },
        });


      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Bir Hata Oluştu",
        });
      }
    },
    error: function (error) {
      //console.log("Error:");
      //console.log(error);
    },
  });
}

var _id = 0;
function KategoriSilConfirm(id) {
  _id = id;
  Swal.fire({
    html: "Kategoriyi Silmek İstediğinize Emin misiniz?",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) KategoriGuncelle(1);
  });
}






function UrunGuncelle(islem) {
  $.ajax({
    url: "urunler.php",
    type: "get",
    data: { id: _id, islem: islem },
    success: function (data,status) {
      data = data.replace("<br>", "");
      if (status === "success") {
        $("#RowMenu" + _id).remove();
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Ürün Silindi",
        });
      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Bir Hata Oluştu",
        });
      }
    },
    error: function (error) {
      //console.log("Error:");
      //console.log(error);
    },
  });
}

var _id = 0;
function UrunSilConfirm(id) {
  _id = id;
  Swal.fire({
    html: "Ürünü Silmek İstediğinize Emin misiniz?",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) UrunGuncelle(1);
  });
}






function MagazaUrunGuncelle(islem) {
  $.ajax({
    url: "magazaurunler.php",
    type: "get",
    data: { id: _id, islem: islem },
    success: function (data,status) {
      data = data.replace("<br>", "");
      if (status === "success") {
        $("#RowMenu" + _id).remove();
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Ürün Silindi",
        });
      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Bir Hata Oluştu",
        });
      }
    },
    error: function (error) {
      //console.log("Error:");
      //console.log(error);
    },
  });
}

var _id = 0;
function MagazaUrunSilConfirm(id) {
  _id = id;
  Swal.fire({
    html: "Ürünü Silmek İstediğinize Emin misiniz?",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) MagazaUrunGuncelle(1);
  });
}





function DizinYedekle(id) {
  Swal.fire({
    html: "Dizin yedekleniyor. Lütfen bekleyin...",
    icon: "success",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Tamam",
    cancelButtonText: "Kapat",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  });
  var _id = 2;
  $.ajax({
    url: "yedekle.php",
    type: "get",
    data: { id: _id },
    success: function (data,status) {
      if (status === "success") {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Yedek Başarıyla Oluşturuldu",
        });
      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Bir Hata Oluştu",
        });
      }
    },
    error: function (error) {
      //console.log("Error:");
      //console.log(error);
    },
  });
}

function DizinYedekleConfirm() {
  Swal.fire({
    html: "Dizin Yedeği Oluşturmak İstediğinize Emin misiniz?",
    icon: "question",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-secondary me-3",
      cancelButton: "btn btn-danger",
    },
  }).then((result) => {
    if (result["isConfirmed"]) DizinYedekle();
  });
}












function DBYedekle(id) {
  Swal.fire({
    html: "Veritabanı yedekleniyor. Lütfen bekleyin...",
    icon: "success",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Tamam",
    cancelButtonText: "Kapat",
    customClass: {
      confirmButton: "btn btn-danger me-3 something",
      cancelButton: "btn btn-secondary",
    },
  });
  var _id = 3;
  $.ajax({
    url: "yedekle.php",
    type: "get",
    data: { id: _id },
    success: function (data,status) {
      if (status === "success") {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Yedek Başarıyla Oluşturuldu",
        });
      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Bir Hata Oluştu",
        });
      }
    },

    error: function (error) {
      //console.log("Error:");
      //console.log(error);
    },
  });
}

function DBYedekleConfirm() {
  Swal.fire({
    html: "Veritabanı Yedeği Oluşturmak İstediğinize Emin misiniz?",
    icon: "question",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-secondary me-3",
      cancelButton: "btn btn-danger",
    },
  }).then((result) => {
    if (result["isConfirmed"]) DBYedekle(3);
  });
}



function DilAyarlaConfirm(dil) {
  Swal.fire({
    html: "Varsayılan dil olarak seçmek istiyor musunuz?",
    icon: "question",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-secondary me-3",
      cancelButton: "btn btn-danger",
    },
  }).then((result) => {
    
    var _islem = 38;
    $.ajax({
      url: "ayarlar.php",
      type: "post",
      data: { dil: dil, islem: _islem },
      success: function (data,status) {
        if (status === "success") {
          event.preventDefault();
          event.stopPropagation();
          return $.growl.notice1({
            title: "İşlem Sonucu",
            message: "Varsayılan dil başarıyla değiştirildi.",
          });
        } else {
          event.preventDefault();
          event.stopPropagation();
          return $.growl.error1({
            title: "İşlem Sonucu",
            message: "Bir Hata Oluştu",
          });
        }
      },
  
      error: function (error) {
        //console.log("Error:");
        //console.log(error);
      },
    });



  });
}









function TamYedekle(id) {
  Swal.fire({
    html: "Site tam yedekleniyor. (Veritabanı + Dosyalar) Lütfen bekleyin...",
    icon: "success",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Tamam",
    cancelButtonText: "Kapat",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  });
  var _id = 1;
  $.ajax({
    url: "yedekle.php",
    type: "get",
    data: { id: _id },
    success: function (data,status) {
      if (status === "success") {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Yedek Başarıyla Oluşturuldu",
        });
      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Bir Hata Oluştu",
        });
      }
    },
    error: function (error) {
      //console.log("Error:");
      //console.log(error);
    },
  });
}
function TamYedekleConfirm() {
  Swal.fire({
    html: "Tam Site Yedeği Oluşturmak İstediğinize Emin misiniz?",
    icon: "question",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-secondary me-3",
      cancelButton: "btn btn-danger",
    },
  }).then((result) => {
    if (result["isConfirmed"]) TamYedekle(1);
  });
}








function YedekSil(yedeksil) {
  $.ajax({
    url: "ayarlar.php",
    type: "get",
    data: { id: _id, yedeksil: yedeksil },
    success: function (data,status) {
      data = data.replace("<br>", "");
      if (status === "success") {
        $("#RowYedek" + _id).remove();
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Yedek Silindi",
        });
      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Bir Hata Oluştu",
        });
      }
    },
    error: function (error) {
      //console.log("Error:");
      //console.log(error);
    },
  });
}

var _id = 0;
function YedekSilConfirm(id) {
  _id = id;
  Swal.fire({
    html: "Yedeği Silmek İstediğinize Emin misiniz?",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) YedekSil(1);
  });
}

function YedekYukle() {
  var _id = 4;
  $.ajax({
    url: "yedekle.php",
    type: "get",
    data: { yedekadi: _yedekadi, id: _id },
    success: function (data,status) {
      data = data.replace("<br>", "");
      if (status === "success") {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Yedek Yüklendi. Lütfen Sayfayı Yenileyin.",
        });
      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Bir Hata Oluştu",
        });
      }
    },
    error: function (error) {
      //console.log("Error:");
      //console.log(error);
    },
  });
}

var _yedekadi = 0;
function YedekYukleConfirm(yedekadi) {
  _yedekadi = yedekadi;
  Swal.fire({
    html: "Yedeği Yüklemek İstediğinize Emin misiniz? Bu işlemin geri dönüşü yoktur!",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) YedekYukle();
  });
}

console.clear();

function KullaniciSil(islem) {
  $.ajax({
    url: "kullanicilar.php",
    type: "POST",
    data: { id: _id, islem: islem },
    success: function (data,status) {
      if (status === "success") {
        $("#RowKullanici" + _id).remove();
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Kullanıcı Silindi",
        });
      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Bir Hata Oluştu",
        });
      }
    },
    error: function (error) {
      //console.log("Error:");
      //console.log(error);
    },
  });
}

var _id = 0;
function KullaniciSilConfirm(id) {
  _id = id;
  Swal.fire({
    html: "Kullanıcıyı Silmek İstediğinize Emin misiniz?",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) KullaniciSil(1540);
  });
}

function MesajSil(islem) {
  var _is = 1;
  $.ajax({
    url: "iletisimformlari.php",
    type: "POST",
    data: { id: _id, islem: islem, is: _is },
    success: function (data,status) {
      if (status === "success") {
        $("#RowMesaj" + _id).remove();
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Mesaj Silindi",
        });
      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Bir Hata Oluştu",
        });
      }
    },
    error: function (error) {
      //console.log("Error:");
      //console.log(error);
    },
  });
}

var _id = 0;
function MesajSilConfirm(id) {
  _id = id;
  Swal.fire({
    html: "Mesajı Silmek İstediğinize Emin misiniz?",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) MesajSil(1);
  });
}

function MesajGeriAl(islem) {
  var _is = 2;
  $.ajax({
    url: "iletisimformlari.php",
    type: "POST",
    data: { id: _id, islem: islem, is: _is  },
    success: function (data,status) {
      if (status === "success") {
        $("#RowMesaj" + _id).remove();
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Mesaj Geri Alındı",
        });
      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Bir Hata Oluştu",
        });
      }
    },
    error: function (error) {
      //console.log("Error:");
      //console.log(error);
    },
  });
}

var _id = 0;
function MesajGeriAlConfirm(id) {
  _id = id;
  Swal.fire({
    html: "Mesajı Geri Almak İstediğinize Emin misiniz?",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) MesajGeriAl(2);
  });
}





function MesajKaliciSil(islem) {
  var _is = 25;
  $.ajax({
    url: "iletisimformlari.php",
    type: "POST",
    data: { id: _id, islem: islem, is: _is  },
    success: function (data,status) {
      if (status === "success") {
        $("#RowMesaj" + _id).remove();
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Mesaj Silindi",
        });
      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Bir Hata Oluştu",
        });
      }
    },
    error: function (error) {
      //console.log("Error:");
      //console.log(error);
    },
  });
}

var _id = 0;
function MesajKaliciSilConfirm(id) {
  _id = id;
  Swal.fire({
    html: "Mesajı Geri Almak İstediğinize Emin misiniz?",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) MesajKaliciSil(25);
  });
}







function FormSil(islem) {
  $.ajax({
    url: "iletisimformlari.php",
    type: "POST",
    data: { id: _id, islem: islem },
    success: function (data,status) {
      if (status === "success") {
        $("#RowForm" + _id).remove();
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Form Silindi",
        });
      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Bir Hata Oluştu",
        });
      }
    },
    error: function (error) {
      //console.log("Error:");
      //console.log(error);
    },
  });
}

var _id = 0;
function FormSilConfirm(id) {
  _id = id;
  Swal.fire({
    html: "Formu Silmek İstediğinize Emin misiniz?",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) FormSil(5);
  });
}


function TopluMailSil(islem) {
  $.ajax({
    url: "iletisimformlari.php",
    type: "POST",
    data: { id: _id, islem: islem },
    success: function (data,status) {
      if (status === "success") {
        $("#RowTopluMail" + _id).remove();
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Mail Adresi Silindi",
        });
      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Bir Hata Oluştu",
        });
      }
    },
    error: function (error) {
      //console.log("Error:");
      //console.log(error);
    },
  });
}

var _id = 0;
function TopluMailSilConfirm(id) {
  _id = id;
  Swal.fire({
    html: "Formu Silmek İstediğinize Emin misiniz?",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) TopluMailSil(50);
  });
}





function AciliricerikSil(islem) {
  $.ajax({
    url: "aicerik.php",
    type: "POST",
    data: { id: _id, islem: islem },
    success: function (data,status) {
      if (status === "success") {
        $("#Rowai" + _id).remove();
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Açılır İçerik Silindi",
        });
      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Bir Hata Oluştu",
        });
      }
    },
    error: function (error) {
      //console.log("Error:");
      //console.log(error);
    },
  });
}

var _id = 0;
function AciliricerikSilConfirm(id) {
  _id = id;
  Swal.fire({
    html: "Açılır İçeriği Silmek İstediğinize Emin misiniz?",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) AciliricerikSil(1);
  });
}







function bultenSil(islem) {
  $.ajax({
    url: "bulten.php",
    type: "POST",
    data: { id: _id, islem: islem },
    success: function (data,status) {
      if (status === "success") {
        $("#Rowai" + _id).remove();
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Bülten Silindi",
        });
      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Bir Hata Oluştu",
        });
      }
    },
    error: function (error) {
      //console.log("Error:");
      //console.log(error);
    },
  });
}

var _id = 0;
function bultenSilConfirm(id) {
  _id = id;
  Swal.fire({
    html: "Bülteni Silmek İstediğinize Emin misiniz?",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) bultenSil(1);
  });
}





function KisilerSil(islem) {
  $.ajax({
    url: "kisiekle.php",
    type: "POST",
    data: { id: _id, islem: islem },
    success: function (data,status) {
      if (status === "success") {
        $("#Rowai" + _id).remove();
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Kişi Silindi",
        });
      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Bir Hata Oluştu",
        });
      }
    },
    error: function (error) {
      //console.log("Error:");
      //console.log(error);
    },
  });
}

var _id = 0;
function KisilerSilConfirm(id) {
  _id = id;
  Swal.fire({
    html: "Kişiyi Silmek İstediğinize Emin misiniz?",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) KisilerSil(1);
  });
}


function AcilirKisilerSil(islem) {
  $.ajax({
    url: "acilirkisiekle.php",
    type: "POST",
    data: { id: _id, islem: islem },
    success: function (data,status) {
      if (status === "success") {
        $("#Rowai" + _id).remove();
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Kişi Silindi",
        });
      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Bir Hata Oluştu",
        });
      }
    },
    error: function (error) {
      //console.log("Error:");
      //console.log(error);
    },
  });
}

var _id = 0;
function AcilirKisilerSilConfirm(id) {
  _id = id;
  Swal.fire({
    html: "Kişiyi Silmek İstediğinize Emin misiniz?",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) AcilirKisilerSil(1);
  });
}


function menuler() {
  var element = document.getElementById("gizle");
  element.classList.add("gizle");
  element.classList.remove("goster");
  var element = document.getElementById("goster");
  element.classList.remove("gizle");
  element.classList.add("goster");
}

function menuekle() {
  var element = document.getElementById("gizle");
  element.classList.remove("gizle");
  element.classList.add("goster");
  var element = document.getElementById("goster");
  element.classList.add("gizle");
  element.classList.remove("goster");
}



$(document).ready(function() {
    // Form submit olayını yakala
    $('#demo-form2').submit(function(event) {
        // Başlık değerlerini al
        $('.baslik11111111111111').each(function() {
            var dil = $(this).attr('dil');
            var titleValue = $(this).val().trim();

            // Başlık boş mu diye kontrol et
            if (titleValue === '') {
                // Başlık boş ise SweetAlert ile uyarı ver
                event.preventDefault(); // Form postunu engelle
                Swal.fire({
                    icon: 'error',
                    title: 'Uyarı',
                    text: 'Lütfen ' + dil + ' dili için bir başlık ekleyin!',
                });
                // Hata bulunduğunda döngüyü durdur
                return false;
            }
        });
    });
});



function DilSil(islem) {
  $.ajax({
    url: "ayarlar.php",
    type: "get",
    data: { id: _id, islem: islem, kisaltma: _kisaltma },
    success: function (data,status) {
      data = data.replace("<br>", "");
      if (status === "success") {
        $("#RowDil" + _id).remove();
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Dil Silindi",
        });
      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Bir Hata Oluştu",
        });
      }
    },
    error: function (error) {
      //console.log("Error:");
      //console.log(error);
    },
  });
}

var _id = 0;
function DilSilConfirm(id, kisaltma) {
  _id = id;
  _kisaltma = kisaltma;
  Swal.fire({
    html: "Dili silmek istediğinize emin misiniz? Bu dile ait tüm kayıtlar silinir. Yedeğiniz yoksa geri dönüşünüz yoktur!",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) DilSil(99);
  });
}








function KutuSil(islem) {
  $.ajax({
    url: "iletisimformlari.php",
    type: "GET",
    data: { id: _id, islem: islem },
    success: function (data,status) {
      if (status === "success") {
        $("#RowKutu" + _id).remove();
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Kutu Silindi",
        });
      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Bir Hata Oluştu",
        });
      }
    },
    error: function (error) {
      //console.log("Error:");
      //console.log(error);
    },
  });
}

var _id = 0;
function KutuSilConfirm(id) {
  _id = id;
  Swal.fire({
    position: 'top-end',
    html: "Kutuyu silmek istediğinize emin misiniz? Eğer aynı kutu diğer formlarda varsa oradan da silinecektir. İletişim formları yedeğiniz yoksa geri dönüşü yoktur.",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) KutuSil(98);
  });
}






function BayiSil(islem) {
  $.ajax({
    url: "bayiler.php",
    type: "GET",
    data: { id: _id, islem: islem },
    success: function (data,status) {
      if (status === "success") {
        $("#RowMenu" + _id).remove();
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Bayi Silindi",
        });
      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Bir Hata Oluştu",
        });
      }
    },
    error: function (error) {
      //console.log("Error:");
      //console.log(error);
    },
  });
}

var _id = 0;
function BayiSilConfirm(id) {
  _id = id;
  Swal.fire({
    position: 'center',
    html: "Bayiyi silmek istediğinize emin misiniz?",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) BayiSil(1);
  });
}



function BankaSil(islem) {
  $.ajax({
    url: "bankahesaplari.php",
    type: "GET",
    data: { id: _id, islem: islem },
    success: function (data,status) {
      if (status === "success") {
        $("#RowMenu" + _id).remove();
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Banka Hesabı Silindi",
        });
      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Bir Hata Oluştu",
        });
      }
    },
    error: function (error) {
      //console.log("Error:");
      //console.log(error);
    },
  });
}

var _id = 0;
function BankaSilConfirm(id) {
  _id = id;
  Swal.fire({
    position: 'center',
    html: "Banka hesabını silmek istediğinize emin misiniz?",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) BankaSil(1);
  });
}




function KargoSil(islem) {
  $.ajax({
    url: "kargofirmalari.php",
    type: "GET",
    data: { id: _id, islem: islem },
    success: function (data,status) {
      if (status === "success") {
        $("#RowMenu" + _id).remove();
        event.preventDefault();
        event.stopPropagation();
        return $.growl.notice1({
          title: "İşlem Sonucu",
          message: "Kargo Firması Silindi",
        });
      } else {
        event.preventDefault();
        event.stopPropagation();
        return $.growl.error1({
          title: "İşlem Sonucu",
          message: "Bir Hata Oluştu",
        });
      }
    },
    error: function (error) {
      //console.log("Error:");
      //console.log(error);
    },
  });
}

var _id = 0;
function KargoSilConfirm(id) {
  _id = id;
  Swal.fire({
    position: 'center',
    html: "Kargo firmasını silmek istediğinize emin misiniz?",
    icon: "error",
    buttonsStyling: false,
    showCancelButton: true,
    confirmButtonText: "Evet",
    cancelButtonText: "İptal",
    customClass: {
      confirmButton: "btn btn-danger me-3",
      cancelButton: "btn btn-secondary",
    },
  }).then((result) => {
    if (result["isConfirmed"]) KargoSil(1);
  });
}





const telefonInput = document.querySelector('#telefon');
telefonInput.addEventListener('input', function() {
  const sadeceSayilar = this.value.replace(/\D/g, '');
  const formattedTelefon = sadeceSayilar.replace(/^(\d{1})(\d{3})(\d{3})(\d{2})(\d{2})$/, '$1$2 $3 $4 $5');
  this.value = formattedTelefon;
});




$(document).ready(function() {
    $("#parolatekrar").keyup(function() {
      var password1 = $("#parola").val();
      var password2 = $(this).val();
      
      if (password1 === password2) {
        $("#passwordMatch").html('');
        $('#parolaguncelle').attr('disabled', false);
        
        if (password1.length > 0) {
          var passwordStrength = checkPasswordStrength(password1);
          $("#passwordStrength").html("<div class='card-alert alert alert-danger mb-0'>Güvenlik Seviyesi: " + passwordStrength + "</div>");
        } else {
          $("#passwordStrength").html("");
        }
      } else {
        $("#passwordMatch").html('<div class="card-alert alert alert-danger mb-0">Parolalar eşleşmiyor...</div>');
        $("#passwordStrength").text("");
        $('#parolaguncelle').attr('disabled', true);
      }
    });
  });
  

  function checkPasswordStrength(password) {
    var strength = 0;
    
    // Parola en az 8 karakter uzunluğunda olmalı
    if (password.length >= 8) {
      strength += 1;
    }
    
    // Parola en az bir büyük harf içermeli
    if (/[A-Z]/.test(password)) {
      strength += 1;
    }
    
    // Parola en az bir küçük harf içermeli
    if (/[a-z]/.test(password)) {
      strength += 1;
    }
    
    // Parola en az bir rakam içermeli
    if (/[0-9]/.test(password)) {
      strength += 1;
    }
    
    // Parola en az bir özel karakter içermeli
    if (/[^A-Za-z0-9]/.test(password)) {
      strength += 1;
    }
    
    // Parola güvenlik seviyesine göre değerlendirme yapılıyor
    if (strength === 0) {
      $('#parolaguncelle').attr('disabled', true);
      return "Çok Zayıf";
    } else if (strength === 1) {
      $('#parolaguncelle').attr('disabled', true);
      return "Zayıf";
    } else if (strength === 2) {
      $('#parolaguncelle').attr('disabled', true);
      return "Orta";
    } else if (strength === 3) {
      $('#parolaguncelle').attr('disabled', false);
      return "Güçlü";
    } else {
      $('#parolaguncelle').attr('disabled', false);
      return "Çok Güçlü";

    }
  }




  function copyLink(element) {
    var link = element.getAttribute('link');

    var tempInput = document.createElement('input');
    tempInput.value = link;
    document.body.appendChild(tempInput);

    tempInput.select();
    document.execCommand('copy');
    document.body.removeChild(tempInput);

    event.preventDefault();
    event.stopPropagation();
    return $.growl.warning1({
    title: "İşlem Başarılı",
    message: "Link Kopyalandı",
    });
}