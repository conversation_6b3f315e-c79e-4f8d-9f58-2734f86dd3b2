<?php
namespace Verot\Upload;
require 'class.upload.php';

$image = new Upload( $_FILES[ 'resim' ] );
if ( $image->uploaded ) {

  if($maxcozunurluluk > 0) {
      if($image->image_src_x > $maxcozunurluluk) { ?>
      <script> alert('Resim yüksekliği veya genişliği izin verilenden yüksek. Lütfen daha küçük bir resim yükleyin.');</script>
     <?php 
  $resim = $resimcek['resim'];   
  } else { goto islem; }
  }else {
      islem:
      $size = getimagesize($_FILES['resim']['tmp_name']);
      $width = $size[0];
      $height = $size[1];

      $new_width = $width * $kucultmeorani / 100;
      $new_height = $height * $kucultmeorani / 100;

      $yol = 'images/'.$klasorcek['yetki'].'/';
      $image->image_convert = 'webp';
      $image->file_max_size = $resimboyut * 1000000;
      $image->webp_quality = $kalite;
      $image->allowed = array ( 'image/*' );

      if($resimkucultme == 1){
        if($dikeylimit <= $height & $yataylimit <= $width){
        $image->image_resize = true;
        $image->image_x = $new_width;
        $image->image_y = $new_height;
      }
      }


      $image->Process( '../'.$yol );
      $isim = $image->file_dst_name;
      if ( $image->processed ) {
          $resim = $yol.SEOdosya($image->file_dst_name);
          rename("../$yol/$isim","../$yol/".SEOdosya($isim)."");
      } else {
          print 'Bir sorun oluştu: ' . $image->error; 
      }
  }
}

$image = new Upload( $_FILES[ 'resim1' ] );
if ( $image->uploaded ) {

  if($maxcozunurluluk > 0) {
      if($image->image_src_x > $maxcozunurluluk) { ?>
      <script> alert('Resim yüksekliği veya genişliği izin verilenden yüksek. Lütfen daha küçük bir resim yükleyin.');</script>
     <?php 
  $resim1 = $resimcek['resim1'];   
  } else { goto resim; }
  }else {
      resim:
      $size = getimagesize($_FILES['resim1']['tmp_name']);
      $width = $size[0];
      $height = $size[1];

      $new_width = $width * $kucultmeorani / 100;
      $new_height = $height * $kucultmeorani / 100;

      $yol = 'images/'.$klasorcek['yetki'].'/';
      $image->image_convert = 'webp';
      $image->file_max_size = $resimboyut * 1000000;
      $image->webp_quality = $kalite;
      $image->allowed = array ( 'image/*' );

      if($resimkucultme == 1){
        if($dikeylimit <= $height & $yataylimit <= $width){
        $image->image_resize = true;
        $image->image_x = $new_width;
        $image->image_y = $new_height;
      }
      }
      $image->Process( '../'.$yol );
      $isim = $image->file_dst_name;
      if ( $image->processed ) {
          $resim1 = $yol.SEOdosya($image->file_dst_name);
          rename("../$yol/$isim","../$yol/".SEOdosya($isim)."");
      } else {
          print 'Bir sorun oluştu: ' . $image->error; 
      }
  }
}



$image = new Upload( $_FILES[ 'resim2' ] );
if ( $image->uploaded ) {

  if($maxcozunurluluk > 0) {
      if($image->image_src_x > $maxcozunurluluk) { ?>
      <script> alert('Resim yüksekliği veya genişliği izin verilenden yüksek. Lütfen daha küçük bir resim yükleyin.');</script>
     <?php 
  $resim2 = $resimcek['resim2'];   
  } else { goto resim3; }
  }else {
      resim3:
      $size = getimagesize($_FILES['resim2']['tmp_name']);
      $width = $size[0];
      $height = $size[1];

      $new_width = $width * $kucultmeorani / 100;
      $new_height = $height * $kucultmeorani / 100;

      $yol = 'images/'.$klasorcek['yetki'].'/';
      $image->image_convert = 'webp';
      $image->file_max_size = $resimboyut * 1000000;
      $image->webp_quality = $kalite;
      $image->allowed = array ( 'image/*' );

      if($resimkucultme == 1){
        if($dikeylimit <= $height & $yataylimit <= $width){
        $image->image_resize = true;
        $image->image_x = $new_width;
        $image->image_y = $new_height;
      }
      }
      $image->Process( '../'.$yol );
      $isim = $image->file_dst_name;
      if ( $image->processed ) {
          $resim2 = $yol.SEOdosya($image->file_dst_name);
          rename("../$yol/$isim","../$yol/".SEOdosya($isim)."");
      } else {
          print 'Bir sorun oluştu: ' . $image->error; 
      }
  }
}



$image = new Upload( $_FILES[ 'resim3' ] );
if ( $image->uploaded ) {

  if($maxcozunurluluk > 0) {
      if($image->image_src_x > $maxcozunurluluk) { ?>
      <script> alert('Resim yüksekliği veya genişliği izin verilenden yüksek. Lütfen daha küçük bir resim yükleyin.');</script>
     <?php 
  $resim3 = $resimcek['resim3'];   
  } else { goto resim4; }
  }else {
      resim4:
      $size = getimagesize($_FILES['resim3']['tmp_name']);
      $width = $size[0];
      $height = $size[1];

      $new_width = $width * $kucultmeorani / 100;
      $new_height = $height * $kucultmeorani / 100;

      $yol = 'images/'.$klasorcek['yetki'].'/';
      $image->image_convert = 'webp';
      $image->file_max_size = $resimboyut * 1000000;
      $image->webp_quality = $kalite;
      $image->allowed = array ( 'image/*' );

      if($resimkucultme == 1){
        if($dikeylimit <= $height & $yataylimit <= $width){
        $image->image_resize = true;
        $image->image_x = $new_width;
        $image->image_y = $new_height;
      }
      }
      $image->Process( '../'.$yol );
      $isim = $image->file_dst_name;
      if ( $image->processed ) {
          $resim3 = $yol.SEOdosya($image->file_dst_name);
          rename("../$yol/$isim","../$yol/".SEOdosya($isim)."");
      } else {
          print 'Bir sorun oluştu: ' . $image->error; 
      }
  }
}

$image = new Upload( $_FILES['video'] );
if ( $image->uploaded ) {

      $yol = 'images/'.$klasorcek['yetki'].'/';
      $image->file_max_size = $resimboyut * 1000000;
      $image->allowed = array ( 'video/*' );
      $image->Process( '../'.$yol );
      $isim = $image->file_dst_name;
      if ( $image->processed ) {
          $video = $yol.SEOdosya($image->file_dst_name);
          rename("../$yol/$isim","../$yol/".SEOdosya($isim)."");
      } else {
          print 'Bir sorun oluştu: ' . $image->error; 
      }
 
}


$image = new Upload( $_FILES['dosya'] );
if ( $image->uploaded ) {

      $yol = 'dosya/'.$klasorcek['yetki'].'/';
      $image->file_max_size = $resimboyut * 1000000;
      $image->allowed = array ( 'application/*' ,'image/*' ,'video/*' );
      $image->Process( '../'.$yol );
      $isim = $image->file_dst_name;
      if ( $image->processed ) {
          $dosya = $yol.SEOdosya($image->file_dst_name);
          rename("../$yol/$isim","../$yol/".SEOdosya($isim)."");
      } else {
          print 'Bir sorun oluştu: ' . $image->error; 
      }
 
}



$image = new Upload( $_FILES['dosya2'] );
if ( $image->uploaded ) {

      $yol = 'dosya/'.$klasorcek['yetki'].'/';
      $image->file_max_size = $resimboyut * 1000000;
      $image->allowed = array ( 'application/*' ,'image/*' ,'video/*' );
      $image->Process( '../'.$yol );
      $isim = $image->file_dst_name;
      if ( $image->processed ) {
          $dosya2 = $yol.SEOdosya($image->file_dst_name);
          rename("../$yol/$isim","../$yol/".SEOdosya($isim)."");
      } else {
          print 'Bir sorun oluştu: ' . $image->error; 
      }
 
}



?>