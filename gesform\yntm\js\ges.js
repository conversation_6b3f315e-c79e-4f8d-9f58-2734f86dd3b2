﻿function duzenle(){ 
$('#duzenleniyor').html('<center><img src="resimler/loadingAnimation.gif"></center>');
$.ajax({
	type: "POST",
	url: "duzenle2.php",
	data: $('form').serialize(),
	success: function(msg){
	
		$('#duzenleniyor').html("<font color='yellow'>Kayıt Düzenlendi!</font>");
		var path = window.location.href;	
		setTimeout(function () {
		window.location.href = path; //will redirect to your blog page (an ex: blog.html)
		}, 1000); //will call the function after 1 secs.


   }
});
}


function duzenle_profil(){ 
$('#profil_duzenleniyor').html('<center><img src="resimler/loadingAnimation.gif"></center>');
$.ajax({
	type: "POST",
	url: "edit_profile_2.php",
	data: $('form').serialize(),
	success: function(msg){
	
		$('#profil_duzenleniyor').html("<font color='yellow'>Profil Düzenlendi!</font>");
		var path = window.location.href;	
		setTimeout(function () {
		window.location.href = path; //will redirect to your blog page (an ex: blog.html)
		}, 1000); //will call the function after 1 secs.


   }
});
}




function sil_1(no){ 

var sc = $("input[@type=hidden]").serialize();

$('#siliniyor').html('<center><img src="resimler/loadingAnimation.gif"></center>');
$.ajax({
	type: "POST",
	url: "sil_2.php",
	data: $('form').serialize(),
	success: function(msg){

		$('#siliniyor').html("<b><font color='orange'>Bu kayıt silindi!</font></b>");
		setTimeout("window.location = 'index.php';",500);
   }
});
}





function arama_goster() {
$("#div_arama").slideToggle("slow");
}

function ekle(){ 
$('#ekleniyor').html('<center><img src="resimler/loadingAnimation.gif"></center>');
$.ajax({
	type: "POST",
	url: "ekle2.php",
	data: $('form').serialize(),
	success: function(msg){
	if (msg=="ok")
		{
		$('#ekleniyor').html("<font color='yellow'>Kayıt Eklendi!</font>");
	var path = window.location.href;	
	setTimeout(function () {
       window.location.href = path; //will redirect to your blog page (an ex: blog.html)
    }, 1000); //will call the function after 1 secs.
		}
	else
		{$('#ekleniyor').html( msg );}
   }
});
}


function kopya_ekle(){ 
$('#kopya_ekleniyor').html('<center><img src="resimler/loadingAnimation.gif"></center>');
$.ajax({
	type: "POST",
	url: "ekle2.php",
	data: $('form').serialize(),
	success: function(msg){
	if (msg=="ok")
		{
		$('#kopya_ekleniyor').html("<font color='yellow'>Kopya Kayıt Eklendi!</font>");
	var path = window.location.href;	
	setTimeout(function () {
       window.location.href = path; //will redirect to your blog page (an ex: blog.html)
    }, 1000); //will call the function after 1 secs.
		}
	else
		{$('#kopya_ekleniyor').html( msg );}
   }
});
}





function share(no){ 
$('#paylasiliyor').html('<center><img src="resimler/loadingAnimation.gif"></center>');
$.ajax({
	type: "POST",
	url: "share2.php",
	data: $('form').serialize(),
	success: function(msg){
	if (msg=="ok")
		{
		$('#paylasiliyor').html("<font color='yellow'>Kullanıcıyla paylaşım yapıldı!</font>");
	var path = window.location.href;	
	setTimeout(function () {
       window.location.href = path; //will redirect to your blog page (an ex: blog.html)
    }, 1000); //will call the function after 1 secs.
		}
	else
		{$('#paylasiliyor').html( msg );}
   }
});
}



function cat_share(no){ 
$('#paylasiliyor').html('<center><img src="resimler/loadingAnimation.gif"></center>');
$.ajax({
	type: "POST",
	url: "cat_share2.php",
	data: $('form').serialize(),
	success: function(msg){
	if (msg=="ok")
		{
		$('#paylasiliyor').html("<font color='yellow'>Kullanıcıyla paylaşım yapıldı!</font>");
	var path = window.location.href;	
	setTimeout(function () {
       window.location.href = path; //will redirect to your blog page (an ex: blog.html)
    }, 1000); //will call the function after 1 secs.
		}
	else
		{$('#paylasiliyor').html( msg );}
   }
});
}





function silx(no)
{
	if( confirm('Bu Kaydı silmek istediğinizden emin misiniz?') ) {
          $.ajax({
            type: "POST",
            url: "sil.php",
            data: "tel=" + encodeURIComponent(no),
            success: function(cevap){
            	if( cevap=="ok" )
				{
					$('tr#'+no).hide();
				}
				else 
				{
					alert("Silme işleminde problem çıktı!"); 
				}
			}
		});
	}
}

function sil( no )
{
	if( confirm('Bu Kaydı silmek istediğinizden emin misiniz?A') ) {
          $.ajax({
            type: "POST",
            url: "sil.php",
            data: "tel=" + encodeURIComponent(no),
            success: function(msg)
			{
            	if( msg === 'ok' )
				{
					$('tr#'+no).hide();
				}
				else 
				{
					alert("Silme işleminde problem çıktı!A"); 
				}
			}
		});
	}
}




function copyToClipboard(element) {
  var $temp = $("<input>");
  $("body").append($temp);
  $temp.val($(element).text()).select();
  document.execCommand("copy");
  $temp.remove();
}




function copy(no)
{
	if( confirm(encodeURIComponent(no) + 'Bu kaydın bir kopyasını oluşturmak istediğinizden emin misiniz?') ) {
          $.ajax({
            type: "POST",
            url: "copy.php",
            data: "tel=" + encodeURIComponent(no),
            success: function(cevap)
			{
            	if( cevap === 'ok' )
				{
					$('tr#'+no).hide();
				}
				else 
				{
					alert("Kopyalama işleminde problem çıktı!"); 
				}
			}
		});
	}
}



function ssh(){ 
$('#komut_isleniyor').html('<center><img src="resimler/loadingAnimation.gif"></center>');
$.ajax({
	type: "POST",
	url: "telnet.php",
	data: $('form').serialize(),
	success: function(msg){
	if (msg=="ok")
		{
		$('#komut_isleniyor').html("<font color='black'>Komut Gönderildi!</font>");
		}
	else
		{$('#komut_isleniyor').html( msg );}
   }
});
}


function ssh_end()
{
	if( confirm('Çıkış yapmak istediğinizden emin misiniz?') ) {
          $.ajax({
            type: "POST",
            url: "telnet.php?logout=true",
            data: $('form').serialize(),
            success: function(cevap)
			{
            	if( cevap === 'ok' )
				{
					alert("Çıkış başarılı!"); 
				}
				else 
				{
					alert("Çıkış işleminde problem çıktı!"); 
				}
			}
		});
	}
}




function rdp(address){
 try{
  var rdpexe = 'C:\\WINDOWS\\system32\\mstsc.exe';
  var ws = new ActiveXObject('WScript.Shell');
  ws.Exec(rdpexe + " /v:" + address);
 }
 catch(e){
  alert("This link will try to launch RDP session from web browser\n"+
     "1. Please use Internet Explorer 7.0+\n"+ 
     "2. Go to Tools > Internet options > Security > Trusted Sites and click \"sites\" button.\n"+
     "3. Add *.your_domain to trusted site\n"+
     "4. Click Custom Level and find the section \"ActiveX controls and plug-ins\"\n"+
     "5. Select Enable for \"Initializing and Script ActiveX controls not marked as safe\"");
 }
}