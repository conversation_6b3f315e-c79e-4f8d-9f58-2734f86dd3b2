<?php
session_start();

include "db/baglanti.php"; // Veritabanı bağlantısını başlatıyoruz.

// Geçerli dil seçeneği kontrolü
$gecerliDil = $_SESSION["dil"] ?? "tr";
$mevcutdilseo = "seo_" . $_SESSION["dil"] ?? "seo_tr";

if (isset($_GET["dil"]) && DilGecerliMi($_GET["dil"])) {
    $gecerliDil = strip_tags($_GET["dil"]);
    $_SESSION["dil"] = $gecerliDil;

    $geldigi_sayfa = $_SERVER['HTTP_REFERER']; // Ziyaretçinin dil butonuna tıkladığı sayfayı buluyoruz
    $dizi = explode("/", $geldigi_sayfa);
    $sayfa = $dizi[3];
    $kontrol = $dizi[2];

    // Yönlendirmeleri yapmak için dil dosyalarını ve SEO linklerini kullanabilirsiniz
    $dilDosyasi = "dil/$gecerliDil.php";
    if (file_exists($dilDosyasi)) {
        $dilVerileri = require($dilDosyasi); // Dil dosyasındaki verileri bir değişkene atayın

        Yonlendir($kontrol, $sayfa);
    }
} else {
    header("Location: index.php"); // Geçersiz dil seçeneği durumunda ana sayfaya yönlendiriyoruz
}



function DilGecerliMi($dil)
{
    global $db;
    $sql = "SELECT DISTINCT kisaltma FROM dil";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $gecerli_diller = $stmt->fetchAll(PDO::FETCH_COLUMN);
    return in_array($dil, $gecerli_diller); // Geçerli dilleri kontrol ediyoruz
}



function Yonlendir($kontrol, $sayfa)
{
    global $gecerliDil, $db, $d, $mevcutdilseo;
    extract($d);
    $dilSeoSutunu = "seo_$gecerliDil";


    $dizi_iletisim          = ["iletisim", "contact", "contact-ru", "contact-ar"];
    $dizi_ara               = ["ara", "search"];
    $dizi_haberdetay        = ["haber", "news-detail", "news-detail-ru", "news-detail-ar"];
    $dizi_haberler          = ["haberler", "news", "news-ru", "news-ar"];
    $dizi_projedetay       = ["proje-detay", "project-detail"];
    $dizi_duyurular         = ["duyurular", "announcements"];
    $dizi_altsayfa          = ["bilgi", "info"];
    $dizi_bayi              = ["bayi-talep-formu", "dealer-request-form"];


    if (in_array($kontrol, $dizi_iletisim)) {
        $url = 'https://' . $_SERVER['SERVER_NAME'] . "/$iletisimlink";
        header("Location: $url");
    } elseif (in_array($kontrol, $dizi_ara)) {
        $url = 'https://' . $_SERVER['SERVER_NAME'] . "/$aramalink";
        header("Location: $url");
    } elseif (in_array($kontrol, $dizi_haberler)) {
        $url = 'https://' . $_SERVER['SERVER_NAME'] . "/$haberlink";
        header("Location: $url");
    } elseif (in_array($kontrol, $dizi_duyurular)) {
        $url = 'https://' . $_SERVER['SERVER_NAME'] . "/$duyurulink";
        header("Location: $url");
    } elseif (in_array($kontrol, $dizi_bayi)) {
        $url = 'https://' . $_SERVER['SERVER_NAME'] . "/$bayiliklink";
        header("Location: $url");        
    } elseif (empty($sayfa)) {
        $_SESSION["dil"] = $gecerliDil;
        header("Location: /");
    } else {
        if (in_array($kontrol, $dizi_haberdetay)) {
            $dilkontrol = $db->prepare("SELECT $dilSeoSutunu FROM sayfalar WHERE $mevcutdilseo = :sayfa AND sayfatip = 6 ORDER BY id LIMIT 1");
            $dilkontrol->bindParam(':sayfa', $sayfa, PDO::PARAM_STR);
            $dilkontrol->execute();
            $yenisayfa = $dilkontrol->fetchColumn();
            
             if ($yenisayfa) {
                 $url = 'https://' . $_SERVER['SERVER_NAME'] . "/$haberdetaylink/$yenisayfa";
                 header("Location: $url");
             } else {
                 $url = 'https://' . $_SERVER['SERVER_NAME'] . "/";
                 header("Location: $url");
             }
        } else if (in_array($kontrol, $dizi_projedetay)) {
            $dilkontrol = $db->prepare("SELECT $dilSeoSutunu FROM sayfalar WHERE $mevcutdilseo = :sayfa AND sayfatip = 8 ORDER BY id LIMIT 1");
            $dilkontrol->bindParam(':sayfa', $sayfa, PDO::PARAM_STR);
            $dilkontrol->execute();
            $yenisayfa = $dilkontrol->fetchColumn();
            
             if ($yenisayfa) {
                 $url = 'https://' . $_SERVER['SERVER_NAME'] . "/$projedetaylink/$yenisayfa";
                 header("Location: $url");
             } else {
                 $url = 'https://' . $_SERVER['SERVER_NAME'] . "/";
                 header("Location: $url");
             }
        } else if (in_array($kontrol, $dizi_altsayfa)) {
            $dilkontrol = $db->prepare("SELECT $dilSeoSutunu FROM sayfalar WHERE $mevcutdilseo = :sayfa AND sayfatip = 2 ORDER BY id LIMIT 1");
            $dilkontrol->bindParam(':sayfa', $sayfa, PDO::PARAM_STR);
            $dilkontrol->execute();
            $yenisayfa = $dilkontrol->fetchColumn();
            
             if ($yenisayfa) {
                 $url = 'https://' . $_SERVER['SERVER_NAME'] . "/$altsayfalink/$yenisayfa";
                 header("Location: $url");
             } else {
                 $url = 'https://' . $_SERVER['SERVER_NAME'] . "/";
                 header("Location: $url");
             }
        } else {
            $dilkontrol = $db->prepare("SELECT $dilSeoSutunu FROM sayfalar WHERE $mevcutdilseo = :sayfa ORDER BY id LIMIT 1");
            $dilkontrol->bindParam(':sayfa', $sayfa, PDO::PARAM_STR);
            $dilkontrol->execute();
            $yenisayfa = $dilkontrol->fetchColumn();


            if ($yenisayfa) {
                $url = 'https://' . $_SERVER['SERVER_NAME'] . "/$yenisayfa";
                header("Location: $url");
            } else {
                $url = 'https://' . $_SERVER['SERVER_NAME'] . "/";
                header("Location: $url");
            }
        }
    }
}
